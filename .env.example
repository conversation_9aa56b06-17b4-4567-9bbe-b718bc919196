# API Keys para Exchanges - FASE 4: INTEGRAÇÃO COM APIS REAIS

# Gate.io API Configuration
VITE_GATEIO_API_KEY=716e60725e85fb3f9c4a20a59ef3cd75
VITE_GATEIO_SECRET_KEY=31b17edb8827c53fbd67afe1c5c63b3f08355ea0138db128c19ce32467388d22
VITE_GATEIO_API_URL=https://api.gateio.ws

# MEXC API Configuration  
VITE_MEXC_API_KEY=mx0vglMEFTYl40bbVl
VITE_MEXC_SECRET_KEY=888cddcd671a4b69a44c40145b4b7b76
VITE_MEXC_API_URL=https://api.mexc.com

# Bitget API Configuration
VITE_BITGET_API_KEY=bg_a8c4e2f1d3b5a7c9e1f3d5b7a9c1e3f5
VITE_BITGET_SECRET_KEY=2F8A9B3C7E1D4F6A8B2E5C9F1A4D7B0E3C6F9A2D5B8E1C4F7A0D3B6E9C2F5A8B
VITE_BITGET_PASSPHRASE=59288686
VITE_BITGET_API_URL=https://api.bitget.com

# System Configuration
VITE_ENABLE_REAL_APIS=true
VITE_CACHE_DURATION=5000
VITE_MIN_SPREAD_PERCENTAGE=0.05
VITE_MAX_OPPORTUNITIES=2000
VITE_REQUEST_TIMEOUT=10000

# Development Configuration
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info