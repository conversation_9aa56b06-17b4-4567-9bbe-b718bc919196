# OTIMIZAÇÃO: Configuração para Ultra Baixa Latência
# Copie este arquivo para .env para ativar todas as otimizações

# Configurações do Servidor
PORT=5001
NODE_ENV=production

# URLs e CORS
CORS_ORIGIN=http://localhost:5002
WS_PORT=5001

# APIs das Exchanges (ATIVAR PARA PRODUÇÃO)
ENABLE_REAL_APIS=true

# OTIMIZAÇÃO: Feature Flags - Ativar todas as otimizações
ENABLE_PARALLEL_PROCESSING=true
ENABLE_MULTI_LAYER_CACHE=true
ENABLE_WEBSOCKET_STREAMING=true
ENABLE_BATCH_PROCESSING=true
ENABLE_CONNECTION_POOLING=true
ENABLE_VIRTUALIZED_UI=true
ENABLE_PERFORMANCE_MONITORING=true

# OTIMIZAÇÃO: Flags de Fallback (manter desabilitadas em produção)
ENABLE_FALLBACK=false
EMERGENCY_MODE=false

# Rate Limiting Otimizado
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=60000

# Cache Configuration
CACHE_TTL_HOT=100
CACHE_TTL_WARM=1000
CACHE_TTL_COLD=30000

# Performance Thresholds
LATENCY_P99_THRESHOLD=1000
LATENCY_P95_THRESHOLD=800
LATENCY_P90_THRESHOLD=600
ERROR_RATE_THRESHOLD=5
MEMORY_USAGE_THRESHOLD=80
CPU_USAGE_THRESHOLD=80
THROUGHPUT_MIN_THRESHOLD=250

# WebSocket Configuration
WS_HEARTBEAT_INTERVAL=30000
WS_MAX_CONNECTIONS=1000
WS_MESSAGE_BUFFER_SIZE=1000

# Connection Pooling
HTTP_KEEP_ALIVE=true
HTTP_MAX_SOCKETS=50
HTTP_MAX_FREE_SOCKETS=10
HTTP_TIMEOUT=5000

# Exchange API Timeouts (otimizados)
GATEIO_TIMEOUT=4000
MEXC_TIMEOUT=4000
BITGET_TIMEOUT=4000

# Batch Processing
BATCH_SIZE=50
BATCH_PARALLEL_LIMIT=10
BATCH_TIMEOUT=2000

# Monitoring
METRICS_COLLECTION_INTERVAL=10000
ALERT_CHECK_INTERVAL=30000
CLEANUP_INTERVAL=300000

# Logging
LOG_LEVEL=info
LOG_PERFORMANCE=true
LOG_CACHE_HITS=false
LOG_WEBSOCKET_EVENTS=false

# Database/Storage (se necessário)
# DATABASE_URL=
# REDIS_URL=

# Security
# JWT_SECRET=
# API_KEY_ENCRYPTION=

# Alerting (configurar para produção)
# SLACK_WEBHOOK_URL=
# EMAIL_SMTP_HOST=
# EMAIL_SMTP_PORT=
# EMAIL_SMTP_USER=
# EMAIL_SMTP_PASS=

# Backup and Recovery
AUTO_BACKUP=true
BACKUP_INTERVAL=3600000
MAX_BACKUP_FILES=24

# Development/Testing
ENABLE_DEBUG_ENDPOINTS=false
ENABLE_MOCK_DATA=false
ENABLE_PERFORMANCE_TESTS=true
