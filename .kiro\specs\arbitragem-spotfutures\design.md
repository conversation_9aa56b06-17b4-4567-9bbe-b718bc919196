# Design Document - Sistema Completo de Arbitragem de Criptomoedas

## Overview

O sistema completo de arbitragem de criptomoedas é uma aplicação web moderna e robusta que integra backend Node.js com frontend React/TypeScript, especializada em detectar oportunidades de arbitragem cross-exchange em tempo real. A arquitetura é projetada para ser modular, escalável e de alta performance, processando dados de TODAS as criptomoedas disponíveis nas exchanges Gate.io, MEXC e Bitget.

O sistema identifica duas modalidades principais de arbitragem:
1. **Spot vs Futuros Cross-Exchange**: Comprar spot na Exchange A e shortar futuros na Exchange B
2. **Futuros vs Futuros Cross-Exchange**: Long futuros na Exchange A e short futuros na Exchange B

O design prioriza a separação de responsabilidades, com serviços especializados para descoberta automática de símbolos, cálculos precisos de arbitragem cross-exchange, interface moderna e intuitiva, e sistema completo de auditoria e monitoramento.

## Architecture

### High-Level System Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    SISTEMA COMPLETO UNIFICADO                  │
├─────────────────────────────────────────────────────────────────┤
│  Frontend (React/TS)     │  Backend (Node.js)  │  External APIs │
│  ┌─────────────────────┐ │ ┌─────────────────┐ │ ┌─────────────┐ │
│  │ • Modern UI         │◄┼►│ • Data Collector│◄┼►│ • Gate.io   │ │
│  │ • Real-time Updates │ │ │ • API Endpoints │ │ │ • MEXC      │ │
│  │ • Charts & Graphs   │ │ │ • WebSocket     │ │ │ • Bitget    │ │
│  │ • Position Manager  │ │ │ • Cache Layer   │ │ │             │ │
│  │ • Alert System     │ │ │ • Error Handler │ │ │             │ │
│  └─────────────────────┘ │ └─────────────────┘ │ └─────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                    AUDITORIA E MONITORAMENTO                   │
│  • System Health Monitor  • Performance Metrics  • Quality Assurance │
└─────────────────────────────────────────────────────────────────┘
```

### Detailed Frontend Architecture

```
src/
├── components/
│   ├── ui/                     # Sistema de Design Base
│   │   ├── Button.jsx         # Componente Button avançado
│   │   ├── Card.jsx           # Cards com variantes de arbitragem
│   │   ├── Badge.jsx          # Badges com status de exchanges
│   │   ├── Input.jsx          # Inputs com validação
│   │   ├── Select.jsx         # Selects customizados
│   │   ├── Tabs.jsx           # Sistema de tabs
│   │   ├── Switch.jsx         # Switches para configurações
│   │   ├── Slider.jsx         # Sliders para ranges
│   │   └── ThemeProvider.jsx  # Provider de temas
│   ├── layout/                # Sistema de Layout
│   │   ├── Layout.jsx         # Layout principal responsivo
│   │   ├── Header.jsx         # Header com busca e controles
│   │   └── Sidebar.jsx        # Sidebar colapsável
│   ├── dashboard/             # Dashboard Principal
│   │   ├── DashboardMain.jsx  # Dashboard orquestrador
│   │   ├── StatsCards.jsx     # Cards de estatísticas
│   │   └── DashboardTabs.jsx  # Sistema de tabs do dashboard
│   ├── opportunities/         # Sistema de Oportunidades
│   │   ├── OpportunityTable.jsx # Tabela principal
│   │   ├── OpportunityCard.jsx  # Cards individuais
│   │   └── OpportunityActions.jsx # Ações rápidas
│   ├── filters/               # Sistema de Filtros Avançados
│   │   ├── BasicFilters.jsx   # Filtros básicos
│   │   ├── AdvancedFilters.jsx # Filtros avançados colapsáveis
│   │   └── FilterBadges.jsx   # Badges de filtros ativos
│   ├── charts/                # Sistema de Gráficos
│   │   ├── ChartModal.jsx     # Modal de gráficos
│   │   ├── SpreadChart.jsx    # Gráfico de spread
│   │   └── VolumeChart.jsx    # Gráfico de volume
│   ├── positions/             # Sistema de Posições
│   │   ├── PositionManager.jsx # Gerenciador principal
│   │   ├── PositionAlerts.jsx  # Sistema de alertas
│   │   └── PnLCalculator.jsx   # Calculadora de P&L
│   ├── realtime/              # Sistema Tempo Real
│   │   ├── RealTimeUpdates.jsx # Atualizações visuais
│   │   ├── ConnectionStatus.jsx # Status de conexão
│   │   └── WebSocketStatus.jsx  # Status WebSocket
│   └── notifications/         # Sistema de Notificações
│       ├── NotificationSystem.jsx # Sistema principal
│       ├── NotificationToast.jsx  # Toasts
│       └── NotificationCenter.jsx # Central de notificações
├── hooks/                     # Hooks Customizados
│   ├── useArbitrageData.ts   # Hook principal de dados
│   ├── useChartData.ts       # Hook para gráficos
│   ├── useFilters.ts         # Hook para filtros
│   ├── useWebSocket.ts       # Hook para WebSocket
│   ├── useNotifications.ts   # Hook para notificações
│   └── useTheme.ts           # Hook para temas
├── services/                  # Serviços Frontend
│   ├── api.ts                # Cliente API
│   ├── websocket.ts          # Cliente WebSocket
│   └── notifications.ts      # Serviço de notificações
├── utils/                     # Utilitários
│   ├── formatters.ts         # Formatação de dados
│   ├── calculations.ts       # Cálculos auxiliares
│   ├── constants.ts          # Constantes
│   └── performance.ts        # Utilitários de performance
└── styles/                    # Estilos
    ├── globals.css           # Estilos globais
    ├── components.css        # Estilos de componentes
    └── themes.css            # Variáveis de tema
```

### Detailed Backend Architecture

```
src/
├── services/                  # Serviços Core
│   ├── ExchangeAPI.ts        # Integração com exchanges
│   ├── DataCollector.ts      # Orquestrador de dados
│   ├── SpreadCalculator.ts   # Cálculos de arbitragem
│   ├── AlertSystem.ts        # Sistema de alertas
│   ├── DataValidator.ts      # Validação de dados
│   ├── DataQualityMonitor.ts # Monitor de qualidade
│   ├── HealthMonitor.ts      # Monitor de saúde
│   ├── PerformanceMonitor.ts # Monitor de performance
│   └── Logger.ts             # Sistema de logs
├── config/                    # Configurações
│   ├── arbitrage.ts          # Configurações de arbitragem
│   ├── exchanges.ts          # Configurações de exchanges
│   └── production.js         # Configurações de produção
├── types/                     # Definições TypeScript
│   └── arbitrage.ts          # Tipos principais
├── utils/                     # Utilitários Backend
│   ├── cache.ts              # Sistema de cache
│   ├── retry.ts              # Sistema de retry
│   └── validation.ts         # Validações
└── __tests__/                 # Testes
    ├── integration.test.ts   # Testes de integração
    ├── ExchangeAPI.test.ts   # Testes de API
    └── DataCollector.test.ts # Testes de coleta
```

## Core Data Models

### Enhanced ArbitrageOpportunity

```typescript
interface ArbitrageOpportunity {
  id: string
  symbol: string              // BTC/USDT, ETH/USDT
  baseAsset: string          // BTC, ETH
  quoteAsset: string         // USDT
  
  // Dados do Spot (Exchange A)
  spotExchange: Exchange     // gateio, mexc, bitget
  spotPrice: number
  spotVolume: number
  spotBid: number
  spotAsk: number
  
  // Dados do Futuros (Exchange B - diferente do spot)
  futuresExchange: Exchange  // Diferente do spotExchange
  futuresPrice: number
  futuresVolume: number
  futuresBid: number
  futuresAsk: number
  contractType: string       // PERP, quarterly, monthly
  
  // Cálculos de Spread Cross-Exchange
  spreadAbsolute: number     // Diferença de preço absoluta
  spreadPercentage: number   // Percentual de diferença
  spreadDirection: 'positive' | 'negative'
  netSpread: number          // Após taxas e custos
  profitability: 'high' | 'medium' | 'low' // Classificação
  
  // Estratégia Cross-Exchange
  type: 'spot-futures-cross' | 'futures-futures-cross'
  strategy: {
    action1: string          // "Buy BTC spot on MEXC"
    action2: string          // "Short BTC futures on Gate.io"
    exitCondition: string    // "When prices converge"
  }
  
  // URLs para Redirecionamento
  urls: {
    spot: string             // URL da exchange spot
    futures: string          // URL da exchange futures
  }
  
  // Métricas Históricas (4h window)
  openings: number          // Vezes que spread abriu
  closings: number          // Vezes que spread fechou
  inversions: number        // Vezes que direção inverteu
  
  // Análise de Risco
  riskLevel: 'low' | 'medium' | 'high'
  liquidityScore: number    // Score de liquidez
  volatilityScore: number   // Score de volatilidade
  
  // Metadados
  lastUpdate: Date
  dataAge: number           // Idade em milliseconds
  timestamp: Date
}
```

### Enhanced Position Model

```typescript
interface Position {
  id: string
  symbol: string
  
  // Exchanges Cross-Exchange
  spotExchange: Exchange    // Exchange para posição spot
  futuresExchange: Exchange // Exchange para posição futures (diferente)
  
  // Dados de Entrada
  entrySpotPrice: number
  entryFuturesPrice: number
  entrySpread: number
  entryTime: Date
  
  // Dados Atuais Cross-Exchange
  currentSpotPrice: number
  currentFuturesPrice: number
  currentSpread: number
  
  // Cálculos de P&L Cross-Exchange
  unrealizedPnL: number
  unrealizedPnLPercentage: number
  realizedPnL: number
  totalPnL: number
  
  // Sistema de Alertas
  status: 'open' | 'alert' | 'closed'
  alertThreshold: number
  alertEnabled: boolean
  
  // URLs para Fechamento
  urls: {
    spotClose: string       // URL para fechar posição spot
    futuresClose: string    // URL para fechar posição futures
  }
  
  // Metadados
  notes: string
  createdAt: Date
  updatedAt: Date
}
```

### Dashboard Metrics Model

```typescript
interface DashboardMetrics {
  // Métricas Gerais
  totalOpportunities: number
  averageSpread: number
  totalVolume: number
  
  // Distribuição por Tipo Cross-Exchange
  spotFuturesCount: number      // Spot vs Futures cross-exchange
  futuresFuturesCount: number   // Futures vs Futures cross-exchange
  
  // Distribuição por Rentabilidade
  highProfitCount: number       // > 1% spread
  mediumProfitCount: number     // 0.5% - 1% spread
  lowProfitCount: number        // 0.05% - 0.5% spread
  
  // Top Exchange Pairs Cross-Exchange
  topExchangePairs: Array<{
    spotExchange: Exchange
    futuresExchange: Exchange
    count: number
    avgSpread: number
    totalVolume: number
  }>
  
  // Status das Exchanges
  exchangeStatus: {
    gateio: ExchangeStatus
    mexc: ExchangeStatus
    bitget: ExchangeStatus
  }
  
  // Métricas de Sistema
  systemMetrics: {
    updateFrequency: number     // Updates por minuto
    errorRate: number          // Taxa de erro
    uptime: number            // Uptime percentual
    responseTime: number      // Tempo médio de resposta
    dataFreshness: number     // Idade média dos dados
  }
  
  // Métricas de Performance
  performanceMetrics: {
    opportunityDetectionTime: number
    dataProcessingTime: number
    uiRenderTime: number
    cacheHitRate: number
  }
}
```

## Service Layer Design

### Enhanced DataCollector Service

```typescript
class DataCollector {
  private exchanges = ['gateio', 'mexc', 'bitget']
  private allSymbols = new Set<string>() // 6,800+ símbolos
  private cache = new Map<string, CacheEntry>()
  
  // Método Principal - Coleta Completa Cross-Exchange
  async collectAllData(): Promise<ArbitrageOpportunity[]> {
    console.log('🔍 Iniciando coleta completa cross-exchange...')
    
    // 1. Descobrir TODOS os símbolos (6,800+)
    await this.discoverAllSymbols()
    
    // 2. Coletar dados paralelos de todas as exchanges
    const [allSpotData, allFuturesData] = await Promise.allSettled([
      this.collectAllSpotData(),
      this.collectAllFuturesData()
    ])
    
    // 3. Detectar oportunidades cross-exchange
    const opportunities = this.findAllCrossExchangeOpportunities(
      allSpotData.value || [],
      allFuturesData.value || []
    )
    
    console.log(`🎯 Detectadas ${opportunities.length} oportunidades cross-exchange`)
    
    return this.rankOpportunities(opportunities)
  }
  
  // Detecção de Oportunidades Cross-Exchange
  private findAllCrossExchangeOpportunities(
    allSpotData: ExchangeData[],
    allFuturesData: ExchangeData[]
  ): ArbitrageOpportunity[] {
    const opportunities: ArbitrageOpportunity[] = []
    
    // Agrupar por símbolo
    const spotBySymbol = this.groupDataBySymbol(allSpotData)
    const futuresBySymbol = this.groupDataBySymbol(allFuturesData)
    
    // Para cada símbolo, encontrar combinações cross-exchange
    for (const symbol of this.allSymbols) {
      const spotOptions = spotBySymbol.get(symbol) || []
      const futuresOptions = futuresBySymbol.get(symbol) || []
      
      // Spot vs Futuros Cross-Exchange
      opportunities.push(...this.findSpotFuturesCrossExchange(symbol, spotOptions, futuresOptions))
      
      // Futuros vs Futuros Cross-Exchange
      opportunities.push(...this.findFuturesFuturesCrossExchange(symbol, futuresOptions))
    }
    
    return opportunities.filter(opp => this.isValidCrossExchangeOpportunity(opp))
  }
  
  // Validação Específica Cross-Exchange
  private isValidCrossExchangeOpportunity(opp: ArbitrageOpportunity): boolean {
    return (
      opp.spotExchange !== opp.futuresExchange && // Deve ser cross-exchange
      Math.abs(opp.spreadPercentage) >= 0.05 &&   // Spread mínimo 0.05%
      opp.volume >= 1000 &&                       // Volume mínimo $1,000
      opp.dataAge <= 15000 &&                     // Dados frescos (<15s)
      opp.spotPrice > 0 &&                        // Preços válidos
      opp.futuresPrice > 0
    )
  }
}
```

### Enhanced ExchangeAPI Service

```typescript
class ExchangeAPI {
  private cache = new Map<string, CacheEntry>()
  private rateLimiters = new Map<Exchange, RateLimiter>()
  private connectionPool = new Map<Exchange, HttpAgent>()
  
  // Configuração de Rate Limits por Exchange
  private readonly RATE_LIMITS = {
    gateio: { requestsPerMinute: 100, requestsPerSecond: 10 },
    mexc: { requestsPerMinute: 120, requestsPerSecond: 20 },
    bitget: { requestsPerMinute: 200, requestsPerSecond: 30 }
  }
  
  // Coleta Completa com Autenticação HMAC
  async getAllCompleteData(): Promise<{
    allSpotData: ExchangeData[]
    allFuturesData: ExchangeData[]
    metadata: CollectionMetadata
  }> {
    const startTime = Date.now()
    
    // Coleta paralela com autenticação
    const [gateioData, mexcData, bitgetData] = await Promise.allSettled([
      this.getCompleteExchangeData('gateio'),
      this.getCompleteExchangeData('mexc'),
      this.getCompleteExchangeData('bitget')
    ])
    
    // Processar resultados
    const allSpotData = []
    const allFuturesData = []
    
    this.processExchangeResults(gateioData, allSpotData, allFuturesData)
    this.processExchangeResults(mexcData, allSpotData, allFuturesData)
    this.processExchangeResults(bitgetData, allSpotData, allFuturesData)
    
    return {
      allSpotData,
      allFuturesData,
      metadata: {
        totalPairs: allSpotData.length + allFuturesData.length,
        processingTime: Date.now() - startTime,
        exchangeStatus: this.getExchangeStatus(),
        timestamp: new Date()
      }
    }
  }
  
  // Autenticação HMAC por Exchange
  private async authenticatedRequest(
    exchange: Exchange,
    url: string,
    method: string = 'GET'
  ): Promise<any> {
    const timestamp = Date.now().toString()
    let signature: string
    let headers: Record<string, string>
    
    switch (exchange) {
      case 'gateio':
        signature = this.generateGateioSignature(method, url, '', timestamp)
        headers = {
          'KEY': process.env.GATEIO_API_KEY!,
          'Timestamp': timestamp,
          'SIGN': signature
        }
        break
        
      case 'mexc':
        signature = this.generateMexcSignature(timestamp)
        headers = {
          'X-MEXC-APIKEY': process.env.MEXC_API_KEY!,
          'X-MEXC-TIMESTAMP': timestamp,
          'X-MEXC-SIGNATURE': signature
        }
        break
        
      case 'bitget':
        signature = this.generateBitgetSignature(timestamp, method, url)
        headers = {
          'ACCESS-KEY': process.env.BITGET_API_KEY!,
          'ACCESS-TIMESTAMP': timestamp,
          'ACCESS-SIGN': signature,
          'ACCESS-PASSPHRASE': process.env.BITGET_PASSPHRASE!
        }
        break
    }
    
    return this.fetchWithRateLimit(exchange, url, { headers })
  }
}
```

### Enhanced SpreadCalculator Service

```typescript
class SpreadCalculator {
  // Cálculo Cross-Exchange Principal
  calculateCrossExchangeSpread(
    spotData: ExchangeData,
    futuresData: ExchangeData
  ): CrossExchangeSpreadResult {
    // Validar que são exchanges diferentes
    if (spotData.exchange === futuresData.exchange) {
      throw new Error('Cross-exchange arbitrage requires different exchanges')
    }
    
    // Cálculo básico
    const rawSpread = futuresData.price - spotData.price
    const spreadPercentage = (rawSpread / spotData.price) * 100
    
    // Ajustar por taxas das exchanges
    const spotFee = this.getExchangeFee(spotData.exchange)
    const futuresFee = this.getExchangeFee(futuresData.exchange)
    const totalFees = spotFee + futuresFee
    
    // Spread líquido após taxas
    const netSpread = rawSpread - (spotData.price * totalFees / 100)
    const netSpreadPercentage = (netSpread / spotData.price) * 100
    
    // Classificação de rentabilidade
    const profitability = this.classifyProfitability(Math.abs(netSpreadPercentage))
    
    // Estratégia recomendada
    const strategy = this.generateCrossExchangeStrategy(
      spotData, futuresData, spreadPercentage
    )
    
    return {
      rawSpread,
      spreadPercentage,
      netSpread,
      netSpreadPercentage,
      profitability,
      strategy,
      fees: { spot: spotFee, futures: futuresFee, total: totalFees },
      direction: rawSpread > 0 ? 'positive' : 'negative'
    }
  }
  
  // Geração de Estratégia Cross-Exchange
  private generateCrossExchangeStrategy(
    spotData: ExchangeData,
    futuresData: ExchangeData,
    spreadPercentage: number
  ): ArbitrageStrategy {
    const symbol = spotData.symbol
    const spotExchange = spotData.exchange.toUpperCase()
    const futuresExchange = futuresData.exchange.toUpperCase()
    
    if (spreadPercentage > 0) {
      // Futuros mais caro que spot
      return {
        action1: `Buy ${symbol} spot on ${spotExchange}`,
        action2: `Short ${symbol} futures on ${futuresExchange}`,
        exitCondition: 'When futures price converges to spot price',
        type: 'spot-futures-cross'
      }
    } else {
      // Spot mais caro que futuros
      return {
        action1: `Short ${symbol} spot on ${spotExchange}`,
        action2: `Buy ${symbol} futures on ${futuresExchange}`,
        exitCondition: 'When spot price converges to futures price',
        type: 'futures-spot-cross'
      }
    }
  }
  
  // Classificação de Rentabilidade
  private classifyProfitability(spreadPercentage: number): 'high' | 'medium' | 'low' {
    if (spreadPercentage >= 1.0) return 'high'    // >= 1%
    if (spreadPercentage >= 0.5) return 'medium'  // 0.5% - 1%
    return 'low'                                   // 0.05% - 0.5%
  }
}
```

## Frontend Component Design

### Modern UI System

```typescript
// Enhanced Card Component com Variantes de Arbitragem
interface CardProps {
  variant?: 'default' | 'opportunity' | 'high-profit' | 'medium-profit' | 'low-profit'
  crossExchange?: boolean
  pulse?: boolean
  spread?: number
  children: React.ReactNode
}

const Card: React.FC<CardProps> = ({ 
  variant = 'default', 
  crossExchange = false,
  pulse = false,
  spread,
  children 
}) => {
  const getCardStyles = () => {
    const baseStyles = "rounded-lg border p-4 transition-all duration-200"
    
    switch (variant) {
      case 'high-profit':
        return `${baseStyles} border-green-500 bg-green-50 dark:bg-green-950 ${pulse ? 'animate-pulse' : ''}`
      case 'medium-profit':
        return `${baseStyles} border-yellow-500 bg-yellow-50 dark:bg-yellow-950`
      case 'low-profit':
        return `${baseStyles} border-blue-500 bg-blue-50 dark:bg-blue-950`
      default:
        return `${baseStyles} border-gray-200 dark:border-gray-800`
    }
  }
  
  return (
    <div className={getCardStyles()}>
      {crossExchange && (
        <div className="mb-2 flex items-center gap-1">
          <Badge variant="outline" size="sm">Cross-Exchange</Badge>
          {spread && <Badge variant="secondary" size="sm">{spread.toFixed(2)}%</Badge>}
        </div>
      )}
      {children}
    </div>
  )
}
```

### Enhanced Dashboard System

```typescript
// Dashboard Principal com Métricas Cross-Exchange
const DashboardMain: React.FC = () => {
  const { data: opportunities, metadata } = useArbitrageData()
  const dashboardStats = useMemo(() => 
    calculateDashboardStats(opportunities), [opportunities]
  )
  
  return (
    <div className="space-y-6">
      {/* Stats Cards Cross-Exchange */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatsCard
          title="Total Oportunidades"
          value={dashboardStats.totalOpportunities}
          subtitle="Cross-Exchange"
          icon={TrendingUp}
          trend="up"
        />
        <StatsCard
          title="Spot-Futures"
          value={dashboardStats.spotFuturesCount}
          subtitle="Cross-Exchange"
          icon={ArrowRightLeft}
          color="success"
        />
        <StatsCard
          title="Futures-Futures"
          value={dashboardStats.futuresFuturesCount}
          subtitle="Cross-Exchange"
          icon={Repeat}
          color="primary"
        />
        <StatsCard
          title="Spread Médio"
          value={`${dashboardStats.averageSpread.toFixed(3)}%`}
          subtitle="Todas as exchanges"
          icon={Percent}
          color="warning"
        />
      </div>
      
      {/* Sistema de Tabs */}
      <Tabs defaultValue="opportunities" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="opportunities">Oportunidades</TabsTrigger>
          <TabsTrigger value="charts">Gráficos</TabsTrigger>
          <TabsTrigger value="exchanges">Exchanges</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoramento</TabsTrigger>
          <TabsTrigger value="positions">Posições</TabsTrigger>
        </TabsList>
        
        <TabsContent value="opportunities">
          <OpportunityTable opportunities={opportunities} />
        </TabsContent>
        
        <TabsContent value="charts">
          <RealTimeCharts data={opportunities} />
        </TabsContent>
        
        <TabsContent value="exchanges">
          <ExchangeStatusGrid status={metadata?.exchangeStatus} />
        </TabsContent>
        
        <TabsContent value="analytics">
          <AnalyticsTab stats={dashboardStats} />
        </TabsContent>
        
        <TabsContent value="monitoring">
          <MonitoringTab metrics={metadata?.systemMetrics} />
        </TabsContent>
        
        <TabsContent value="positions">
          <PositionManagerModern />
        </TabsContent>
      </Tabs>
    </div>
  )
}
```

## Real-Time System Design

### WebSocket Integration

```typescript
// Hook WebSocket para Atualizações em Tempo Real
export const useWebSocket = (url: string) => {
  const [socket, setSocket] = useState<WebSocket | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected')
  const [lastMessage, setLastMessage] = useState<any>(null)
  
  useEffect(() => {
    const ws = new WebSocket(url)
    
    ws.onopen = () => {
      setConnectionStatus('connected')
      console.log('🔗 WebSocket conectado')
    }
    
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      setLastMessage(data)
    }
    
    ws.onclose = () => {
      setConnectionStatus('disconnected')
      console.log('🔌 WebSocket desconectado - tentando reconectar...')
      
      // Reconexão automática após 5 segundos
      setTimeout(() => {
        setConnectionStatus('connecting')
        // Reconectar...
      }, 5000)
    }
    
    ws.onerror = (error) => {
      console.error('❌ Erro WebSocket:', error)
      setConnectionStatus('disconnected')
    }
    
    setSocket(ws)
    
    return () => {
      ws.close()
    }
  }, [url])
  
  return { socket, connectionStatus, lastMessage }
}

// Sistema de Atualizações Visuais em Tempo Real
const RealTimeUpdates: React.FC<{ opportunities: ArbitrageOpportunity[] }> = ({ 
  opportunities 
}) => {
  const { lastMessage, connectionStatus } = useWebSocket('/ws/arbitrage')
  const [updatedOpportunities, setUpdatedOpportunities] = useState<Set<string>>(new Set())
  
  useEffect(() => {
    if (lastMessage?.type === 'opportunity_update') {
      const { opportunityId } = lastMessage
      
      // Adicionar animação pulse
      setUpdatedOpportunities(prev => new Set([...prev, opportunityId]))
      
      // Remover animação após 2 segundos
      setTimeout(() => {
        setUpdatedOpportunities(prev => {
          const newSet = new Set(prev)
          newSet.delete(opportunityId)
          return newSet
        })
      }, 2000)
    }
  }, [lastMessage])
  
  return (
    <div className="space-y-4">
      {/* Indicador de Status de Conexão */}
      <div className="flex items-center gap-2">
        <Badge 
          variant={connectionStatus === 'connected' ? 'success' : 'destructive'}
          className="flex items-center gap-1"
        >
          <div className={`w-2 h-2 rounded-full ${
            connectionStatus === 'connected' ? 'bg-green-500' : 'bg-red-500'
          }`} />
          {connectionStatus === 'connected' ? 'Conectado' : 'Desconectado'}
        </Badge>
        <span className="text-sm text-muted-foreground">
          Última atualização: {new Date().toLocaleTimeString()}
        </span>
      </div>
      
      {/* Grid de Oportunidades com Animações */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {opportunities.map(opportunity => (
          <Card
            key={opportunity.id}
            variant={opportunity.profitability === 'high' ? 'high-profit' : 
                    opportunity.profitability === 'medium' ? 'medium-profit' : 'low-profit'}
            crossExchange={opportunity.spotExchange !== opportunity.futuresExchange}
            pulse={updatedOpportunities.has(opportunity.id)}
            spread={opportunity.spreadPercentage}
          >
            <OpportunityCard opportunity={opportunity} />
          </Card>
        ))}
      </div>
    </div>
  )
}
```

## Performance Optimizations

### Frontend Performance

```typescript
// Virtualização para Listas Grandes
import { FixedSizeList as List } from 'react-window'

const VirtualizedOpportunityTable: React.FC<{ opportunities: ArbitrageOpportunity[] }> = ({ 
  opportunities 
}) => {
  const Row = ({ index, style }: { index: number, style: React.CSSProperties }) => (
    <div style={style}>
      <OpportunityCard opportunity={opportunities[index]} />
    </div>
  )
  
  return (
    <List
      height={600}
      itemCount={opportunities.length}
      itemSize={120}
      width="100%"
    >
      {Row}
    </List>
  )
}

// Memoização de Cálculos Complexos
const useMemoizedOpportunities = (opportunities: ArbitrageOpportunity[], filters: FilterState) => {
  return useMemo(() => {
    return opportunities
      .filter(opp => {
        // Filtros aplicados
        if (filters.spotExchange && opp.spotExchange !== filters.spotExchange) return false
        if (filters.futuresExchange && opp.futuresExchange !== filters.futuresExchange) return false
        if (filters.minSpread && Math.abs(opp.spreadPercentage) < filters.minSpread) return false
        if (filters.searchTerm && !opp.symbol.toLowerCase().includes(filters.searchTerm.toLowerCase())) return false
        return true
      })
      .sort((a, b) => Math.abs(b.spreadPercentage) - Math.abs(a.spreadPercentage))
  }, [opportunities, filters])
}

// Debounce para Filtros
const useDebouncedFilters = (filters: FilterState, delay: number = 300) => {
  const [debouncedFilters, setDebouncedFilters] = useState(filters)
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedFilters(filters)
    }, delay)
    
    return () => clearTimeout(timer)
  }, [filters, delay])
  
  return debouncedFilters
}
```

### Backend Performance

```typescript
// Cache Multi-Camadas
class MultiLayerCache {
  private l1Cache = new Map<string, any>() // Memória - 2s
  private l2Cache = new Map<string, any>() // Memória - 5s
  private l3Cache = new Map<string, any>() // Memória - 10s
  
  async get(key: string): Promise<any> {
    // L1 Cache (mais rápido)
    if (this.l1Cache.has(key)) {
      return this.l1Cache.get(key)
    }
    
    // L2 Cache
    if (this.l2Cache.has(key)) {
      const value = this.l2Cache.get(key)
      this.l1Cache.set(key, value)
      return value
    }
    
    // L3 Cache
    if (this.l3Cache.has(key)) {
      const value = this.l3Cache.get(key)
      this.l2Cache.set(key, value)
      this.l1Cache.set(key, value)
      return value
    }
    
    return null
  }
  
  set(key: string, value: any, ttl: number = 5000): void {
    if (ttl <= 2000) {
      this.l1Cache.set(key, value)
    } else if (ttl <= 5000) {
      this.l2Cache.set(key, value)
    } else {
      this.l3Cache.set(key, value)
    }
    
    // Auto-cleanup
    setTimeout(() => {
      this.l1Cache.delete(key)
      this.l2Cache.delete(key)
      this.l3Cache.delete(key)
    }, ttl)
  }
}

// Connection Pooling para APIs
const createConnectionPool = () => {
  return new https.Agent({
    keepAlive: true,
    maxSockets: 50,
    maxFreeSockets: 10,
    timeout: 60000,
    freeSocketTimeout: 30000
  })
}
```

## Testing Strategy

### Comprehensive Testing Approach

```typescript
// Testes de Integração Cross-Exchange
describe('Cross-Exchange Arbitrage Integration', () => {
  test('should detect spot-futures cross-exchange opportunities', async () => {
    const opportunities = await dataCollector.collectAllData()
    
    const crossExchangeOpps = opportunities.filter(opp => 
      opp.spotExchange !== opp.futuresExchange
    )
    
    expect(crossExchangeOpps.length).toBeGreaterThan(0)
    expect(crossExchangeOpps.every(opp => 
      opp.type === 'spot-futures-cross'
    )).toBe(true)
  })
  
  test('should calculate accurate cross-exchange spreads', async () => {
    const mockSpotData = { exchange: 'mexc', price: 45000, bid: 44999, ask: 45001 }
    const mockFuturesData = { exchange: 'gateio', price: 45150, bid: 45149, ask: 45151 }
    
    const result = spreadCalculator.calculateCrossExchangeSpread(mockSpotData, mockFuturesData)
    
    expect(result.spreadPercentage).toBeCloseTo(0.33, 2)
    expect(result.strategy.type).toBe('spot-futures-cross')
    expect(result.strategy.action1).toContain('MEXC')
    expect(result.strategy.action2).toContain('Gate.io')
  })
})

// Testes de Performance
describe('Performance Tests', () => {
  test('should process 6800+ pairs within 10 seconds', async () => {
    const startTime = Date.now()
    const opportunities = await dataCollector.collectAllData()
    const processingTime = Date.now() - startTime
    
    expect(processingTime).toBeLessThan(10000) // 10 segundos
    expect(opportunities.length).toBeGreaterThan(100)
  })
  
  test('should maintain UI responsiveness with large datasets', async () => {
    const largeDataset = Array(2000).fill(null).map((_, i) => createMockOpportunity(i))
    
    const startTime = performance.now()
    render(<OpportunityTable opportunities={largeDataset} />)
    const renderTime = performance.now() - startTime
    
    expect(renderTime).toBeLessThan(100) // 100ms
  })
})
```

## Deployment Architecture

### Production Environment

```
┌─────────────────────────────────────────────────────────────┐
│                    PRODUÇÃO COMPLETA                       │
├─────────────────────────────────────────────────────────────┤
│  Frontend (Vercel)    │  Backend (Railway)  │  Monitoring   │
│  ┌─────────────────┐  │ ┌─────────────────┐ │ ┌───────────┐ │
│  │ • React Build   │  │ │ • Node.js API   │ │ │ • Logs    │ │
│  │ • Static Assets │  │ │ • WebSocket     │ │ │ • Metrics │ │
│  │ • CDN Cache     │  │ │ • Real APIs     │ │ │ • Alerts  │ │
│  │ • Edge Compute  │  │ │ • Cache Redis   │ │ │ • Health  │ │
│  └─────────────────┘  │ └─────────────────┘ │ └───────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    EXTERNAL APIS                           │
│  Gate.io (HMAC) • MEXC (HMAC) • Bitget (HMAC) • 6,800+ Pairs │
└─────────────────────────────────────────────────────────────┘
```

Este design garante um sistema completo, robusto e escalável que integra perfeitamente backend Node.js com frontend React moderno, processando dados reais de 6,800+ pares de criptomoedas através de APIs autenticadas das exchanges, com interface elegante e sistema completo de auditoria e monitoramento.