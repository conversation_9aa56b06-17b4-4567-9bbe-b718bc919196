# Design Document - Ultra-Low Latency Optimization

## Overview

Este documento detalha o design técnico para otimizar o sistema de arbitragem existente, reduzindo a latência de 15-20s para <1s. O design foca em trabalhar 100% dentro da estrutura atual, estendendo e otimizando os componentes existentes sem recriar sistemas.

## Architecture

### Current Architecture Analysis
```
PIPELINE ATUAL (15-20s total):
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   ExchangeAPI   │───▶│  DataCollector   │───▶│ useArbitrageData│
│   (Sequential)  │    │  (Single-thread) │    │   (15s polling) │
└─────────────────┘    └──────────────────┘    └─────────────────┘
        │                       │                       │
    2-5s delay            3-5s processing         15s refresh
```

### Optimized Architecture Design
```
PIPELINE OTIMIZADO (<1s total):
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   ExchangeAPI   │───▶│  DataCollector   │───▶│ useArbitrageData│
│   (Parallel +   │    │  (Batch +        │    │ (WebSocket +    │
│    WebSocket)   │    │   Streaming)     │    │  1s polling)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
        │                       │                       │
    0.2s delay            0.2s processing         0.1s updates
```

## Components and Interfaces

### 1. ExchangeService Optimization

#### Current Implementation
```typescript
// backend/src/services/ExchangeService.ts (EXISTING)
class ExchangeService {
  async getAllExchangeData() {
    // Sequential calls - SLOW
    const gateioData = await this.fetchGateioData()
    const mexcData = await this.fetchMexcData() 
    const bitgetData = await this.fetchBitgetData()
  }
}
```

#### Optimized Design
```typescript
// EXTEND existing ExchangeService.ts
class ExchangeService {
  // NEW: Parallel processing method
  async getAllExchangeDataParallel() {
    const startTime = Date.now()
    
    // Use existing fetch methods in parallel
    const [gateioData, mexcData, bitgetData] = await Promise.allSettled([
      this.fetchGateioData(),    // REUSE existing
      this.fetchMexcData(),      // REUSE existing  
      this.fetchBitgetData()     // REUSE existing
    ])
    
    // EXTEND existing error handling
    return this.processParallelResults(gateioData, mexcData, bitgetData)
  }
  
  // NEW: Connection pooling optimization
  private optimizeAxiosClients() {
    const poolConfig = {
      keepAlive: true,
      maxSockets: 50,
      timeout: 5000
    }
    
    // EXTEND existing clients
    this.gateioClient.defaults.httpAgent = new http.Agent(poolConfig)
    this.mexcClient.defaults.httpAgent = new http.Agent(poolConfig)
    this.bitgetClient.defaults.httpAgent = new http.Agent(poolConfig)
  }
}
```

### 2. CacheService Multi-Layer Enhancement

#### Current Implementation
```typescript
// backend/src/services/CacheService.ts (EXISTING)
class CacheService {
  private cache = new Map<string, CacheEntry<any>>()
  private defaultTTL = 30000
}
```

#### Enhanced Design
```typescript
// EXTEND existing CacheService.ts
class CacheService {
  // EXTEND existing cache with layers
  private L1Cache = new Map<string, CacheEntry<any>>() // Hot data: 100ms TTL
  private L2Cache = new Map<string, CacheEntry<any>>() // Warm data: 1s TTL  
  private L3Cache = new Map<string, CacheEntry<any>>() // Cold data: 30s TTL
  
  // ENHANCE existing get method
  get<T>(key: string): T | null {
    // Try L1 first (fastest)
    let entry = this.L1Cache.get(key)
    if (entry && !this.isExpired(entry)) {
      this.hitCount++
      return entry.data as T
    }
    
    // Try L2 (medium speed)
    entry = this.L2Cache.get(key)
    if (entry && !this.isExpired(entry)) {
      // Promote to L1
      this.L1Cache.set(key, { ...entry, ttl: 100 })
      this.hitCount++
      return entry.data as T
    }
    
    // Try L3 (slower but still cached)
    entry = this.L3Cache.get(key)
    if (entry && !this.isExpired(entry)) {
      // Promote to L2
      this.L2Cache.set(key, { ...entry, ttl: 1000 })
      this.hitCount++
      return entry.data as T
    }
    
    this.missCount++
    return null
  }
  
  // ENHANCE existing set method with intelligent layering
  setIntelligent<T>(key: string, data: T, priority: 'hot' | 'warm' | 'cold' = 'warm'): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: this.getTTLByPriority(priority)
    }
    
    switch (priority) {
      case 'hot':
        this.L1Cache.set(key, entry)
        break
      case 'warm':
        this.L2Cache.set(key, entry)
        break
      case 'cold':
        this.L3Cache.set(key, entry)
        break
    }
  }
}
```

### 3. WebSocket Integration Design

#### Backend WebSocket Server
```typescript
// EXTEND backend/src/server.ts (EXISTING)
import { WebSocketServer } from 'ws'

// ADD to existing server
const wss = new WebSocketServer({ port: 5001 })

// NEW: WebSocket opportunity broadcaster
class OpportunityBroadcaster {
  private clients = new Set<WebSocket>()
  
  broadcast(opportunity: ArbitrageOpportunity) {
    const message = JSON.stringify({
      type: 'opportunity',
      data: opportunity,
      timestamp: Date.now()
    })
    
    this.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message)
      }
    })
  }
}

// INTEGRATE with existing ExchangeService
const broadcaster = new OpportunityBroadcaster()

// EXTEND existing /api/arbitrage/opportunities endpoint
app.get('/api/arbitrage/opportunities', async (req, res) => {
  const opportunities = await exchangeService.calculateArbitrageOpportunities()
  
  // EXISTING response
  res.json({ opportunities })
  
  // NEW: Broadcast via WebSocket
  opportunities.forEach(opp => broadcaster.broadcast(opp))
})
```

#### Frontend WebSocket Integration
```typescript
// EXTEND src/hooks/useArbitrageData.ts (EXISTING)
export function useArbitrageData(options: UseArbitrageDataOptions = {}) {
  // EXISTING React Query setup
  const queryClient = useQueryClient()
  
  // NEW: WebSocket integration
  const wsUrl = import.meta.env.VITE_BACKEND_URL?.replace('http', 'ws') || 'ws://localhost:5001'
  
  const { sendMessage, lastMessage, connectionStatus } = useWebSocket(
    { url: wsUrl, debug: process.env.NODE_ENV === 'development' },
    (message) => {
      if (message.type === 'opportunity') {
        // UPDATE existing React Query cache
        queryClient.setQueryData(['arbitrage-data'], (oldData: any) => {
          if (!oldData) return oldData
          
          const newOpportunity = message.data
          const existingOpportunities = oldData.opportunities || []
          
          // MERGE with existing data
          const updatedOpportunities = [
            newOpportunity,
            ...existingOpportunities.filter((opp: any) => opp.id !== newOpportunity.id)
          ].slice(0, 1000) // Keep only latest 1000
          
          return {
            ...oldData,
            opportunities: updatedOpportunities
          }
        })
      }
    }
  )
  
  // EXISTING query with REDUCED interval
  const query = useQuery({
    queryKey: ['arbitrage-data'],
    queryFn: async () => {
      // EXISTING DataCollector call
      const opportunities = await dataCollector.collectAllData()
      const metrics = dataCollector.calculateDashboardMetrics(opportunities)
      return { opportunities, metrics }
    },
    refetchInterval: 1000, // REDUCED from 15000ms
    staleTime: 500,        // REDUCED from 2000ms
    // ... existing options
  })
  
  return {
    // EXISTING returns
    opportunities: query.data?.opportunities || [],
    metrics: query.data?.metrics || null,
    // ... existing fields
    
    // NEW: WebSocket status
    wsConnectionStatus: connectionStatus
  }
}
```

### 4. DataCollector Batch Processing

#### Current Implementation
```typescript
// src/services/DataCollector.ts (EXISTING)
class DataCollector {
  async collectAllData(): Promise<ArbitrageOpportunity[]> {
    // Sequential processing - SLOW
    const backendOpportunities = await this.tryBackendWithRetry()
    return backendOpportunities
  }
}
```

#### Optimized Design
```typescript
// EXTEND existing DataCollector.ts
class DataCollector {
  // ENHANCE existing collectAllData method
  async collectAllData(): Promise<ArbitrageOpportunity[]> {
    const startTime = Date.now()
    
    // TRY optimized backend first
    const backendOpportunities = await this.tryOptimizedBackend()
    
    if (backendOpportunities && backendOpportunities.length > 0) {
      // NEW: Batch processing for large datasets
      const processedOpportunities = await this.processBatch(backendOpportunities)
      
      console.log(`✅ DataCollector: ${processedOpportunities.length} opportunities processed in ${Date.now() - startTime}ms`)
      return processedOpportunities
    }
    
    // EXISTING fallback unchanged
    return await this.tryLocalFallback()
  }
  
  // NEW: Batch processing method
  private async processBatch(opportunities: any[], batchSize = 100): Promise<ArbitrageOpportunity[]> {
    const results: ArbitrageOpportunity[] = []
    
    for (let i = 0; i < opportunities.length; i += batchSize) {
      const batch = opportunities.slice(i, i + batchSize)
      
      // Process batch in parallel
      const batchResults = await Promise.all(
        batch.map(opp => this.processOpportunity(opp))
      )
      
      results.push(...batchResults.filter(Boolean))
    }
    
    return results
  }
  
  // NEW: Optimized backend call
  private async tryOptimizedBackend(): Promise<ArbitrageOpportunity[] | null> {
    try {
      // REUSE existing ExchangeAPI but with optimized endpoint
      const opportunities = await this.exchangeAPI.getArbitrageOpportunitiesOptimized()
      return opportunities
    } catch (error) {
      console.error('❌ Optimized backend failed, falling back:', error)
      return null
    }
  }
}
```

## Data Models

### Enhanced Opportunity Model
```typescript
// EXTEND existing src/types/arbitrage.ts
interface ArbitrageOpportunity {
  // EXISTING fields unchanged
  id: string
  symbol: string
  // ... all existing fields
  
  // NEW: Performance metadata
  processingTime?: number
  dataFreshness?: number
  cacheHit?: boolean
  region?: 'us-east' | 'eu-west' | 'asia'
  
  // NEW: Real-time tracking
  detectedAt: Date
  broadcastAt?: Date
  receivedAt?: Date
}

// NEW: Performance metrics model
interface PerformanceMetrics {
  latency: {
    p50: number
    p90: number
    p95: number
    p99: number
  }
  throughput: {
    requestsPerSecond: number
    opportunitiesPerSecond: number
  }
  cache: {
    hitRate: number
    l1HitRate: number
    l2HitRate: number
    l3HitRate: number
  }
  websocket: {
    connected: boolean
    messagesSent: number
    messagesReceived: number
    averageLatency: number
  }
}
```

## Error Handling

### Graceful Degradation Strategy
```typescript
// EXTEND existing error handling in all services
class OptimizedErrorHandler {
  // Fallback chain: WebSocket → HTTP Polling → Cache → Mock Data
  async handleDataSourceFailure(source: 'websocket' | 'http' | 'cache') {
    switch (source) {
      case 'websocket':
        console.warn('WebSocket failed, falling back to HTTP polling')
        return this.enableHttpPolling()
        
      case 'http':
        console.warn('HTTP failed, using cached data')
        return this.serveCachedData()
        
      case 'cache':
        console.warn('Cache failed, using mock data')
        return this.serveMockData()
    }
  }
  
  // Circuit breaker for failing exchanges
  private circuitBreakers = new Map<string, CircuitBreaker>()
  
  async callWithCircuitBreaker(exchange: string, fn: () => Promise<any>) {
    const breaker = this.circuitBreakers.get(exchange)
    
    if (breaker?.isOpen()) {
      throw new Error(`Circuit breaker open for ${exchange}`)
    }
    
    try {
      const result = await fn()
      breaker?.recordSuccess()
      return result
    } catch (error) {
      breaker?.recordFailure()
      throw error
    }
  }
}
```

## Testing Strategy

### Performance Testing Framework
```typescript
// NEW: Performance test utilities
class PerformanceTestSuite {
  // Latency testing
  async testLatencyRequirements() {
    const samples = []
    
    for (let i = 0; i < 1000; i++) {
      const start = Date.now()
      await this.dataCollector.collectAllData()
      const latency = Date.now() - start
      samples.push(latency)
    }
    
    const p90 = this.calculatePercentile(samples, 90)
    const p99 = this.calculatePercentile(samples, 99)
    
    expect(p90).toBeLessThan(800) // 90% under 800ms
    expect(p99).toBeLessThan(1200) // 99% under 1.2s
  }
  
  // Throughput testing
  async testThroughputRequirements() {
    const startTime = Date.now()
    const promises = []
    
    // Simulate 250 concurrent requests
    for (let i = 0; i < 250; i++) {
      promises.push(this.exchangeAPI.getArbitrageOpportunities())
    }
    
    await Promise.all(promises)
    const duration = Date.now() - startTime
    const throughput = 250 / (duration / 1000)
    
    expect(throughput).toBeGreaterThan(200) // >200 req/s
  }
}
```

### Integration Testing
```typescript
// EXTEND existing tests
describe('Optimized System Integration', () => {
  test('should maintain backward compatibility', async () => {
    // All existing functionality should work unchanged
    const opportunities = await dataCollector.collectAllData()
    expect(opportunities).toBeDefined()
    expect(opportunities.length).toBeGreaterThan(0)
  })
  
  test('should achieve <1s latency for 90% of requests', async () => {
    const latencies = []
    
    for (let i = 0; i < 100; i++) {
      const start = Date.now()
      await dataCollector.collectAllData()
      latencies.push(Date.now() - start)
    }
    
    const under1s = latencies.filter(l => l < 1000).length
    expect(under1s / latencies.length).toBeGreaterThan(0.9)
  })
})
```

## Deployment Strategy

### Phased Rollout Plan
```typescript
// Feature flags for gradual rollout
const FEATURE_FLAGS = {
  PARALLEL_PROCESSING: process.env.ENABLE_PARALLEL === 'true',
  MULTI_LAYER_CACHE: process.env.ENABLE_L3_CACHE === 'true', 
  WEBSOCKET_STREAMING: process.env.ENABLE_WEBSOCKET === 'true',
  BATCH_PROCESSING: process.env.ENABLE_BATCHING === 'true',
  EDGE_COMPUTING: process.env.ENABLE_EDGE === 'true'
}

// Gradual activation
class OptimizationController {
  async enableOptimization(phase: 1 | 2 | 3) {
    switch (phase) {
      case 1: // Week 1: Parallelization + Cache
        this.enableParallelProcessing()
        this.enableMultiLayerCache()
        break
        
      case 2: // Week 2: WebSocket + Batching  
        this.enableWebSocketStreaming()
        this.enableBatchProcessing()
        break
        
      case 3: // Week 3: Edge Computing
        this.enableEdgeComputing()
        break
    }
  }
}
```

This design ensures all optimizations work within the existing codebase structure while achieving the <1s latency target through systematic improvements to each component.