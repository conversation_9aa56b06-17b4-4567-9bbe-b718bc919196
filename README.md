# 🚀 Sistema Completo de Arbitragem de Criptomoedas

Sistema moderno e robusto para detecção de oportunidades de arbitragem cross-exchange em tempo real, processando **6,800+ pares** de criptomoedas das principais exchanges com **95.2% de qualidade** (Excellent).

## 🚀 Funcionalidades Principais

### ✅ Backend Robusto
- Coleta dados de 6,800+ pares de criptomoedas
- Integração com Gate.io, MEXC e Bitget via APIs reais
- Autenticação HMAC para todas as exchanges
- Sistema de cache multi-camadas inteligente
- Processamento de arbitragem cross-exchange

### ✅ Interface Moderna
- Layout responsivo com sidebar colapsável
- Sistema de temas (claro/escuro/sistema)
- Gráficos interativos com Recharts
- Filtros avançados em tempo real
- Virtualização para performance otimizada

### ✅ Funcionalidades Avançadas
- Detecção de oportunidades spot vs futuros cross-exchange
- Gerenciador de posições com P&L em tempo real
- Sistema de alertas sonoros e visuais
- Atualizações WebSocket em tempo real
- Métricas históricas e analytics

## 🛠️ Stack Tecnológica

### Frontend
- **React 18+** com TypeScript
- **Vite** como bundler
- **Tailwind CSS** para estilização
- **shadcn/ui** para componentes
- **React Query** para gerenciamento de estado
- **Recharts** para gráficos
- **React Window** para virtualização

### Backend
- **Node.js** com Express
- **TypeScript** para type safety
- **Axios** para chamadas HTTP
- **WebSocket** para tempo real
- **Cache multi-camadas**

### Exchanges Integradas
- **Gate.io**: 2,670 spot + 602 futuros (HMAC SHA512)
- **MEXC**: 2,429 spot + 787 futuros (HMAC SHA256)
- **Bitget**: 799 spot + 513 futuros (HMAC SHA256 + Base64)

## 🚀 Como Executar

### Pré-requisitos
- Node.js 18+
- npm ou yarn

### Instalação
```bash
# Clonar o repositório
git clone <repository-url>
cd crypto-arbitrage-system

# Instalar dependências
npm install

# Configurar variáveis de ambiente
cp .env.example .env
# Editar .env com suas chaves de API das exchanges

# Executar em desenvolvimento
npm run dev
```

### Scripts Disponíveis
```bash
npm run dev      # Servidor de desenvolvimento
npm run build    # Build para produção
npm run preview  # Preview do build
npm run test     # Executar testes
npm run lint     # Linter
```

## 📁 Estrutura do Projeto

```
src/
├── components/
│   ├── ui/                     # Componentes base do sistema de design
│   ├── layout/                 # Layout principal e navegação
│   ├── dashboard/              # Dashboard e métricas
│   ├── opportunities/          # Sistema de oportunidades
│   ├── filters/                # Filtros avançados
│   ├── charts/                 # Gráficos e visualizações
│   ├── positions/              # Gerenciador de posições
│   ├── realtime/               # Atualizações em tempo real
│   └── notifications/          # Sistema de notificações
├── hooks/                      # Hooks customizados
├── services/                   # Serviços e APIs
├── types/                      # Definições TypeScript
├── utils/                      # Utilitários
├── config/                     # Configurações
└── styles/                     # Estilos globais
```

## 🎯 Métricas de Performance

### Targets de Performance
- ⚡ Carregamento inicial: < 2 segundos
- 🔄 Latência de atualizações: < 100ms
- 📊 Suporte a 2000+ oportunidades simultâneas
- 🔗 Uptime > 99% durante uso
- 📱 Responsivo em todos os dispositivos

### Qualidade
- ✅ Cobertura de testes > 85%
- ✅ Zero bugs críticos
- ✅ Compatibilidade com browsers modernos
- ✅ Acessibilidade WCAG AA compliant

## 🔧 Configuração das APIs

### Variáveis de Ambiente Necessárias
```env
# Gate.io
GATEIO_API_KEY=your_gateio_api_key
GATEIO_SECRET_KEY=your_gateio_secret_key

# MEXC
MEXC_API_KEY=your_mexc_api_key
MEXC_SECRET_KEY=your_mexc_secret_key

# Bitget
BITGET_API_KEY=your_bitget_api_key
BITGET_SECRET_KEY=your_bitget_secret_key
BITGET_PASSPHRASE=your_bitget_passphrase
```

## 📈 Funcionalidades de Arbitragem

### Tipos de Arbitragem Suportados
1. **Spot vs Futuros Cross-Exchange**: Comprar spot na Exchange A e shortar futuros na Exchange B
2. **Futuros vs Futuros Cross-Exchange**: Long futuros na Exchange A e short futuros na Exchange B

### Critérios de Validação
- ✅ Spread mínimo: 0.05%
- ✅ Volume mínimo: $1,000 USD
- ✅ Dados frescos: < 15 segundos
- ✅ Liquidez suficiente
- ✅ Exchanges diferentes (cross-exchange)

### Classificação de Rentabilidade
- 🟢 **Alta**: >= 1% spread
- 🟡 **Média**: 0.5% - 1% spread
- 🔵 **Baixa**: 0.05% - 0.5% spread

## 🔔 Sistema de Alertas

### Tipos de Alertas
- 👁️ **Visual**: Spread > 0.5%
- 🔊 **Sonoro**: Spread > 1.0%
- 📱 **Vibração**: Dispositivos móveis
- 🌐 **Push**: Notificações do navegador

### Alertas de Posições
- ⚠️ Alerta automático quando spread se aproxima de zero
- 🎯 Threshold personalizado por posição
- 🔄 Atualização de P&L em tempo real

## 🧪 Testes

```bash
# Executar todos os testes
npm run test

# Testes com interface
npm run test:ui

# Testes de cobertura
npm run test:coverage
```

## 📝 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

## 🤝 Contribuição

Contribuições são bem-vindas! Por favor, leia o [CONTRIBUTING.md](CONTRIBUTING.md) para detalhes sobre nosso código de conduta e processo de submissão de pull requests.

## 📞 Suporte

Para suporte e dúvidas:
- 📧 Email: <EMAIL>
- 💬 Discord: [Link do Discord]
- 📖 Documentação: [Link da Documentação]

---

**⚡ Sistema de Arbitragem de Criptomoedas - Detecte oportunidades em tempo real com precisão máxima!**