{"name": "crypto-arbitrage-backend", "version": "1.0.0", "description": "Backend Node.js para Sistema de Arbitragem com APIs Reais", "main": "dist/server.js", "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "vitest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.5", "crypto-js": "^4.2.0", "dotenv": "^16.3.1", "ws": "^8.16.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/crypto-js": "^4.2.2", "@types/node": "^20.10.5", "@types/ws": "^8.5.10", "tsx": "^4.7.0", "typescript": "^5.2.2", "vitest": "^1.1.0"}}