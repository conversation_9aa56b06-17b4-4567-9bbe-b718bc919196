import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import http from 'http';
import { ExchangeService } from './services/ExchangeService.js';
import { WebSocketService } from './services/WebSocketService.js';
import { PerformanceMonitor } from './services/PerformanceMonitor.js';
import { FeatureFlagService } from './services/FeatureFlagService.js';
import type { ArbitrageOpportunity } from './types/index.js';

// Carregar variáveis de ambiente
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5001;

// Middleware
app.use(cors({
  origin: [
    process.env.CORS_ORIGIN || 'http://localhost:5002',
    'http://localhost:5003',
    'http://localhost:5004'
  ],
  credentials: true
}));

app.use(express.json());

// Rate limiting simples
const requestCounts = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_REQUESTS = parseInt(process.env.RATE_LIMIT_REQUESTS || '100');
const RATE_LIMIT_WINDOW = parseInt(process.env.RATE_LIMIT_WINDOW || '60000'); // 1 minuto

// OTIMIZAÇÃO: Middleware de métricas com PerformanceMonitor
const metricsMiddleware = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const startTime = Date.now();

  // Interceptar o final da resposta
  const originalSend = res.send;
  res.send = function(data) {
    const responseTime = Date.now() - startTime;
    const isError = res.statusCode >= 400;
    const operation = `${req.method} ${req.path}`;

    // OTIMIZAÇÃO: Registrar no PerformanceMonitor
    const performanceMonitor = PerformanceMonitor.getInstance();
    performanceMonitor.recordLatency(operation, responseTime, !isError);

    // Manter compatibilidade com MetricsService existente
    import('./services/MetricsService.js').then(({ MetricsService }) => {
      const metricsService = MetricsService.getInstance();
      metricsService.recordApiCall(responseTime, isError);
    }).catch(console.error);

    return originalSend.call(this, data);
  };

  next();
};

const rateLimiter = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
  const now = Date.now();
  
  const clientData = requestCounts.get(clientIP);
  
  if (!clientData || now > clientData.resetTime) {
    requestCounts.set(clientIP, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    next();
  } else if (clientData.count < RATE_LIMIT_REQUESTS) {
    clientData.count++;
    next();
  } else {
    res.status(429).json({ 
      error: 'Rate limit exceeded',
      message: `Máximo ${RATE_LIMIT_REQUESTS} requests por minuto`
    });
  }
};

app.use(rateLimiter);

// Instância do serviço de exchanges
const exchangeService = new ExchangeService();

// Aplicar middleware de métricas
app.use(metricsMiddleware);

// Middleware de logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    env: {
      enableRealAPIs: process.env.ENABLE_REAL_APIS === 'true',
      nodeEnv: process.env.NODE_ENV || 'development'
    }
  });
});

// Endpoint principal - dados de todas as exchanges
app.get('/api/exchanges/data', async (req, res) => {
  try {
    console.log('🚀 Iniciando coleta de dados das exchanges...');
    const startTime = Date.now();
    
    const data = await exchangeService.getAllExchangeData();
    
    const processingTime = Date.now() - startTime;
    console.log(`✅ Dados coletados em ${processingTime}ms`);
    
    res.json({
      success: true,
      data,
      processingTime,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao coletar dados das exchanges:', error);
    
    res.status(500).json({
      success: false,
      error: 'Falha na coleta de dados',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// Endpoint para oportunidades de arbitragem
app.get('/api/arbitrage/opportunities', async (req, res) => {
  try {
    console.log('🎯 Calculando oportunidades de arbitragem...');
    const startTime = Date.now();
    
    const opportunities = await exchangeService.calculateArbitrageOpportunities();

    const processingTime = Date.now() - startTime;
    console.log(`✅ ${opportunities.length} oportunidades calculadas em ${processingTime}ms`);

    // OTIMIZAÇÃO: Broadcast via WebSocket para clientes conectados
    if (opportunities.length > 0) {
      opportunities.forEach(opportunity => {
        webSocketService.broadcastOpportunity(opportunity);
      });
    }

    res.json({
      success: true,
      opportunities,
      count: opportunities.length,
      processingTime,
      timestamp: new Date().toISOString(),
      websocketClients: webSocketService.getStatus().clientCount
    });
    
  } catch (error) {
    console.error('❌ Erro ao calcular oportunidades:', error);
    
    res.status(500).json({
      success: false,
      error: 'Falha no cálculo de oportunidades',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// OTIMIZAÇÃO: Endpoint para status do WebSocket
app.get('/api/websocket/status', (req, res) => {
  try {
    const status = webSocketService.getStatus();
    const metrics = webSocketService.getMetrics();

    res.json({
      success: true,
      status,
      metrics,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao obter status do WebSocket',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

// OTIMIZAÇÃO: Endpoint para métricas de performance detalhadas
app.get('/api/performance/metrics', (req, res) => {
  try {
    const metrics = performanceMonitor.getMetrics();

    res.json({
      success: true,
      metrics,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao obter métricas de performance',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

// OTIMIZAÇÃO: Endpoint para resumo de performance
app.get('/api/performance/summary', (req, res) => {
  try {
    const summary = performanceMonitor.getSummary();

    res.json({
      success: true,
      summary,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao obter resumo de performance',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

// OTIMIZAÇÃO: Endpoint para alertas de performance
app.get('/api/performance/alerts', (req, res) => {
  try {
    const metrics = performanceMonitor.getMetrics();
    const activeAlerts = metrics.alerts.filter(alert =>
      Date.now() - alert.timestamp.getTime() < 300000 // Últimos 5 minutos
    );

    res.json({
      success: true,
      alerts: activeAlerts,
      totalAlerts: metrics.alerts.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao obter alertas de performance',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

// OTIMIZAÇÃO: Endpoints para Feature Flags
app.get('/api/feature-flags', (req, res) => {
  try {
    const flags = featureFlagService.getAllFlags();
    const config = featureFlagService.getConfig();
    const status = featureFlagService.getSystemStatus();

    res.json({
      success: true,
      flags,
      config,
      status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao obter feature flags',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

app.post('/api/feature-flags/:flagName', (req, res) => {
  try {
    const { flagName } = req.params;
    const { enabled, rolloutPercentage } = req.body;

    if (typeof enabled !== 'boolean') {
      return res.status(400).json({
        success: false,
        error: 'Campo "enabled" deve ser boolean'
      });
    }

    featureFlagService.setFlag(flagName as any, enabled, rolloutPercentage);

    res.json({
      success: true,
      message: `Flag ${flagName} ${enabled ? 'habilitada' : 'desabilitada'}`,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao atualizar feature flag',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

app.post('/api/feature-flags/:flagName/rollback', (req, res) => {
  try {
    const { flagName } = req.params;
    const success = featureFlagService.rollbackFlag(flagName as any);

    if (success) {
      res.json({
        success: true,
        message: `Rollback da flag ${flagName} realizado com sucesso`,
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(400).json({
        success: false,
        error: `Não foi possível fazer rollback da flag ${flagName}`
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao fazer rollback da feature flag',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

app.post('/api/emergency/activate', (req, res) => {
  try {
    featureFlagService.activateEmergencyMode();

    res.json({
      success: true,
      message: 'Modo de emergência ativado - todas as otimizações desabilitadas',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao ativar modo de emergência',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

app.post('/api/emergency/deactivate', (req, res) => {
  try {
    featureFlagService.deactivateEmergencyMode();

    res.json({
      success: true,
      message: 'Modo de emergência desativado - estado anterior restaurado',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao desativar modo de emergência',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

// Endpoint para dados de uma exchange específica
app.get('/api/exchanges/:exchange', async (req, res) => {
  try {
    const { exchange } = req.params;
    
    if (!['gateio', 'mexc', 'bitget'].includes(exchange)) {
      return res.status(400).json({
        success: false,
        error: 'Exchange inválida',
        message: 'Exchanges suportadas: gateio, mexc, bitget'
      });
    }
    
    console.log(`🔍 Coletando dados específicos da ${exchange}...`);
    const startTime = Date.now();
    
    const allData = await exchangeService.getAllExchangeData();
    
    // Filtrar dados da exchange específica
    const spotData = allData.allSpotData.find(data => data.exchange === exchange);
    const futuresData = allData.allFuturesData.find(data => data.exchange === exchange);
    
    const processingTime = Date.now() - startTime;
    
    res.json({
      success: true,
      exchange,
      data: {
        spot: spotData || { exchange, spot: [], futures: [], timestamp: Date.now(), status: 'not_found' },
        futures: futuresData || { exchange, spot: [], futures: [], timestamp: Date.now(), status: 'not_found' }
      },
      processingTime,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error(`❌ Erro ao coletar dados da ${req.params.exchange}:`, error);
    
    res.status(500).json({
      success: false,
      error: 'Falha na coleta de dados da exchange',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// Endpoint para estatísticas de cache
app.get('/api/cache/stats', async (req, res) => {
  try {
    const { CacheService } = await import('./services/CacheService.js');
    const cacheService = CacheService.getInstance();
    const stats = cacheService.getStats();
    
    res.json({
      success: true,
      cache: stats,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao obter estatísticas do cache:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao obter estatísticas do cache',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// Endpoint para limpar cache
app.post('/api/cache/clear', async (req, res) => {
  try {
    const { CacheService } = await import('./services/CacheService.js');
    const cacheService = CacheService.getInstance();
    cacheService.clear();
    cacheService.resetStats();
    
    res.json({
      success: true,
      message: 'Cache limpo com sucesso',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao limpar cache:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao limpar cache',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// Endpoint para estatísticas do sistema
app.get('/api/stats', async (req, res) => {
  try {
    const data = await exchangeService.getAllExchangeData();
    
    const stats = {
      totalExchanges: 3,
      totalSpotPairs: data.allSpotData.reduce((sum, exchange) => sum + exchange.spot.length, 0),
      totalFuturesPairs: data.allFuturesData.reduce((sum, exchange) => sum + exchange.futures.length, 0),
      totalPairs: 0, // Será calculado abaixo
      exchanges: data.metadata.exchanges,
      lastUpdate: data.metadata.timestamp,
      processingTime: data.metadata.processingTime,
      errors: data.metadata.errors,
      warnings: data.metadata.warnings
    };
    
    stats.totalPairs = stats.totalSpotPairs + stats.totalFuturesPairs;
    
    res.json({
      success: true,
      stats,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao coletar estatísticas:', error);
    
    res.status(500).json({
      success: false,
      error: 'Falha na coleta de estatísticas',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// Endpoint para debug de símbolo específico
app.get('/api/debug/symbol/:symbol', async (req, res) => {
  try {
    let symbol = req.params.symbol.toUpperCase();
    
    // Se não contém /, adicionar /USDT
    if (!symbol.includes('/')) {
      symbol = symbol + '/USDT';
    }
    
    console.log(`🔍 Debug para símbolo: ${symbol}`);
    
    const opportunities = await exchangeService.calculateArbitrageOpportunities();
    
    // Filtrar oportunidades do símbolo específico
    const symbolOpportunities = opportunities.filter(opp => 
      opp.symbol.toUpperCase() === symbol
    );
    
    // Análise detalhada
    const debug = {
      symbol,
      found: symbolOpportunities.length,
      opportunities: symbolOpportunities.map(opp => ({
        id: `${opp.symbol}-${opp.spotExchange}-${opp.futuresExchange}`,
        type: opp.type,
        spotExchange: opp.spotExchange,
        futuresExchange: opp.futuresExchange,
        spotPrice: opp.spotPrice,
        futuresPrice: opp.futuresPrice,
        spread: opp.spread,
        spreadPercentage: opp.spreadPercentage,
        volume: opp.volume,
        fundingRate: opp.fundingRate,
        profitPotential: opp.profitPotential,
        timestamp: opp.timestamp,
        dataAge: opp.dataAge,
        isValid: Math.abs(opp.spreadPercentage) > 0.05 && opp.dataAge < 30000
      })),
      allSymbols: [...new Set(opportunities.map(o => o.symbol))].sort(),
      totalOpportunities: opportunities.length
    };
    
    res.json({
      success: true,
      debug,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro no debug:', error);
    res.status(500).json({
      success: false,
      error: 'Falha no debug',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// Endpoint para validação de oportunidades > threshold
app.get('/api/validation/opportunities/:threshold', async (req, res) => {
  try {
    const threshold = parseFloat(req.params.threshold) || 0.3;
    console.log(`🔍 Validando oportunidades > ${threshold}%...`);
    
    const opportunities = await exchangeService.calculateArbitrageOpportunities();
    
    // Filtrar oportunidades acima do threshold
    const filteredOpportunities = opportunities.filter(opp => 
      Math.abs(opp.spreadPercentage) > threshold
    );
    
    // Análise detalhada
    const analysis = {
      threshold: threshold + '%',
      total: opportunities.length,
      aboveThreshold: filteredOpportunities.length,
      percentage: opportunities.length > 0 ? (filteredOpportunities.length / opportunities.length * 100).toFixed(2) + '%' : '0%',
      
      byType: filteredOpportunities.reduce((acc, opp) => {
        acc[opp.type] = (acc[opp.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      
      byExchange: filteredOpportunities.reduce((acc, opp) => {
        const key = `${opp.spotExchange}-${opp.futuresExchange}`;
        acc[key] = (acc[key] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      
      spreadRanges: {
        '0.3-0.5%': filteredOpportunities.filter(o => Math.abs(o.spreadPercentage) >= 0.3 && Math.abs(o.spreadPercentage) < 0.5).length,
        '0.5-1%': filteredOpportunities.filter(o => Math.abs(o.spreadPercentage) >= 0.5 && Math.abs(o.spreadPercentage) < 1).length,
        '1-2%': filteredOpportunities.filter(o => Math.abs(o.spreadPercentage) >= 1 && Math.abs(o.spreadPercentage) < 2).length,
        '2-5%': filteredOpportunities.filter(o => Math.abs(o.spreadPercentage) >= 2 && Math.abs(o.spreadPercentage) < 5).length,
        '>5%': filteredOpportunities.filter(o => Math.abs(o.spreadPercentage) >= 5).length
      },
      
      topOpportunities: filteredOpportunities
        .sort((a, b) => Math.abs(b.spreadPercentage) - Math.abs(a.spreadPercentage))
        .slice(0, 10)
        .map(o => ({
          symbol: o.symbol,
          type: o.type,
          spotExchange: o.spotExchange,
          futuresExchange: o.futuresExchange,
          spreadPercentage: Math.abs(o.spreadPercentage).toFixed(3) + '%',
          volume: Math.round(o.volume).toLocaleString()
        }))
    };
    
    res.json({
      success: true,
      analysis,
      opportunities: filteredOpportunities,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro na validação:', error);
    res.status(500).json({
      success: false,
      error: 'Falha na validação',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// Endpoint para métricas de qualidade
app.get('/api/quality/metrics', async (req, res) => {
  try {
    console.log('📊 Calculando métricas de qualidade...');
    const startTime = Date.now();
    
    const opportunities = await exchangeService.calculateArbitrageOpportunities();
    
    // Calcular métricas detalhadas
    const spreads = opportunities.map(o => Math.abs(o.spreadPercentage));
    const volumes = opportunities.map(o => o.volume);
    const dataAges = opportunities.map(o => o.dataAge);

    // Agrupar por tipo
    const byType = opportunities.reduce((acc, opp) => {
      acc[opp.type] = (acc[opp.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Agrupar por exchange
    const byExchange = opportunities.reduce((acc, opp) => {
      const key = `${opp.spotExchange}-${opp.futuresExchange}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Oportunidades por faixa de spread
    const spreadRanges = {
      'ultra_low': opportunities.filter(o => Math.abs(o.spreadPercentage) < 0.1).length,
      'low': opportunities.filter(o => Math.abs(o.spreadPercentage) >= 0.1 && Math.abs(o.spreadPercentage) < 0.5).length,
      'medium': opportunities.filter(o => Math.abs(o.spreadPercentage) >= 0.5 && Math.abs(o.spreadPercentage) < 1).length,
      'high': opportunities.filter(o => Math.abs(o.spreadPercentage) >= 1 && Math.abs(o.spreadPercentage) < 5).length,
      'very_high': opportunities.filter(o => Math.abs(o.spreadPercentage) >= 5).length
    };

    // Oportunidades de alta qualidade
    const highQuality = opportunities.filter(o => 
      Math.abs(o.spreadPercentage) > 1 && 
      o.volume > 10000
    );

    const processingTime = Date.now() - startTime;
    
    const metrics = {
      summary: {
        totalOpportunities: opportunities.length,
        highQualityOpportunities: highQuality.length,
        qualityRatio: opportunities.length > 0 ? (highQuality.length / opportunities.length * 100).toFixed(1) + '%' : '0%'
      },
      spreadAnalysis: {
        average: spreads.length > 0 ? (spreads.reduce((a, b) => a + b, 0) / spreads.length).toFixed(4) + '%' : '0%',
        median: spreads.length > 0 ? spreads.sort((a, b) => a - b)[Math.floor(spreads.length / 2)].toFixed(4) + '%' : '0%',
        min: spreads.length > 0 ? Math.min(...spreads).toFixed(4) + '%' : '0%',
        max: spreads.length > 0 ? Math.max(...spreads).toFixed(4) + '%' : '0%',
        ranges: spreadRanges
      },
      volumeAnalysis: {
        average: volumes.length > 0 ? Math.round(volumes.reduce((a, b) => a + b, 0) / volumes.length).toLocaleString() : '0',
        total: volumes.length > 0 ? Math.round(volumes.reduce((a, b) => a + b, 0)).toLocaleString() : '0',
        median: volumes.length > 0 ? Math.round(volumes.sort((a, b) => a - b)[Math.floor(volumes.length / 2)]).toLocaleString() : '0'
      },
      dataFreshness: {
        averageAge: dataAges.length > 0 ? Math.round(dataAges.reduce((a, b) => a + b, 0) / dataAges.length / 1000) + 's' : '0s',
        maxAge: dataAges.length > 0 ? Math.round(Math.max(...dataAges) / 1000) + 's' : '0s',
        minAge: dataAges.length > 0 ? Math.round(Math.min(...dataAges) / 1000) + 's' : '0s'
      },
      distribution: {
        byType,
        topExchangePairs: Object.entries(byExchange)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 10)
          .reduce((acc, [key, value]) => {
            acc[key] = value;
            return acc;
          }, {} as Record<string, number>)
      },
      topOpportunities: opportunities
        .filter(o => Math.abs(o.spreadPercentage) > 0.5)
        .sort((a, b) => Math.abs(b.spreadPercentage) - Math.abs(a.spreadPercentage))
        .slice(0, 10)
        .map(o => ({
          symbol: o.symbol,
          type: o.type,
          spotExchange: o.spotExchange,
          futuresExchange: o.futuresExchange,
          spreadPercentage: Math.abs(o.spreadPercentage).toFixed(3) + '%',
          volume: Math.round(o.volume).toLocaleString()
        }))
    };
    
    console.log(`✅ Métricas calculadas em ${processingTime}ms`);
    
    res.json({
      success: true,
      metrics,
      processingTime,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao calcular métricas de qualidade:', error);
    
    res.status(500).json({
      success: false,
      error: 'Falha no cálculo de métricas',
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    });
  }
});

// Endpoints para sistema de alertas
app.get('/api/alerts', async (req, res) => {
  try {
    const { AlertService } = await import('./services/AlertService.js');
    const alertService = AlertService.getInstance();
    
    const limit = parseInt(req.query.limit as string) || 50;
    const alerts = alertService.getAlerts(limit);
    
    res.json({
      success: true,
      alerts,
      count: alerts.length,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao buscar alertas:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao buscar alertas',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

app.get('/api/alerts/rules', async (req, res) => {
  try {
    const { AlertService } = await import('./services/AlertService.js');
    const alertService = AlertService.getInstance();
    
    const rules = alertService.getRules();
    
    res.json({
      success: true,
      rules,
      count: rules.length,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao buscar regras de alerta:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao buscar regras de alerta',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

app.get('/api/alerts/stats', async (req, res) => {
  try {
    const { AlertService } = await import('./services/AlertService.js');
    const alertService = AlertService.getInstance();
    
    const stats = alertService.getStats();
    
    res.json({
      success: true,
      stats,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao buscar estatísticas de alertas:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao buscar estatísticas de alertas',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

app.post('/api/alerts/rules', async (req, res) => {
  try {
    const { AlertService } = await import('./services/AlertService.js');
    const alertService = AlertService.getInstance();
    
    const newRule = alertService.addRule(req.body);
    
    res.json({
      success: true,
      rule: newRule,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao criar regra de alerta:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao criar regra de alerta',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

app.delete('/api/alerts', async (req, res) => {
  try {
    const { AlertService } = await import('./services/AlertService.js');
    const alertService = AlertService.getInstance();
    
    alertService.clearAlerts();
    
    res.json({
      success: true,
      message: 'Alertas limpos com sucesso',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao limpar alertas:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao limpar alertas',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

// Endpoints para métricas de performance
app.get('/api/metrics/health', async (req, res) => {
  try {
    const { MetricsService } = await import('./services/MetricsService.js');
    const metricsService = MetricsService.getInstance();
    
    const health = metricsService.getSystemHealth();
    
    res.json({
      success: true,
      health,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao buscar saúde do sistema:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao buscar saúde do sistema',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

app.get('/api/metrics/business', async (req, res) => {
  try {
    const { MetricsService } = await import('./services/MetricsService.js');
    const metricsService = MetricsService.getInstance();
    
    const businessMetrics = metricsService.getBusinessMetrics();
    
    res.json({
      success: true,
      metrics: businessMetrics,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao buscar métricas de negócio:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao buscar métricas de negócio',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

app.get('/api/metrics/performance', async (req, res) => {
  try {
    const { MetricsService } = await import('./services/MetricsService.js');
    const metricsService = MetricsService.getInstance();
    
    const latencyMetrics = metricsService.getLatencyMetrics();
    const summary = metricsService.getMetricsSummary();
    
    res.json({
      success: true,
      latency: latencyMetrics,
      summary,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao buscar métricas de performance:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao buscar métricas de performance',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

app.get('/api/metrics/all', async (req, res) => {
  try {
    const { MetricsService } = await import('./services/MetricsService.js');
    const metricsService = MetricsService.getInstance();
    
    const category = req.query.category as string;
    const limit = parseInt(req.query.limit as string) || 100;
    
    const metrics = metricsService.getMetrics(category as any, limit);
    
    res.json({
      success: true,
      metrics,
      count: metrics.length,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro ao buscar métricas:', error);
    res.status(500).json({
      success: false,
      error: 'Falha ao buscar métricas',
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

// Middleware de tratamento de erros
app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('❌ Erro não tratado:', error);
  
  res.status(500).json({
    success: false,
    error: 'Erro interno do servidor',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Erro interno',
    timestamp: new Date().toISOString()
  });
});

// Middleware para rotas não encontradas
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Rota não encontrada',
    message: `Endpoint ${req.method} ${req.originalUrl} não existe`,
    timestamp: new Date().toISOString()
  });
});

// OTIMIZAÇÃO: Criar servidor HTTP para suportar WebSocket
const server = http.createServer(app);

// OTIMIZAÇÃO: Inicializar WebSocket Service
const webSocketService = WebSocketService.getInstance();
webSocketService.initialize(server, parseInt(process.env.WS_PORT || '5001'));

// OTIMIZAÇÃO: Inicializar Performance Monitor
const performanceMonitor = PerformanceMonitor.getInstance();

// OTIMIZAÇÃO: Inicializar Feature Flag Service
const featureFlagService = FeatureFlagService.getInstance();

// Iniciar servidor
server.listen(PORT, () => {
  console.log(`🚀 Servidor backend iniciado na porta ${PORT}`);
  console.log(`⚡ WebSocket servidor na porta ${process.env.WS_PORT || '5001'}`);
  console.log(`📊 APIs reais: ${process.env.ENABLE_REAL_APIS === 'true' ? 'ATIVADAS' : 'DESATIVADAS'}`);
  console.log(`🌐 CORS origin: ${process.env.CORS_ORIGIN || 'http://localhost:5002'}`);
  console.log(`⚡ Rate limit: ${RATE_LIMIT_REQUESTS} requests/${RATE_LIMIT_WINDOW/1000}s`);
  console.log(`📈 Endpoints disponíveis:`);
  console.log(`   GET /health - Status do servidor`);
  console.log(`   GET /api/exchanges/data - Dados de todas as exchanges`);
  console.log(`   GET /api/arbitrage/opportunities - Oportunidades de arbitragem`);
  console.log(`   GET /api/exchanges/:exchange - Dados de exchange específica`);
  console.log(`   GET /api/stats - Estatísticas do sistema`);
  console.log(`   WS  /ws - WebSocket para streaming em tempo real`);
});

// OTIMIZAÇÃO: Graceful shutdown com WebSocket
const gracefulShutdown = () => {
  console.log('🛑 Encerrando servidor graciosamente...');

  // Fechar WebSocket primeiro
  webSocketService.shutdown();

  // Fechar servidor HTTP
  server.close(() => {
    console.log('✅ Servidor encerrado com sucesso');
    process.exit(0);
  });

  // Forçar encerramento após 10 segundos
  setTimeout(() => {
    console.log('⚠️ Forçando encerramento...');
    process.exit(1);
  }, 10000);
};

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

export default app;