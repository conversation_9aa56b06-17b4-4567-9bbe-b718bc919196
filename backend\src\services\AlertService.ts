import { ArbitrageOpportunity } from '../types';

export interface AlertRule {
  id: string;
  name: string;
  symbol?: string;
  exchange?: string;
  minSpread: number;
  maxSpread?: number;
  type?: 'spot-futures' | 'futures-futures';
  enabled: boolean;
  lastTriggered?: number;
  cooldownMinutes: number;
}

export interface Alert {
  id: string;
  ruleId: string;
  opportunity: ArbitrageOpportunity;
  timestamp: number;
  message: string;
  severity: 'low' | 'medium' | 'high';
}

export class AlertService {
  private static instance: AlertService;
  private rules: AlertRule[] = [];
  private alerts: Alert[] = [];
  private maxAlerts = 1000;

  private constructor() {
    this.initializeDefaultRules();
  }

  public static getInstance(): AlertService {
    if (!AlertService.instance) {
      AlertService.instance = new AlertService();
    }
    return AlertService.instance;
  }

  private initializeDefaultRules(): void {
    this.rules = [
      {
        id: 'high-spread-btc',
        name: 'BTC High Spread Alert',
        symbol: 'BTC/USDT',
        minSpread: 1.0,
        enabled: true,
        cooldownMinutes: 5
      },
      {
        id: 'high-spread-eth',
        name: 'ETH High Spread Alert',
        symbol: 'ETH/USDT',
        minSpread: 0.8,
        enabled: true,
        cooldownMinutes: 5
      },
      {
        id: 'general-high-spread',
        name: 'General High Spread Alert',
        minSpread: 2.0,
        enabled: true,
        cooldownMinutes: 10
      },
      {
        id: 'futures-arbitrage',
        name: 'Futures-Futures Arbitrage',
        type: 'futures-futures',
        minSpread: 1.5,
        enabled: true,
        cooldownMinutes: 5
      }
    ];
  }

  public checkOpportunities(opportunities: ArbitrageOpportunity[]): Alert[] {
    const newAlerts: Alert[] = [];
    const now = Date.now();

    for (const opportunity of opportunities) {
      for (const rule of this.rules) {
        if (!rule.enabled) continue;

        // Verificar cooldown
        if (rule.lastTriggered && (now - rule.lastTriggered) < (rule.cooldownMinutes * 60 * 1000)) {
          continue;
        }

        // Verificar se a oportunidade atende aos critérios da regra
        if (this.matchesRule(opportunity, rule)) {
          const alert = this.createAlert(rule, opportunity);
          newAlerts.push(alert);
          
          // Atualizar último trigger
          rule.lastTriggered = now;
        }
      }
    }

    // Adicionar novos alertas à lista
    this.alerts.unshift(...newAlerts);
    
    // Manter apenas os últimos alertas
    if (this.alerts.length > this.maxAlerts) {
      this.alerts = this.alerts.slice(0, this.maxAlerts);
    }

    return newAlerts;
  }

  private matchesRule(opportunity: ArbitrageOpportunity, rule: AlertRule): boolean {
    // Verificar spread mínimo
    if (Math.abs(opportunity.spreadPercentage) < rule.minSpread) {
      return false;
    }

    // Verificar spread máximo se definido
    if (rule.maxSpread && Math.abs(opportunity.spreadPercentage) > rule.maxSpread) {
      return false;
    }

    // Verificar símbolo se definido
    if (rule.symbol && opportunity.symbol !== rule.symbol) {
      return false;
    }

    // Verificar exchange se definido
    if (rule.exchange && 
        opportunity.spotExchange !== rule.exchange && 
        opportunity.futuresExchange !== rule.exchange) {
      return false;
    }

    // Verificar tipo se definido
    if (rule.type && opportunity.type !== rule.type) {
      return false;
    }

    return true;
  }

  private createAlert(rule: AlertRule, opportunity: ArbitrageOpportunity): Alert {
    const severity = this.calculateSeverity(opportunity.spreadPercentage);
    const message = this.generateAlertMessage(rule, opportunity);

    return {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ruleId: rule.id,
      opportunity,
      timestamp: Date.now(),
      message,
      severity
    };
  }

  private calculateSeverity(spreadPercentage: number): 'low' | 'medium' | 'high' {
    const absSpread = Math.abs(spreadPercentage);
    
    if (absSpread >= 3.0) return 'high';
    if (absSpread >= 1.5) return 'medium';
    return 'low';
  }

  private generateAlertMessage(rule: AlertRule, opportunity: ArbitrageOpportunity): string {
    const spread = Math.abs(opportunity.spreadPercentage).toFixed(3);
    const action = opportunity.spreadPercentage > 0 ? 'Buy' : 'Sell';
    
    return `🚨 ${rule.name}: ${opportunity.symbol} - ${spread}% spread detected! ` +
           `${action} on ${opportunity.spotExchange} (${opportunity.spotPrice.toFixed(6)}) ` +
           `vs ${opportunity.futuresExchange} (${opportunity.futuresPrice.toFixed(6)})`;
  }

  // Métodos públicos para gerenciar regras
  public getRules(): AlertRule[] {
    return [...this.rules];
  }

  public getAlerts(limit: number = 50): Alert[] {
    return this.alerts.slice(0, limit);
  }

  public addRule(rule: Omit<AlertRule, 'id'>): AlertRule {
    const newRule: AlertRule = {
      ...rule,
      id: `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
    
    this.rules.push(newRule);
    return newRule;
  }

  public updateRule(id: string, updates: Partial<AlertRule>): boolean {
    const ruleIndex = this.rules.findIndex(r => r.id === id);
    if (ruleIndex === -1) return false;

    this.rules[ruleIndex] = { ...this.rules[ruleIndex], ...updates };
    return true;
  }

  public deleteRule(id: string): boolean {
    const ruleIndex = this.rules.findIndex(r => r.id === id);
    if (ruleIndex === -1) return false;

    this.rules.splice(ruleIndex, 1);
    return true;
  }

  public clearAlerts(): void {
    this.alerts = [];
  }

  public getStats(): {
    totalRules: number;
    activeRules: number;
    totalAlerts: number;
    recentAlerts: number;
  } {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);

    return {
      totalRules: this.rules.length,
      activeRules: this.rules.filter(r => r.enabled).length,
      totalAlerts: this.alerts.length,
      recentAlerts: this.alerts.filter(a => a.timestamp > oneHourAgo).length
    };
  }
}