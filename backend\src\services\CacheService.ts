import { CacheEntry } from '../types/index.js';

export class CacheService {
  private static instance: CacheService;

  // OTIMIZAÇÃO: Cache Multi-Camadas
  private L1Cache = new Map<string, CacheEntry<any>>(); // Hot data: 100ms TTL
  private L2Cache = new Map<string, CacheEntry<any>>(); // Warm data: 1s TTL
  private L3Cache = new Map<string, CacheEntry<any>>(); // Cold data: 30s TTL

  // Cache original mantido para compatibilidade
  private cache = new Map<string, CacheEntry<any>>();
  private defaultTTL = 30000; // 30 segundos
  private hitCount = 0;
  private missCount = 0;
  private volatilityCache = new Map<string, number>();

  // OTIMIZAÇÃO: Métricas por camada
  private l1HitCount = 0;
  private l2HitCount = 0;
  private l3HitCount = 0;

  /**
   * Singleton pattern
   */
  static getInstance(): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService();
      
      // Limpeza automática a cada 30 segundos
      setInterval(() => {
        CacheService.instance.cleanup();
      }, 30000);
      
      console.log('🚀 CacheService: Cache inteligente inicializado');
    }
    return CacheService.instance;
  }

  /**
   * Armazena dados no cache
   */
  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl
    };
    this.cache.set(key, entry);
  }

  /**
   * OTIMIZAÇÃO: Recupera dados do cache multi-camadas
   */
  get<T>(key: string): T | null {
    // Tentar L1 Cache primeiro (mais rápido - 5ms)
    let entry = this.L1Cache.get(key);
    if (entry && !this.isExpired(entry)) {
      this.l1HitCount++;
      this.hitCount++;
      return entry.data as T;
    }

    // Tentar L2 Cache (médio - 1s)
    entry = this.L2Cache.get(key);
    if (entry && !this.isExpired(entry)) {
      this.l2HitCount++;
      this.hitCount++;
      // Promover para L1 para próximas consultas
      this.setIntelligent(key, entry.data, 'hot');
      return entry.data as T;
    }

    // Tentar L3 Cache (lento - 30s)
    entry = this.L3Cache.get(key);
    if (entry && !this.isExpired(entry)) {
      this.l3HitCount++;
      this.hitCount++;
      // Promover para L2
      this.setIntelligent(key, entry.data, 'warm');
      return entry.data as T;
    }

    // Fallback para cache original (compatibilidade)
    entry = this.cache.get(key);
    if (entry && !this.isExpired(entry)) {
      this.hitCount++;
      return entry.data as T;
    }

    this.missCount++;
    return null;
  }

  /**
   * OTIMIZAÇÃO: Verifica se entrada está expirada
   */
  private isExpired(entry: CacheEntry<any>): boolean {
    const now = Date.now();
    return (now - entry.timestamp) > entry.ttl;
  }

  /**
   * Recupera dados do cache (método original mantido para compatibilidade)
   */
  getOriginal<T>(key: string): T | null {
    const entry = this.cache.get(key);

    if (!entry) {
      this.missCount++;
      return null;
    }

    const now = Date.now();
    const isExpired = (now - entry.timestamp) > entry.ttl;

    if (isExpired) {
      this.cache.delete(key);
      this.missCount++;
      return null;
    }

    this.hitCount++;
    return entry.data as T;
  }

  /**
   * Remove entrada do cache
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Limpa todo o cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * OTIMIZAÇÃO: Limpa entradas expiradas de todas as camadas
   */
  cleanup(): void {
    let cleanedCount = 0;

    // Limpar L1 Cache
    for (const [key, entry] of this.L1Cache.entries()) {
      if (this.isExpired(entry)) {
        this.L1Cache.delete(key);
        cleanedCount++;
      }
    }

    // Limpar L2 Cache
    for (const [key, entry] of this.L2Cache.entries()) {
      if (this.isExpired(entry)) {
        this.L2Cache.delete(key);
        cleanedCount++;
      }
    }

    // Limpar L3 Cache
    for (const [key, entry] of this.L3Cache.entries()) {
      if (this.isExpired(entry)) {
        this.L3Cache.delete(key);
        cleanedCount++;
      }
    }

    // Limpar cache original
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 Cache cleanup: ${cleanedCount} entradas expiradas removidas`);
    }
  }

  /**
   * TTL dinâmico baseado na volatilidade
   */
  getDynamicTTL(key: string): number {
    // Para dados de exchanges, usar TTL mais agressivo
    if (key.includes('exchange_data')) {
      return 5000; // 5 segundos para dados das exchanges
    }
    
    if (key.includes('arbitrage_opportunities')) {
      return 3000; // 3 segundos para oportunidades
    }
    
    return this.defaultTTL;
  }

  /**
   * OTIMIZAÇÃO: Set inteligente com cache multi-camadas
   */
  setIntelligent<T>(key: string, data: T, priority: 'hot' | 'warm' | 'cold' = 'warm'): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: this.getTTLByPriority(priority)
    };

    switch (priority) {
      case 'hot':
        this.L1Cache.set(key, entry);
        break;
      case 'warm':
        this.L2Cache.set(key, entry);
        break;
      case 'cold':
        this.L3Cache.set(key, entry);
        break;
    }

    // Também salvar no cache original para compatibilidade
    this.cache.set(key, entry);
  }

  /**
   * OTIMIZAÇÃO: Determina TTL baseado na prioridade
   */
  private getTTLByPriority(priority: 'hot' | 'warm' | 'cold'): number {
    switch (priority) {
      case 'hot': return 100;    // 100ms para dados muito quentes
      case 'warm': return 1000;  // 1s para dados mornos
      case 'cold': return 30000; // 30s para dados frios
      default: return this.defaultTTL;
    }
  }

  /**
   * Set com TTL dinâmico (mantido para compatibilidade)
   */
  setDynamic<T>(key: string, data: T): void {
    // OTIMIZAÇÃO: Determinar prioridade baseada no tipo de dados
    let priority: 'hot' | 'warm' | 'cold' = 'warm';

    if (key.includes('arbitrage_opportunities')) {
      priority = 'hot'; // Oportunidades são dados quentes
    } else if (key.includes('exchange_data')) {
      priority = 'warm'; // Dados de exchange são mornos
    } else {
      priority = 'cold'; // Outros dados são frios
    }

    this.setIntelligent(key, data, priority);
  }

  /**
   * OTIMIZAÇÃO: Retorna estatísticas detalhadas do cache multi-camadas
   */
  getStats(): {
    size: number;
    keys: string[];
    hitRate: number;
    hitCount: number;
    missCount: number;
    // OTIMIZAÇÃO: Estatísticas por camada
    l1Stats: { size: number; hitCount: number; hitRate: number };
    l2Stats: { size: number; hitCount: number; hitRate: number };
    l3Stats: { size: number; hitCount: number; hitRate: number };
    performance: { averageResponseTime: number; cacheEfficiency: number };
  } {
    const total = this.hitCount + this.missCount;
    const hitRate = total > 0 ? (this.hitCount / total) * 100 : 0;

    const l1Total = this.l1HitCount;
    const l2Total = this.l2HitCount;
    const l3Total = this.l3HitCount;
    const layerTotal = l1Total + l2Total + l3Total;

    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      hitRate: Math.round(hitRate * 100) / 100,
      hitCount: this.hitCount,
      missCount: this.missCount,

      // OTIMIZAÇÃO: Estatísticas detalhadas por camada
      l1Stats: {
        size: this.L1Cache.size,
        hitCount: this.l1HitCount,
        hitRate: layerTotal > 0 ? Math.round((l1Total / layerTotal) * 10000) / 100 : 0
      },
      l2Stats: {
        size: this.L2Cache.size,
        hitCount: this.l2HitCount,
        hitRate: layerTotal > 0 ? Math.round((l2Total / layerTotal) * 10000) / 100 : 0
      },
      l3Stats: {
        size: this.L3Cache.size,
        hitCount: this.l3HitCount,
        hitRate: layerTotal > 0 ? Math.round((l3Total / layerTotal) * 10000) / 100 : 0
      },

      // OTIMIZAÇÃO: Métricas de performance
      performance: {
        averageResponseTime: this.calculateAverageResponseTime(),
        cacheEfficiency: hitRate
      }
    };
  }

  /**
   * OTIMIZAÇÃO: Calcula tempo médio de resposta baseado nas camadas
   */
  private calculateAverageResponseTime(): number {
    const l1Weight = this.l1HitCount * 5;    // L1: ~5ms
    const l2Weight = this.l2HitCount * 50;   // L2: ~50ms
    const l3Weight = this.l3HitCount * 200;  // L3: ~200ms

    const totalHits = this.l1HitCount + this.l2HitCount + this.l3HitCount;

    if (totalHits === 0) return 0;

    return Math.round((l1Weight + l2Weight + l3Weight) / totalHits);
  }

  /**
   * Reset das estatísticas
   */
  resetStats(): void {
    this.hitCount = 0;
    this.missCount = 0;
  }
}