import { CacheEntry } from '../types/index.js';

export class CacheService {
  private static instance: CacheService;
  private cache = new Map<string, CacheEntry<any>>();
  private defaultTTL = 30000; // 30 segundos
  private hitCount = 0;
  private missCount = 0;
  private volatilityCache = new Map<string, number>();

  /**
   * Singleton pattern
   */
  static getInstance(): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService();
      
      // Limpeza automática a cada 30 segundos
      setInterval(() => {
        CacheService.instance.cleanup();
      }, 30000);
      
      console.log('🚀 CacheService: Cache inteligente inicializado');
    }
    return CacheService.instance;
  }

  /**
   * Armazena dados no cache
   */
  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl
    };
    this.cache.set(key, entry);
  }

  /**
   * Recupera dados do cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.missCount++;
      return null;
    }

    const now = Date.now();
    const isExpired = (now - entry.timestamp) > entry.ttl;

    if (isExpired) {
      this.cache.delete(key);
      this.missCount++;
      return null;
    }

    this.hitCount++;
    return entry.data as T;
  }

  /**
   * Remove entrada do cache
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Limpa todo o cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Limpa entradas expiradas
   */
  cleanup(): void {
    const now = Date.now();
    
    for (const [key, entry] of this.cache.entries()) {
      const isExpired = (now - entry.timestamp) > entry.ttl;
      if (isExpired) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * TTL dinâmico baseado na volatilidade
   */
  getDynamicTTL(key: string): number {
    // Para dados de exchanges, usar TTL mais agressivo
    if (key.includes('exchange_data')) {
      return 5000; // 5 segundos para dados das exchanges
    }
    
    if (key.includes('arbitrage_opportunities')) {
      return 3000; // 3 segundos para oportunidades
    }
    
    return this.defaultTTL;
  }

  /**
   * Set com TTL dinâmico
   */
  setDynamic<T>(key: string, data: T): void {
    const ttl = this.getDynamicTTL(key);
    this.set(key, data, ttl);
  }

  /**
   * Retorna estatísticas do cache
   */
  getStats(): { 
    size: number; 
    keys: string[]; 
    hitRate: number;
    hitCount: number;
    missCount: number;
  } {
    const total = this.hitCount + this.missCount;
    const hitRate = total > 0 ? (this.hitCount / total) * 100 : 0;
    
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      hitRate: Math.round(hitRate * 100) / 100,
      hitCount: this.hitCount,
      missCount: this.missCount
    };
  }

  /**
   * Reset das estatísticas
   */
  resetStats(): void {
    this.hitCount = 0;
    this.missCount = 0;
  }
}