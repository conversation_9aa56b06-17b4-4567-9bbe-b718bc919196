import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { HMACAuth } from './HMACAuth.js';
import { CacheService } from './CacheService.js';
import { AlertService } from './AlertService.js';
import { MetricsService } from './MetricsService.js';
import type {
  ExchangeData,
  SpotData,
  FuturesData,
  ArbitrageOpportunity,
  ExchangeConfig,
  HMACConfig
} from '../types/index.js';

// Tipos específicos para respostas das APIs
interface GateioSpotTicker {
  currency_pair: string;
  last: string;
  lowest_ask: string;
  highest_bid: string;
  change_percentage: string;
  base_volume: string;
  quote_volume: string;
  high_24h: string;
  low_24h: string;
}

interface GateioFuturesTicker {
  contract: string;
  last: string;
  change_percentage: string;
  total_size: string;
  volume_24h: string;
  volume_24h_base: string;
  volume_24h_quote: string;
  volume_24h_settle: string;
  mark_price: string;
  funding_rate: string;
  funding_rate_indicative: string;
  index_price: string;
  quanto_base_rate: string;
  basis_rate: string;
  basis_value: string;
  lowest_ask: string;
  highest_bid: string;
  orderbook_id: number;
  trade_id: number;
  trade_size: number;
  position_size: number;
  auto_deleveraging_rank: number;
}

interface MexcSpotTicker {
  symbol: string;
  priceChange: string;
  priceChangePercent: string;
  weightedAvgPrice: string;
  prevClosePrice: string;
  lastPrice: string;
  lastQty: string;
  bidPrice: string;
  askPrice: string;
  openPrice: string;
  highPrice: string;
  lowPrice: string;
  volume: string;
  quoteVolume: string;
  openTime: number;
  closeTime: number;
  count: number;
}

interface MexcFuturesTicker {
  symbol: string;
  lastPrice: string;
  bidPrice: string;
  askPrice: string;
  volume24: string;  // Campo correto é 'volume24', não 'volume24h'
  amount24h: string;
  holdVol: string;
  lower24hPrice: string;
  high24hPrice: string;
  riseFallRate: string;
  riseFallValue: string;
  indexPrice: string;
  fairPrice: string;
  fundingRate: string;
  maxBidPrice: string;
  minAskPrice: string;
  timestamp: number;
}

interface BitgetSpotTicker {
  symbol: string;
  high24h: string;
  low24h: string;
  close: string;
  quoteVol: string;
  baseVol: string;
  usdtVol: string;
  ts: string;
  buyOne: string;
  sellOne: string;
  bidSz: string;
  askSz: string;
  openUtc: string;
  changeUtc24h: string;
  change24h: string;
}

interface BitgetFuturesTicker {
  symbol: string;
  last: string;  // Campo correto é 'last', não 'lastPr'
  bestAsk: string;
  bestBid: string;
  bidSz: string;
  askSz: string;
  high24h: string;
  low24h: string;
  timestamp: string;
  priceChangePercent: string;  // Campo correto é 'priceChangePercent', não 'change24h'
  baseVolume: string;
  quoteVolume: string;
  usdtVolume: string;
  openUtc: string;
  chgUtc: string;
  indexPrice: string;
  fundingRate: string;
  holdingAmount: string;
}

export class ExchangeService {
  private gateioClient: AxiosInstance;
  private mexcClient: AxiosInstance;
  private mexcFuturesClient: AxiosInstance;
  private bitgetClient: AxiosInstance;
  private cache: CacheService;
  private alertService: AlertService;
  private metricsService: MetricsService;

  /**
   * Normaliza símbolos para formato consistente
   */
  private normalizeSymbol(symbol: string): string {
    // Remove sufixos específicos das exchanges
    let cleanSymbol = symbol
      .replace('_UMCBL', '')  // Bitget futures
      .replace('_USDT', '/USDT')  // Padrão comum
      .replace('_USDC', '/USDC')  // Padrão comum
      .replace('_BTC', '/BTC')    // Padrão comum
      .replace('_ETH', '/ETH');   // Padrão comum

    // Se já tem barra, retorna como está
    if (cleanSymbol.includes('/')) {
      return cleanSymbol;
    }

    // Para símbolos sem barra, tenta identificar a base e quote
    // Padrões comuns: BTCUSDT, ETHUSDT, etc.
    if (cleanSymbol.endsWith('USDT')) {
      const base = cleanSymbol.slice(0, -4);
      return `${base}/USDT`;
    }
    if (cleanSymbol.endsWith('USDC')) {
      const base = cleanSymbol.slice(0, -4);
      return `${base}/USDC`;
    }
    if (cleanSymbol.endsWith('BTC')) {
      const base = cleanSymbol.slice(0, -3);
      return `${base}/BTC`;
    }
    if (cleanSymbol.endsWith('ETH')) {
      const base = cleanSymbol.slice(0, -3);
      return `${base}/ETH`;
    }

    // Fallback: retorna como está
    return cleanSymbol;
  }

  constructor() {
    this.cacheService = CacheService.getInstance();
    this.alertService = AlertService.getInstance();
    this.metricsService = MetricsService.getInstance();

    // Configurar clientes HTTP para cada exchange
    this.gateioClient = axios.create({
      baseURL: process.env.GATEIO_BASE_URL || 'https://api.gateio.ws',
      timeout: parseInt(process.env.REQUEST_TIMEOUT || '10000'),
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'User-Agent': 'crypto-arbitrage-bot/1.0'
      }
    });

    this.mexcClient = axios.create({
      baseURL: process.env.MEXC_BASE_URL || 'https://api.mexc.com',
      timeout: parseInt(process.env.REQUEST_TIMEOUT || '10000'),
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'User-Agent': 'crypto-arbitrage-bot/1.0'
      }
    });

    this.mexcFuturesClient = axios.create({
      baseURL: process.env.MEXC_FUTURES_BASE_URL || 'https://contract.mexc.com',
      timeout: parseInt(process.env.REQUEST_TIMEOUT || '10000'),
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'User-Agent': 'crypto-arbitrage-bot/1.0'
      }
    });

    this.bitgetClient = axios.create({
      baseURL: process.env.BITGET_BASE_URL || 'https://api.bitget.com',
      timeout: parseInt(process.env.REQUEST_TIMEOUT || '10000'),
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'User-Agent': 'crypto-arbitrage-bot/1.0'
      }
    });
  }

  /**
   * Coleta dados completos de todas as exchanges
   */
  async getAllExchangeData(): Promise<{
    allSpotData: ExchangeData[];
    allFuturesData: ExchangeData[];
    metadata: any;
  }> {
    const cacheKey = 'all_exchange_data';
    const cached = this.cacheService.get(cacheKey) as {
      allSpotData: ExchangeData[];
      allFuturesData: ExchangeData[];
      metadata: any;
    } | null;

    if (cached) {
      console.log('📦 Cache HIT - Retornando dados do cache');
      return cached;
    }
    
    console.log('📦 Cache MISS - Coletando dados das APIs');

    console.log('🚀 Coletando dados reais de todas as exchanges...');
    const startTime = Date.now();

    try {
      // Coleta paralela de todas as exchanges
      const [gateioData, mexcData, bitgetData] = await Promise.allSettled([
        this.fetchGateioData(),
        this.fetchMexcData(),
        this.fetchBitgetData()
      ]);

      const allSpotData: ExchangeData[] = [];
      const allFuturesData: ExchangeData[] = [];
      const errors: string[] = [];

      // Processar resultados do Gate.io
      if (gateioData.status === 'fulfilled') {
        allSpotData.push(gateioData.value.spot);
        allFuturesData.push(gateioData.value.futures);
        console.log(`✅ Gate.io: ${gateioData.value.spot.spot.length} spot + ${gateioData.value.futures.futures.length} futures`);
      } else {
        errors.push(`Gate.io: ${gateioData.reason}`);
        console.error('❌ Gate.io falhou:', gateioData.reason);
      }

      // Processar resultados do MEXC
      if (mexcData.status === 'fulfilled') {
        allSpotData.push(mexcData.value.spot);
        allFuturesData.push(mexcData.value.futures);
        console.log(`✅ MEXC: ${mexcData.value.spot.spot.length} spot + ${mexcData.value.futures.futures.length} futures`);
      } else {
        errors.push(`MEXC: ${mexcData.reason}`);
        console.error('❌ MEXC falhou:', mexcData.reason);
      }

      // Processar resultados do Bitget
      if (bitgetData.status === 'fulfilled') {
        allSpotData.push(bitgetData.value.spot);
        allFuturesData.push(bitgetData.value.futures);
        console.log(`✅ Bitget: ${bitgetData.value.spot.spot.length} spot + ${bitgetData.value.futures.futures.length} futures`);
      } else {
        errors.push(`Bitget: ${bitgetData.reason}`);
        console.error('❌ Bitget falhou:', bitgetData.reason);
      }

      const processingTime = Date.now() - startTime;
      const totalSpotPairs = allSpotData.reduce((sum, exchange) => sum + exchange.spot.length, 0);
      const totalFuturesPairs = allFuturesData.reduce((sum, exchange) => sum + exchange.futures.length, 0);

      const result = {
        allSpotData,
        allFuturesData,
        metadata: {
          totalPairs: totalSpotPairs + totalFuturesPairs,
          spotPairs: totalSpotPairs,
          futuresPairs: totalFuturesPairs,
          processingTime,
          timestamp: new Date(),
          errors,
          warnings: errors.length > 0 ? ['Algumas exchanges falharam'] : [],
          exchanges: {
            gateio: {
              spot: gateioData.status === 'fulfilled' ? gateioData.value.spot.spot.length : 0,
              futures: gateioData.status === 'fulfilled' ? gateioData.value.futures.futures.length : 0,
              status: gateioData.status
            },
            mexc: {
              spot: mexcData.status === 'fulfilled' ? mexcData.value.spot.spot.length : 0,
              futures: mexcData.status === 'fulfilled' ? mexcData.value.futures.futures.length : 0,
              status: mexcData.status
            },
            bitget: {
              spot: bitgetData.status === 'fulfilled' ? bitgetData.value.spot.spot.length : 0,
              futures: bitgetData.status === 'fulfilled' ? bitgetData.value.futures.futures.length : 0,
              status: bitgetData.status
            }
          }
        }
      };

      console.log(`🎯 Coletados ${totalSpotPairs} spot + ${totalFuturesPairs} futures em ${processingTime}ms`);

      // Cache inteligente com TTL dinâmico
      this.cacheService.setDynamic(cacheKey, result);
      
      // Log de performance
      const cacheStats = this.cacheService.getStats();
      console.log(`📊 Cache Stats - Hit Rate: ${cacheStats.hitRate}%, Hits: ${cacheStats.hitCount}, Misses: ${cacheStats.missCount}`);

      return result;

    } catch (error) {
      console.error('❌ Erro na coleta de dados:', error);
      throw new Error(`Falha na coleta de dados: ${error}`);
    }
  }

  /**
   * Coleta dados do Gate.io
   */
  private async fetchGateioData(): Promise<{
    spot: ExchangeData;
    futures: ExchangeData;
  }> {
    const spotData: SpotData[] = [];
    const futuresData: FuturesData[] = [];

    try {
      // Fetch spot data
      const spotResponse = await this.gateioClient.get('/api/v4/spot/tickers');

      if (Array.isArray(spotResponse.data)) {
        for (const ticker of spotResponse.data.slice(0, 1000)) {
          try {
            const normalized = this.normalizeGateioSpotData(ticker);
            if (normalized) spotData.push(normalized);
          } catch (error) {
            // Ignorar erros de normalização individual
          }
        }
      }

      // Fetch futures data
      const futuresResponse = await this.gateioClient.get('/api/v4/futures/usdt/tickers');

      if (Array.isArray(futuresResponse.data)) {
        for (const ticker of futuresResponse.data.slice(0, 500)) {
          try {
            const normalized = this.normalizeGateioFuturesData(ticker);
            if (normalized) futuresData.push(normalized);
          } catch (error) {
            // Ignorar erros de normalização individual
          }
        }
      }

    } catch (error) {
      console.error('Erro ao coletar dados do Gate.io:', error);
      throw error;
    }

    return {
      spot: {
        exchange: 'gateio',
        spot: spotData,
        futures: [],
        timestamp: Date.now(),
        status: 'success'
      },
      futures: {
        exchange: 'gateio',
        spot: [],
        futures: futuresData,
        timestamp: Date.now(),
        status: 'success'
      }
    };
  }

  /**
   * Coleta dados do MEXC
   */
  private async fetchMexcData(): Promise<{
    spot: ExchangeData;
    futures: ExchangeData;
  }> {
    const spotData: SpotData[] = [];
    const futuresData: FuturesData[] = [];

    console.log('[MEXC] Iniciando coleta de dados...');

    try {
      // Fetch spot data
      console.log('[MEXC] Fazendo requisição para spot data: /api/v3/ticker/24hr');
      const spotResponse = await this.mexcClient.get('/api/v3/ticker/24hr');

      console.log(`[MEXC] Spot response status: ${spotResponse.status}`);
      console.log(`[MEXC] Spot response data type: ${typeof spotResponse.data}`);
      console.log(`[MEXC] Spot response is array: ${Array.isArray(spotResponse.data)}`);

      if (spotResponse.data) {
        console.log(`[MEXC] Spot response length: ${Array.isArray(spotResponse.data) ? spotResponse.data.length : 'not array'}`);
        if (Array.isArray(spotResponse.data) && spotResponse.data.length > 0) {
          console.log('[MEXC] First spot ticker sample:', JSON.stringify(spotResponse.data[0], null, 2));

          // Procurar especificamente pelo NEIRO
          const neiroTicker = spotResponse.data.find(ticker => ticker.symbol === 'NEIROUSDT');
          if (neiroTicker) {
            console.log('[MEXC] NEIRO ticker found in response:', JSON.stringify(neiroTicker, null, 2));
          }
        }
      }

      if (Array.isArray(spotResponse.data)) {
        const tickersToProcess = spotResponse.data.slice(0, 1000);
        console.log(`[MEXC] Processing ${tickersToProcess.length} spot tickers...`);

        for (const ticker of tickersToProcess) {
          try {
            const normalized = this.normalizeMexcSpotData(ticker);
            if (normalized) {
              spotData.push(normalized);
            } else if (ticker.symbol === 'NEIROUSDT') {
              console.log(`[DEBUG] MEXC NEIRO: normalization returned null`);
            }
          } catch (error) {
            console.log(`[MEXC] Error normalizing spot ticker ${ticker?.symbol}:`, error);
          }
        }
        console.log(`[MEXC] Successfully normalized ${spotData.length} spot tickers`);
      } else {
        console.log('[MEXC] Spot response data is not an array!');
      }

      // Fetch futures data
      console.log('[MEXC] Fazendo requisição para futures data: /api/v1/contract/ticker');
      const futuresResponse = await this.mexcFuturesClient.get('/api/v1/contract/ticker');

      console.log(`[MEXC] Futures response status: ${futuresResponse.status}`);
      console.log(`[MEXC] Futures response data type: ${typeof futuresResponse.data}`);
      console.log(`[MEXC] Futures response structure:`, Object.keys(futuresResponse.data || {}));

      if (futuresResponse.data?.data) {
        console.log(`[MEXC] Futures data is array: ${Array.isArray(futuresResponse.data.data)}`);
        console.log(`[MEXC] Futures data length: ${Array.isArray(futuresResponse.data.data) ? futuresResponse.data.data.length : 'not array'}`);

        if (Array.isArray(futuresResponse.data.data) && futuresResponse.data.data.length > 0) {
          console.log('[MEXC] First futures ticker sample:', JSON.stringify(futuresResponse.data.data[0], null, 2));
        }
      }

      if (futuresResponse.data?.data && Array.isArray(futuresResponse.data.data)) {
        const tickersToProcess = futuresResponse.data.data.slice(0, 500);
        console.log(`[MEXC] Processing ${tickersToProcess.length} futures tickers...`);

        for (const ticker of tickersToProcess) {
          try {
            const normalized = this.normalizeMexcFuturesData(ticker);
            if (normalized) {
              futuresData.push(normalized);
            }
          } catch (error) {
            console.log(`[MEXC] Error normalizing futures ticker ${ticker?.symbol}:`, error);
          }
        }
        console.log(`[MEXC] Successfully normalized ${futuresData.length} futures tickers`);
      } else {
        console.log('[MEXC] Futures response data.data is not an array!');
      }

    } catch (error) {
      console.error('[MEXC] Erro ao coletar dados:', error);
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as any;
        console.error('[MEXC] Response status:', axiosError.response?.status);
        console.error('[MEXC] Response data:', axiosError.response?.data);
      }
      throw error;
    }

    console.log(`[MEXC] Coleta finalizada - Spot: ${spotData.length}, Futures: ${futuresData.length}`);

    return {
      spot: {
        exchange: 'mexc',
        spot: spotData,
        futures: [],
        timestamp: Date.now(),
        status: 'success'
      },
      futures: {
        exchange: 'mexc',
        spot: [],
        futures: futuresData,
        timestamp: Date.now(),
        status: 'success'
      }
    };
  }

  /**
   * Coleta dados do Bitget
   */
  private async fetchBitgetData(): Promise<{
    spot: ExchangeData;
    futures: ExchangeData;
  }> {
    const spotData: SpotData[] = [];
    const futuresData: FuturesData[] = [];

    console.log('[BITGET] Iniciando coleta de dados...');

    try {
      // Fetch spot data
      console.log('[BITGET] Fazendo requisição para spot data: /api/spot/v1/market/tickers');
      const spotResponse = await this.bitgetClient.get('/api/spot/v1/market/tickers');

      console.log(`[BITGET] Spot response status: ${spotResponse.status}`);
      console.log(`[BITGET] Spot response data type: ${typeof spotResponse.data}`);
      console.log(`[BITGET] Spot response structure:`, Object.keys(spotResponse.data || {}));

      if (spotResponse.data?.data) {
        console.log(`[BITGET] Spot data is array: ${Array.isArray(spotResponse.data.data)}`);
        console.log(`[BITGET] Spot data length: ${Array.isArray(spotResponse.data.data) ? spotResponse.data.data.length : 'not array'}`);

        if (Array.isArray(spotResponse.data.data) && spotResponse.data.data.length > 0) {
          console.log('[BITGET] First spot ticker sample:', JSON.stringify(spotResponse.data.data[0], null, 2));
        }
      }

      if (spotResponse.data?.data && Array.isArray(spotResponse.data.data)) {
        const tickersToProcess = spotResponse.data.data.slice(0, 1000);
        console.log(`[BITGET] Processing ${tickersToProcess.length} spot tickers...`);

        for (const ticker of tickersToProcess) {
          try {
            const normalized = this.normalizeBitgetSpotData(ticker);
            if (normalized) {
              spotData.push(normalized);
            }
          } catch (error) {
            console.log(`[BITGET] Error normalizing spot ticker ${ticker?.symbol}:`, error);
          }
        }
        console.log(`[BITGET] Successfully normalized ${spotData.length} spot tickers`);
      } else {
        console.log('[BITGET] Spot response data.data is not an array!');
      }

      // Fetch futures data
      console.log('[BITGET] Fazendo requisição para futures data: /api/mix/v1/market/tickers?productType=UMCBL');
      const futuresResponse = await this.bitgetClient.get('/api/mix/v1/market/tickers?productType=UMCBL');

      console.log(`[BITGET] Futures response status: ${futuresResponse.status}`);
      console.log(`[BITGET] Futures response data type: ${typeof futuresResponse.data}`);
      console.log(`[BITGET] Futures response structure:`, Object.keys(futuresResponse.data || {}));

      if (futuresResponse.data?.data) {
        console.log(`[BITGET] Futures data is array: ${Array.isArray(futuresResponse.data.data)}`);
        console.log(`[BITGET] Futures data length: ${Array.isArray(futuresResponse.data.data) ? futuresResponse.data.data.length : 'not array'}`);

        if (Array.isArray(futuresResponse.data.data) && futuresResponse.data.data.length > 0) {
          console.log('[BITGET] First futures ticker sample:', JSON.stringify(futuresResponse.data.data[0], null, 2));
        }
      }

      if (futuresResponse.data?.data && Array.isArray(futuresResponse.data.data)) {
        const tickersToProcess = futuresResponse.data.data.slice(0, 500);
        console.log(`[BITGET] Processing ${tickersToProcess.length} futures tickers...`);

        for (const ticker of tickersToProcess) {
          try {
            const normalized = this.normalizeBitgetFuturesData(ticker);
            if (normalized) {
              futuresData.push(normalized);
            }
          } catch (error) {
            console.log(`[BITGET] Error normalizing futures ticker ${ticker?.symbol}:`, error);
          }
        }
        console.log(`[BITGET] Successfully normalized ${futuresData.length} futures tickers`);
      } else {
        console.log('[BITGET] Futures response data.data is not an array!');
      }

    } catch (error) {
      console.error('[BITGET] Erro ao coletar dados:', error);
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as any;
        console.error('[BITGET] Response status:', axiosError.response?.status);
        console.error('[BITGET] Response data:', axiosError.response?.data);
      }
      throw error;
    }

    console.log(`[BITGET] Coleta finalizada - Spot: ${spotData.length}, Futures: ${futuresData.length}`);

    return {
      spot: {
        exchange: 'bitget',
        spot: spotData,
        futures: [],
        timestamp: Date.now(),
        status: 'success'
      },
      futures: {
        exchange: 'bitget',
        spot: [],
        futures: futuresData,
        timestamp: Date.now(),
        status: 'success'
      }
    };
  }

  /**
   * Normalização de dados Gate.io Spot
   */
  private normalizeGateioSpotData(ticker: GateioSpotTicker): SpotData | null {
    try {
      const price = parseFloat(ticker.last);
      const volume = parseFloat(ticker.base_volume);
      const change24h = parseFloat(ticker.change_percentage);
      const high24h = parseFloat(ticker.high_24h);
      const low24h = parseFloat(ticker.low_24h);
      const bid = parseFloat(ticker.highest_bid);
      const ask = parseFloat(ticker.lowest_ask);

      if (isNaN(price) || price < 0.000001 || isNaN(volume) || volume <= 0 || isNaN(bid) || isNaN(ask)) {
        return null;
      }

      return {
        symbol: this.normalizeSymbol(ticker.currency_pair.replace('_', '/')),
        price,
        volume,
        change24h,
        high24h,
        low24h,
        bid,
        ask,
        timestamp: Date.now()
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Normalização de dados Gate.io Futures
   */
  private normalizeGateioFuturesData(ticker: GateioFuturesTicker): FuturesData | null {
    try {
      const price = parseFloat(ticker.last);
      const volume = parseFloat(ticker.volume_24h_base);
      const change24h = parseFloat(ticker.change_percentage);
      const fundingRate = parseFloat(ticker.funding_rate);
      const bid = parseFloat(ticker.highest_bid);
      const ask = parseFloat(ticker.lowest_ask);

      // Debug para NEIRO
      if (ticker.contract === 'NEIRO_USDT') {
        console.log(`[DEBUG] GATEIO NEIRO: raw ticker=`, JSON.stringify(ticker, null, 2));
        console.log(`[DEBUG] GATEIO NEIRO: raw last="${ticker.last}", parsed price=${price}, bid=${bid}, ask=${ask}`);
      }

      if (isNaN(price) || price < 0.000001 || isNaN(volume) || volume <= 0 || isNaN(bid) || isNaN(ask)) {
        if (ticker.contract === 'NEIRO_USDT') {
          console.log(`[DEBUG] GATEIO NEIRO: REJECTED - price=${price}, volume=${volume}, bid=${bid}, ask=${ask}`);
        }
        return null;
      }

      return {
        symbol: this.normalizeSymbol(ticker.contract.replace('_', '/')),
        price,
        volume,
        change24h,
        high24h: price * 1.05, // Estimativa
        low24h: price * 0.95,  // Estimativa
        bid,
        ask,
        fundingRate,
        openInterest: parseFloat(ticker.total_size) || 0,
        timestamp: Date.now()
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Normalização de dados MEXC Spot
   */
  private normalizeMexcSpotData(ticker: MexcSpotTicker): SpotData | null {
    try {
      const price = parseFloat(ticker.lastPrice);
      const volume = parseFloat(ticker.volume);
      const change24h = parseFloat(ticker.priceChangePercent);
      const high24h = parseFloat(ticker.highPrice);
      const low24h = parseFloat(ticker.lowPrice);
      const bid = parseFloat(ticker.bidPrice);
      const ask = parseFloat(ticker.askPrice);

      // Debug para NEIRO
      if (ticker.symbol === 'NEIROUSDT') {
        console.log(`[DEBUG] MEXC NEIRO: raw ticker=`, JSON.stringify(ticker, null, 2));
        console.log(`[DEBUG] MEXC NEIRO: raw lastPrice="${ticker.lastPrice}", parsed price=${price}, bid=${bid}, ask=${ask}`);
      }

      if (isNaN(price) || isNaN(volume) || volume <= 0 || isNaN(bid) || isNaN(ask)) {
        if (ticker.symbol === 'NEIROUSDT') {
          console.log(`[DEBUG] MEXC NEIRO: REJECTED - price=${price}, volume=${volume}, bid=${bid}, ask=${ask}`);
        }
        return null;
      }

      // Log para preços muito pequenos
      if (price < 0.01 && ticker.symbol === 'NEIROUSDT') {
        console.log(`[DEBUG] MEXC NEIRO: ACCEPTED small price - price=${price}, bid=${bid}, ask=${ask}`);
      }

      return {
        symbol: this.normalizeSymbol(ticker.symbol),
        price,
        volume,
        change24h,
        high24h,
        low24h,
        bid,
        ask,
        timestamp: Date.now()
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Normalização de dados MEXC Futures
   */
  private normalizeMexcFuturesData(ticker: MexcFuturesTicker): FuturesData | null {
    try {
      console.log(`[MEXC] Normalizing futures ticker:`, JSON.stringify(ticker, null, 2));

      // Corrigindo os campos baseado na resposta real da API
      const price = parseFloat(ticker.lastPrice);
      const volume = parseFloat(ticker.volume24);  // Campo correto é 'volume24'
      const change24h = parseFloat(ticker.riseFallRate);
      const high24h = parseFloat(ticker.high24hPrice);  // Era ticker.high24hPrice
      const low24h = parseFloat(ticker.lower24hPrice);  // Era ticker.lower24hPrice
      const fundingRate = parseFloat(ticker.fundingRate);

      console.log(`[MEXC] Parsed values - price: ${price}, volume: ${volume}, change24h: ${change24h}`);

      if (isNaN(price) || price < 0.000001 || isNaN(volume) || volume <= 0) {
        console.log(`[MEXC] Invalid data - price: ${price}, volume: ${volume}`);
        return null;
      }

      const result = {
        symbol: this.normalizeSymbol(ticker.symbol),
        price,
        volume,
        change24h,
        high24h,
        low24h,
        bid: price * (1 - 0.0005), // Estimativa baseada em spread típico de 0.05%
        ask: price * (1 + 0.0005), // Estimativa baseada em spread típico de 0.05%
        fundingRate,
        openInterest: parseFloat(ticker.holdVol) || 0,
        timestamp: Date.now()
      };

      console.log(`[MEXC] Successfully normalized:`, result);
      return result;
    } catch (error) {
      console.log(`[MEXC] Error in normalizeMexcFuturesData:`, error);
      console.log(`[MEXC] Ticker data:`, ticker);
      return null;
    }
  }

  /**
   * Normalização de dados Bitget Spot
   */
  private normalizeBitgetSpotData(ticker: BitgetSpotTicker): SpotData | null {
    try {
      const price = parseFloat(ticker.close);
      const volume = parseFloat(ticker.baseVol);
      const change24h = parseFloat(ticker.change24h);
      const high24h = parseFloat(ticker.high24h);
      const low24h = parseFloat(ticker.low24h);

      if (isNaN(price) || price < 0.000001 || isNaN(volume) || volume <= 0) {
        return null;
      }

      return {
        symbol: this.normalizeSymbol(ticker.symbol),
        price,
        volume,
        change24h,
        high24h,
        low24h,
        timestamp: Date.now()
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Normalização de dados Bitget Futures
   */
  private normalizeBitgetFuturesData(ticker: BitgetFuturesTicker): FuturesData | null {
    try {
      console.log(`[BITGET] Normalizing futures ticker:`, JSON.stringify(ticker, null, 2));

      // Corrigindo os campos baseado na resposta real da API
      const price = parseFloat(ticker.last);  // Campo correto é 'last'
      const volume = parseFloat(ticker.baseVolume);
      const change24h = parseFloat(ticker.priceChangePercent);  // Campo correto é 'priceChangePercent'
      const high24h = parseFloat(ticker.high24h);
      const low24h = parseFloat(ticker.low24h);
      const fundingRate = parseFloat(ticker.fundingRate);

      console.log(`[BITGET] Parsed values - price: ${price}, volume: ${volume}, change24h: ${change24h}`);

      if (isNaN(price) || price < 0.000001 || isNaN(volume) || volume <= 0) {
        console.log(`[BITGET] Invalid data - price: ${price}, volume: ${volume}`);
        return null;
      }

      const result = {
        symbol: this.normalizeSymbol(ticker.symbol),
        price,
        volume,
        change24h,
        high24h,
        low24h,
        bid: price * (1 - 0.0005), // Estimativa baseada em spread típico de 0.05%
        ask: price * (1 + 0.0005), // Estimativa baseada em spread típico de 0.05%
        fundingRate,
        openInterest: parseFloat(ticker.holdingAmount) || 0,
        timestamp: Date.now()
      };

      console.log(`[BITGET] Successfully normalized:`, result);
      return result;
    } catch (error) {
      console.log(`[BITGET] Error in normalizeBitgetFuturesData:`, error);
      console.log(`[BITGET] Ticker data:`, ticker);
      return null;
    }
  }

  /**
   * Calcula oportunidades de arbitragem futures vs futures cross-exchange
   */
  private async calculateFuturesFuturesOpportunities(allFuturesData: (FuturesData & { exchange: string })[]): Promise<ArbitrageOpportunity[]> {
    const opportunities: ArbitrageOpportunity[] = [];
    const minSpread = parseFloat(process.env.MIN_SPREAD_PERCENTAGE || '0.05');
    const maxSpread = parseFloat(process.env.MAX_SPREAD_PERCENTAGE || '100.0');

    let futuresFuturesComparisons = 0;
    let futuresFuturesValidSpreads = 0;

    console.log(`[DEBUG] Calculando oportunidades futures vs futures...`);

    // Encontrar oportunidades entre futuros de diferentes exchanges
    for (let i = 0; i < allFuturesData.length; i++) {
      const futures1 = allFuturesData[i];

      for (let j = i + 1; j < allFuturesData.length; j++) {
        const futures2 = allFuturesData[j];

        // Verificar se é o mesmo símbolo mas exchanges diferentes
        if (futures1.symbol === futures2.symbol && futures1.exchange !== futures2.exchange) {
          futuresFuturesComparisons++;

          // Validação de qualidade dos dados
          if (!this.isValidOrderbookData(futures1) || !this.isValidOrderbookData(futures2)) {
            continue;
          }

          // Cálculo correto usando orderbook:
          // Para comprar futures1: usar ask (preço de venda)
          // Para vender futures2: usar bid (preço de compra)
          const spread = futures2.bid - futures1.ask;
          const spreadPercentage = (spread / futures1.ask) * 100;

          if (futuresFuturesComparisons <= 5) {
            console.log(`[DEBUG] Futures-Futures ${futures1.symbol}: ${futures1.exchange} ask=${futures1.ask}, ${futures2.exchange} bid=${futures2.bid}, spread=${spreadPercentage.toFixed(4)}%`);
          }

          // Validação de liquidez mínima
          const minVolumeUSD = 1000; // USD mínimo
          const effectiveVolume = this.calculateEffectiveVolume(futures1, futures2);

          // Filtrar spreads dentro do range válido com validações adicionais
          if (Math.abs(spreadPercentage) >= minSpread &&
            Math.abs(spreadPercentage) <= maxSpread &&
            effectiveVolume >= minVolumeUSD &&
            this.isReasonableSpread(spreadPercentage)) {

            futuresFuturesValidSpreads++;
            const currentTime = Date.now();
            const dataAge = Math.max(currentTime - futures1.timestamp, currentTime - futures2.timestamp);

            // Para futures vs futures, usar os preços corretos do orderbook
            const isPositiveSpread = spread > 0;
            const buyExchange = isPositiveSpread ? futures1.exchange : futures2.exchange;
            const sellExchange = isPositiveSpread ? futures2.exchange : futures1.exchange;
            const buyPrice = isPositiveSpread ? futures1.ask : futures2.ask;  // Preço que pagamos para comprar
            const sellPrice = isPositiveSpread ? futures2.bid : futures1.bid; // Preço que recebemos para vender

            opportunities.push({
              symbol: futures1.symbol,
              spotExchange: buyExchange,
              futuresExchange: sellExchange,
              spotPrice: buyPrice,   // Preço que pagamos (ask do lado de compra)
              futuresPrice: sellPrice, // Preço que recebemos (bid do lado de venda)
              spread: Math.abs(spread),
              spreadPercentage: Math.abs(spreadPercentage),
              volume: Math.min(futures1.volume, futures2.volume),
              fundingRate: isPositiveSpread ? futures2.fundingRate : futures1.fundingRate,
              profitPotential: Math.abs(spread) * Math.min(futures1.volume, futures2.volume),
              timestamp: Date.now(),
              dataAge: dataAge,
              type: 'futures-futures'
            });
          }
        }
      }
    }

    console.log(`[DEBUG] Futures-Futures comparisons: ${futuresFuturesComparisons}, Valid spreads: ${futuresFuturesValidSpreads}, Opportunities: ${opportunities.length}`);
    return opportunities;
  }

  /**
   * Calcula oportunidades de arbitragem cross-exchange (spot vs futures + futures vs futures)
   */
  async calculateArbitrageOpportunities(): Promise<ArbitrageOpportunity[]> {
    const cacheKey = 'arbitrage_opportunities';
    const cached = this.cacheService.get(cacheKey) as ArbitrageOpportunity[] | null;

    if (cached) {
      console.log('📦 Cache HIT - Retornando oportunidades do cache');
      return cached;
    }

    console.log('📦 Cache MISS - Calculando oportunidades');
    const startTime = Date.now();
    const data = await this.getAllExchangeData();
    const opportunities: ArbitrageOpportunity[] = [];

    // Combinar dados de spot e futures de todas as exchanges
    const allSpotData: (SpotData & { exchange: string })[] = [];
    const allFuturesData: (FuturesData & { exchange: string })[] = [];

    data.allSpotData.forEach(exchangeData => {
      exchangeData.spot.forEach(spot => {
        allSpotData.push({ ...spot, exchange: exchangeData.exchange });
      });
    });

    data.allFuturesData.forEach(exchangeData => {
      exchangeData.futures.forEach(futures => {
        allFuturesData.push({ ...futures, exchange: exchangeData.exchange });
      });
    });

    console.log(`[DEBUG] Total spot data: ${allSpotData.length}, Total futures data: ${allFuturesData.length}`);

    // Verificar símbolos únicos
    const spotSymbols = new Set(allSpotData.map(s => s.symbol));
    const futuresSymbols = new Set(allFuturesData.map(f => f.symbol));
    const commonSymbols = [...spotSymbols].filter(s => futuresSymbols.has(s));
    console.log(`[DEBUG] Common symbols between spot and futures: ${commonSymbols.length}`);
    console.log(`[DEBUG] First 10 common symbols:`, commonSymbols.slice(0, 10));

    let totalComparisons = 0;
    let validSpreads = 0;
    const minSpread = parseFloat(process.env.MIN_SPREAD_PERCENTAGE || '0.05');
    const maxSpread = parseFloat(process.env.MAX_SPREAD_PERCENTAGE || '100.0');
    console.log(`[DEBUG] Spread thresholds: ${minSpread}% - ${maxSpread}%`);

    // 1. Encontrar oportunidades spot vs futures cross-exchange
    console.log(`[DEBUG] Calculando oportunidades spot vs futures...`);

    // Contar dados por exchange
    const spotByExchange = allSpotData.reduce((acc, spot) => {
      acc[spot.exchange] = (acc[spot.exchange] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const futuresByExchange = allFuturesData.reduce((acc, futures) => {
      acc[futures.exchange] = (acc[futures.exchange] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log(`[DEBUG] Spot data by exchange:`, spotByExchange);
    console.log(`[DEBUG] Futures data by exchange:`, futuresByExchange);

    let bitgetSpotOpportunities = 0;

    for (const spot of allSpotData) {
      for (const futures of allFuturesData) {
        // Verificar se é o mesmo símbolo mas exchanges diferentes
        if (spot.symbol === futures.symbol && spot.exchange !== futures.exchange) {
          totalComparisons++;

          // Validação de qualidade dos dados
          if (!this.isValidOrderbookData(spot) || !this.isValidOrderbookData(futures)) {
            continue;
          }

          // Cálculo correto usando orderbook:
          // Para comprar spot: usar ask (preço de venda)
          // Para vender futures: usar bid (preço de compra)
          const spread = futures.bid - spot.ask;
          const spreadPercentage = (spread / spot.ask) * 100;

          if (totalComparisons <= 5) {
            console.log(`[DEBUG] Spot-Futures ${spot.symbol}: ${spot.exchange} ask=${spot.ask}, ${futures.exchange} bid=${futures.bid}, spread=${spreadPercentage.toFixed(4)}%`);
          }

          // Validação de liquidez mínima
          const minVolumeUSD = 1000; // USD mínimo
          const effectiveVolume = this.calculateEffectiveVolume(spot, futures);

          // Filtrar spreads dentro do range válido com validações adicionais
          if (Math.abs(spreadPercentage) >= minSpread &&
            Math.abs(spreadPercentage) <= maxSpread &&
            effectiveVolume >= minVolumeUSD &&
            this.isReasonableSpread(spreadPercentage)) {

            validSpreads++;

            // Contar oportunidades específicas do Bitget spot
            if (spot.exchange === 'bitget') {
              bitgetSpotOpportunities++;
              if (bitgetSpotOpportunities <= 3) {
                console.log(`[DEBUG] BITGET SPOT OPPORTUNITY ${bitgetSpotOpportunities}: ${spot.symbol} - Bitget ask=${spot.ask}, ${futures.exchange} bid=${futures.bid}, spread=${spreadPercentage.toFixed(4)}%, volume=${effectiveVolume.toFixed(0)}USD`);
              }
            }

            const currentTime = Date.now();
            const dataAge = Math.max(currentTime - spot.timestamp, currentTime - futures.timestamp);

            opportunities.push({
              symbol: spot.symbol,
              spotExchange: spot.exchange,
              futuresExchange: futures.exchange,
              spotPrice: spot.ask,      // Preço que pagamos para comprar spot
              futuresPrice: futures.bid, // Preço que recebemos para vender futures
              spread,
              spreadPercentage,
              volume: Math.min(spot.volume, futures.volume),
              fundingRate: futures.fundingRate,
              profitPotential: Math.abs(spread) * Math.min(spot.volume, futures.volume),
              timestamp: Date.now(),
              dataAge: dataAge,
              type: 'spot-futures'
            });
          }
        }
      }
    }

    console.log(`[DEBUG] Bitget spot opportunities found: ${bitgetSpotOpportunities}`);

    console.log(`[DEBUG] Spot-Futures comparisons: ${totalComparisons}, Valid spreads: ${validSpreads}, Opportunities found: ${opportunities.length}`);

    // 2. Encontrar oportunidades futures vs futures cross-exchange
    const futuresFuturesOpportunities = await this.calculateFuturesFuturesOpportunities(allFuturesData);
    opportunities.push(...futuresFuturesOpportunities);

    console.log(`[DEBUG] Total opportunities (spot-futures + futures-futures): ${opportunities.length}`);

    // Adicionar métricas de qualidade
    const qualityMetrics = this.calculateQualityMetrics(opportunities);
    console.log(`[QUALITY] Metrics:`, qualityMetrics);

    // Ordenar por rentabilidade
    opportunities.sort((a, b) => Math.abs(b.spreadPercentage) - Math.abs(a.spreadPercentage));

    // Limitar número de oportunidades
    const maxOpportunities = parseInt(process.env.MAX_OPPORTUNITIES || '2000');
    const limitedOpportunities = opportunities.slice(0, maxOpportunities);

    // Processar alertas para as oportunidades encontradas
    const newAlerts = this.alertService.checkOpportunities(limitedOpportunities);
    if (newAlerts.length > 0) {
      console.log(`[ALERTS] Generated ${newAlerts.length} new alerts`);
    }

    // Registrar métricas de performance
    const processingTime = Date.now() - startTime;
    this.metricsService.recordMetric('data_processing_time', processingTime, 'ms', 'latency');
    this.metricsService.recordBusinessMetric('opportunities_found', limitedOpportunities.length, 'count');

    if (limitedOpportunities.length > 0) {
      const averageSpread = limitedOpportunities.reduce((sum, opp) => sum + Math.abs(opp.spreadPercentage), 0) / limitedOpportunities.length;
      this.metricsService.recordBusinessMetric('average_spread', averageSpread, 'percentage');
    }

    // Cache das oportunidades com TTL dinâmico
    this.cacheService.setDynamic(cacheKey, limitedOpportunities);
    
    // Log de performance do cache
    const cacheStats = this.cacheService.getStats();
    console.log(`📊 Oportunidades calculadas em ${processingTime}ms - Cache Stats: Hit Rate ${cacheStats.hitRate}%`);

    return limitedOpportunities;
  }

  /**
   * Valida se os dados do orderbook são consistentes
   */
  private isValidOrderbookData(data: SpotData | FuturesData): boolean {
    // Verificar se bid/ask existem e são válidos
    if (!data.bid || !data.ask || data.bid <= 0 || data.ask <= 0) {
      return false;
    }

    // Verificar se ask > bid (spread positivo)
    if (data.ask <= data.bid) {
      return false;
    }

    // Verificar se o spread não é muito grande (> 5%)
    const spread = ((data.ask - data.bid) / data.bid) * 100;
    if (spread > 5) {
      return false;
    }

    // Verificar se o preço está próximo do bid/ask
    const midPrice = (data.bid + data.ask) / 2;
    const priceDiff = Math.abs(data.price - midPrice) / midPrice;
    if (priceDiff > 0.1) { // 10% de diferença máxima
      return false;
    }

    return true;
  }

  /**
   * Verifica se o spread é razoável (não é um erro de dados)
   */
  private isReasonableSpread(spreadPercentage: number): boolean {
    // Spreads muito grandes podem indicar dados incorretos
    return Math.abs(spreadPercentage) <= 50; // Máximo 50%
  }

  /**
   * Calcula o volume efetivo em USD para validação de liquidez
   */
  private calculateEffectiveVolume(spot: SpotData, futures: FuturesData): number {
    const spotVolumeUSD = spot.volume * spot.ask;
    const futuresVolumeUSD = futures.volume * futures.bid;
    return Math.min(spotVolumeUSD, futuresVolumeUSD);
  }

  /**
   * Calcula métricas de qualidade das oportunidades
   */
  private calculateQualityMetrics(opportunities: ArbitrageOpportunity[]): any {
    if (opportunities.length === 0) {
      return { totalOpportunities: 0 };
    }

    const spreads = opportunities.map(o => Math.abs(o.spreadPercentage));
    const volumes = opportunities.map(o => o.volume);
    const dataAges = opportunities.map(o => o.dataAge);

    // Agrupar por tipo
    const byType = opportunities.reduce((acc, opp) => {
      acc[opp.type] = (acc[opp.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Agrupar por exchange
    const byExchange = opportunities.reduce((acc, opp) => {
      const key = `${opp.spotExchange}-${opp.futuresExchange}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Calcular estatísticas
    const avgSpread = spreads.reduce((a, b) => a + b, 0) / spreads.length;
    const maxSpread = Math.max(...spreads);
    const minSpread = Math.min(...spreads);

    const avgVolume = volumes.reduce((a, b) => a + b, 0) / volumes.length;
    const totalVolume = volumes.reduce((a, b) => a + b, 0);

    const avgDataAge = dataAges.reduce((a, b) => a + b, 0) / dataAges.length;
    const maxDataAge = Math.max(...dataAges);

    // Oportunidades de alta qualidade (spread > 1% e volume > 10K)
    const highQuality = opportunities.filter(o =>
      Math.abs(o.spreadPercentage) > 1 &&
      o.volume > 10000
    ).length;

    return {
      totalOpportunities: opportunities.length,
      highQualityOpportunities: highQuality,
      qualityRatio: (highQuality / opportunities.length * 100).toFixed(1) + '%',
      spreadStats: {
        average: avgSpread.toFixed(3) + '%',
        min: minSpread.toFixed(3) + '%',
        max: maxSpread.toFixed(3) + '%'
      },
      volumeStats: {
        average: Math.round(avgVolume).toLocaleString(),
        total: Math.round(totalVolume).toLocaleString()
      },
      dataFreshness: {
        averageAge: Math.round(avgDataAge / 1000) + 's',
        maxAge: Math.round(maxDataAge / 1000) + 's'
      },
      distributionByType: byType,
      topExchangePairs: Object.entries(byExchange)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .reduce((acc, [key, value]) => {
          acc[key] = value;
          return acc;
        }, {} as Record<string, number>)
    };
  }
}