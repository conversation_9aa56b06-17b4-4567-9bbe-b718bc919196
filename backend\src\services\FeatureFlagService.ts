import { EventEmitter } from 'events';

interface FeatureFlag {
  name: string;
  enabled: boolean;
  description: string;
  rolloutPercentage: number; // 0-100
  enabledFor: string[];      // Lista de usuários/IPs específicos
  dependencies: string[];    // Flags que devem estar habilitadas
  createdAt: Date;
  updatedAt: Date;
}

interface FeatureFlagConfig {
  // OTIMIZAÇÃO: Flags para otimizações de ultra baixa latência
  PARALLEL_PROCESSING: boolean;
  MULTI_LAYER_CACHE: boolean;
  WEBSOCKET_STREAMING: boolean;
  BATCH_PROCESSING: boolean;
  CONNECTION_POOLING: boolean;
  VIRTUALIZED_UI: boolean;
  PERFORMANCE_MONITORING: boolean;
  
  // Flags de rollback
  FALLBACK_TO_ORIGINAL: boolean;
  EMERGENCY_MODE: boolean;
}

export class FeatureFlagService extends EventEmitter {
  private static instance: FeatureFlagService;
  private flags = new Map<string, FeatureFlag>();
  private config: FeatureFlagConfig;
  private rollbackState: Map<string, any> = new Map();

  /**
   * Singleton pattern
   */
  static getInstance(): FeatureFlagService {
    if (!FeatureFlagService.instance) {
      FeatureFlagService.instance = new FeatureFlagService();
    }
    return FeatureFlagService.instance;
  }

  constructor() {
    super();
    this.initializeDefaultFlags();
    this.loadConfigFromEnv();
    console.log('🚀 FeatureFlagService: Sistema de feature flags inicializado');
  }

  /**
   * OTIMIZAÇÃO: Inicializa flags padrão
   */
  private initializeDefaultFlags(): void {
    const defaultFlags: Partial<Record<keyof FeatureFlagConfig, Omit<FeatureFlag, 'name'>>> = {
      PARALLEL_PROCESSING: {
        enabled: process.env.ENABLE_PARALLEL_PROCESSING === 'true',
        description: 'Processamento paralelo de APIs das exchanges',
        rolloutPercentage: 100,
        enabledFor: [],
        dependencies: [],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      MULTI_LAYER_CACHE: {
        enabled: process.env.ENABLE_MULTI_LAYER_CACHE === 'true',
        description: 'Cache multi-camadas L1/L2/L3',
        rolloutPercentage: 100,
        enabledFor: [],
        dependencies: [],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      WEBSOCKET_STREAMING: {
        enabled: process.env.ENABLE_WEBSOCKET_STREAMING === 'true',
        description: 'Streaming em tempo real via WebSocket',
        rolloutPercentage: 100,
        enabledFor: [],
        dependencies: [],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      BATCH_PROCESSING: {
        enabled: process.env.ENABLE_BATCH_PROCESSING === 'true',
        description: 'Processamento em batches paralelos',
        rolloutPercentage: 100,
        enabledFor: [],
        dependencies: ['PARALLEL_PROCESSING'],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      CONNECTION_POOLING: {
        enabled: process.env.ENABLE_CONNECTION_POOLING === 'true',
        description: 'Connection pooling otimizado',
        rolloutPercentage: 100,
        enabledFor: [],
        dependencies: [],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      VIRTUALIZED_UI: {
        enabled: process.env.ENABLE_VIRTUALIZED_UI === 'true',
        description: 'Interface virtualizada para alta performance',
        rolloutPercentage: 100,
        enabledFor: [],
        dependencies: [],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      PERFORMANCE_MONITORING: {
        enabled: process.env.ENABLE_PERFORMANCE_MONITORING === 'true',
        description: 'Monitoramento avançado de performance',
        rolloutPercentage: 100,
        enabledFor: [],
        dependencies: [],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      FALLBACK_TO_ORIGINAL: {
        enabled: process.env.ENABLE_FALLBACK === 'true',
        description: 'Fallback para implementação original',
        rolloutPercentage: 0,
        enabledFor: [],
        dependencies: [],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      EMERGENCY_MODE: {
        enabled: process.env.EMERGENCY_MODE === 'true',
        description: 'Modo de emergência - desabilita todas as otimizações',
        rolloutPercentage: 0,
        enabledFor: [],
        dependencies: [],
        createdAt: new Date(),
        updatedAt: new Date()
      }
    };

    // Registrar flags
    Object.entries(defaultFlags).forEach(([name, flag]) => {
      this.flags.set(name, { name, ...flag });
    });
  }

  /**
   * OTIMIZAÇÃO: Carrega configuração das variáveis de ambiente
   */
  private loadConfigFromEnv(): void {
    this.config = {
      PARALLEL_PROCESSING: this.isEnabled('PARALLEL_PROCESSING'),
      MULTI_LAYER_CACHE: this.isEnabled('MULTI_LAYER_CACHE'),
      WEBSOCKET_STREAMING: this.isEnabled('WEBSOCKET_STREAMING'),
      BATCH_PROCESSING: this.isEnabled('BATCH_PROCESSING'),
      CONNECTION_POOLING: this.isEnabled('CONNECTION_POOLING'),
      VIRTUALIZED_UI: this.isEnabled('VIRTUALIZED_UI'),
      PERFORMANCE_MONITORING: this.isEnabled('PERFORMANCE_MONITORING'),
      FALLBACK_TO_ORIGINAL: this.isEnabled('FALLBACK_TO_ORIGINAL'),
      EMERGENCY_MODE: this.isEnabled('EMERGENCY_MODE')
    };

    console.log('🎛️ FeatureFlags carregadas:', this.config);
  }

  /**
   * OTIMIZAÇÃO: Verifica se uma feature está habilitada
   */
  isEnabled(flagName: keyof FeatureFlagConfig, userId?: string): boolean {
    // Modo de emergência desabilita tudo
    if (this.config?.EMERGENCY_MODE && flagName !== 'EMERGENCY_MODE') {
      return false;
    }

    const flag = this.flags.get(flagName);
    if (!flag) {
      return false;
    }

    // Verificar dependências
    if (flag.dependencies.length > 0) {
      const dependenciesMet = flag.dependencies.every(dep => this.isEnabled(dep as keyof FeatureFlagConfig, userId));
      if (!dependenciesMet) {
        return false;
      }
    }

    // Flag desabilitada
    if (!flag.enabled) {
      return false;
    }

    // Usuário específico habilitado
    if (userId && flag.enabledFor.includes(userId)) {
      return true;
    }

    // Rollout percentual
    if (flag.rolloutPercentage < 100) {
      const hash = this.hashUserId(userId || 'anonymous');
      return (hash % 100) < flag.rolloutPercentage;
    }

    return true;
  }

  /**
   * OTIMIZAÇÃO: Habilita/desabilita uma feature
   */
  setFlag(flagName: keyof FeatureFlagConfig, enabled: boolean, rolloutPercentage?: number): void {
    const flag = this.flags.get(flagName);
    if (!flag) {
      console.error(`❌ Flag não encontrada: ${flagName}`);
      return;
    }

    // Salvar estado anterior para rollback
    this.rollbackState.set(flagName, {
      enabled: flag.enabled,
      rolloutPercentage: flag.rolloutPercentage,
      timestamp: Date.now()
    });

    flag.enabled = enabled;
    if (rolloutPercentage !== undefined) {
      flag.rolloutPercentage = rolloutPercentage;
    }
    flag.updatedAt = new Date();

    this.flags.set(flagName, flag);
    this.config[flagName] = enabled;

    console.log(`🎛️ Flag ${flagName} ${enabled ? 'HABILITADA' : 'DESABILITADA'} (rollout: ${flag.rolloutPercentage}%)`);
    
    // Emitir evento
    this.emit('flagChanged', { flagName, enabled, rolloutPercentage: flag.rolloutPercentage });
  }

  /**
   * OTIMIZAÇÃO: Rollback de uma feature
   */
  rollbackFlag(flagName: keyof FeatureFlagConfig): boolean {
    const previousState = this.rollbackState.get(flagName);
    if (!previousState) {
      console.error(`❌ Nenhum estado anterior encontrado para ${flagName}`);
      return false;
    }

    const flag = this.flags.get(flagName);
    if (!flag) {
      console.error(`❌ Flag não encontrada: ${flagName}`);
      return false;
    }

    flag.enabled = previousState.enabled;
    flag.rolloutPercentage = previousState.rolloutPercentage;
    flag.updatedAt = new Date();

    this.flags.set(flagName, flag);
    this.config[flagName] = previousState.enabled;

    console.log(`🔄 Rollback ${flagName}: ${previousState.enabled ? 'HABILITADA' : 'DESABILITADA'} (rollout: ${previousState.rolloutPercentage}%)`);
    
    // Emitir evento
    this.emit('flagRolledBack', { flagName, previousState });
    
    return true;
  }

  /**
   * OTIMIZAÇÃO: Ativa modo de emergência
   */
  activateEmergencyMode(): void {
    console.log('🚨 ATIVANDO MODO DE EMERGÊNCIA - Desabilitando todas as otimizações');
    
    // Salvar estado atual de todas as flags
    this.flags.forEach((flag, name) => {
      this.rollbackState.set(`emergency_${name}`, {
        enabled: flag.enabled,
        rolloutPercentage: flag.rolloutPercentage,
        timestamp: Date.now()
      });
    });

    // Desabilitar todas as otimizações
    this.setFlag('EMERGENCY_MODE', true);
    
    // Emitir evento crítico
    this.emit('emergencyActivated', { timestamp: new Date() });
  }

  /**
   * OTIMIZAÇÃO: Desativa modo de emergência
   */
  deactivateEmergencyMode(): void {
    console.log('✅ Desativando modo de emergência - Restaurando estado anterior');
    
    this.setFlag('EMERGENCY_MODE', false);
    
    // Restaurar estado anterior das flags
    this.flags.forEach((flag, name) => {
      const emergencyState = this.rollbackState.get(`emergency_${name}`);
      if (emergencyState) {
        flag.enabled = emergencyState.enabled;
        flag.rolloutPercentage = emergencyState.rolloutPercentage;
        this.config[name as keyof FeatureFlagConfig] = emergencyState.enabled;
      }
    });
    
    // Emitir evento
    this.emit('emergencyDeactivated', { timestamp: new Date() });
  }

  /**
   * OTIMIZAÇÃO: Obtém configuração atual
   */
  getConfig(): FeatureFlagConfig {
    return { ...this.config };
  }

  /**
   * OTIMIZAÇÃO: Obtém todas as flags
   */
  getAllFlags(): Record<string, FeatureFlag> {
    const result: Record<string, FeatureFlag> = {};
    this.flags.forEach((flag, name) => {
      result[name] = { ...flag };
    });
    return result;
  }

  /**
   * OTIMIZAÇÃO: Hash simples para rollout percentual
   */
  private hashUserId(userId: string): number {
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      const char = userId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * OTIMIZAÇÃO: Obtém status do sistema
   */
  getSystemStatus(): {
    emergencyMode: boolean;
    totalFlags: number;
    enabledFlags: number;
    rollbacksAvailable: number;
  } {
    const totalFlags = this.flags.size;
    const enabledFlags = Array.from(this.flags.values()).filter(f => f.enabled).length;
    const rollbacksAvailable = this.rollbackState.size;

    return {
      emergencyMode: this.config.EMERGENCY_MODE,
      totalFlags,
      enabledFlags,
      rollbacksAvailable
    };
  }
}

export default FeatureFlagService;
