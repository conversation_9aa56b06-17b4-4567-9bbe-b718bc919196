export interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: number;
  category: 'latency' | 'throughput' | 'error' | 'business';
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  uptime: number;
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };
  apiResponseTimes: {
    average: number;
    p95: number;
    p99: number;
  };
  errorRate: number;
  lastUpdate: number;
}

export class MetricsService {
  private static instance: MetricsService;
  private metrics: PerformanceMetric[] = [];
  private maxMetrics = 10000;
  private startTime = Date.now();
  private requestTimes: number[] = [];
  private errorCount = 0;
  private totalRequests = 0;

  private constructor() {}

  public static getInstance(): MetricsService {
    if (!MetricsService.instance) {
      MetricsService.instance = new MetricsService();
    }
    return MetricsService.instance;
  }

  public recordMetric(name: string, value: number, unit: string, category: PerformanceMetric['category']): void {
    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: Date.now(),
      category
    };

    this.metrics.unshift(metric);

    // Manter apenas as métricas mais recentes
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(0, this.maxMetrics);
    }
  }

  public recordApiCall(responseTime: number, isError: boolean = false): void {
    this.totalRequests++;
    
    if (isError) {
      this.errorCount++;
      this.recordMetric('api_error', 1, 'count', 'error');
    } else {
      this.recordMetric('api_response_time', responseTime, 'ms', 'latency');
    }

    // Manter apenas os últimos 1000 tempos de resposta para cálculos
    this.requestTimes.unshift(responseTime);
    if (this.requestTimes.length > 1000) {
      this.requestTimes = this.requestTimes.slice(0, 1000);
    }
  }

  public recordBusinessMetric(name: string, value: number, unit: string): void {
    this.recordMetric(name, value, unit, 'business');
  }

  public getMetrics(category?: PerformanceMetric['category'], limit: number = 100): PerformanceMetric[] {
    let filteredMetrics = this.metrics;
    
    if (category) {
      filteredMetrics = this.metrics.filter(m => m.category === category);
    }
    
    return filteredMetrics.slice(0, limit);
  }

  public getSystemHealth(): SystemHealth {
    const now = Date.now();
    const uptime = now - this.startTime;
    
    // Calcular uso de memória
    const memUsage = process.memoryUsage();
    const memoryUsage = {
      used: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
      total: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
      percentage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100)
    };

    // Calcular tempos de resposta da API
    const sortedTimes = [...this.requestTimes].sort((a, b) => a - b);
    const apiResponseTimes = {
      average: sortedTimes.length > 0 ? Math.round(sortedTimes.reduce((a, b) => a + b, 0) / sortedTimes.length) : 0,
      p95: sortedTimes.length > 0 ? sortedTimes[Math.floor(sortedTimes.length * 0.95)] || 0 : 0,
      p99: sortedTimes.length > 0 ? sortedTimes[Math.floor(sortedTimes.length * 0.99)] || 0 : 0
    };

    // Calcular taxa de erro
    const errorRate = this.totalRequests > 0 ? (this.errorCount / this.totalRequests) * 100 : 0;

    // Determinar status de saúde
    let status: SystemHealth['status'] = 'healthy';
    
    if (memoryUsage.percentage > 90 || apiResponseTimes.average > 5000 || errorRate > 10) {
      status = 'unhealthy';
    } else if (memoryUsage.percentage > 70 || apiResponseTimes.average > 2000 || errorRate > 5) {
      status = 'degraded';
    }

    return {
      status,
      uptime,
      memoryUsage,
      apiResponseTimes,
      errorRate: Math.round(errorRate * 100) / 100,
      lastUpdate: now
    };
  }

  public getBusinessMetrics(): {
    totalOpportunities: number;
    averageSpread: number;
    topExchanges: { exchange: string; count: number }[];
    topSymbols: { symbol: string; count: number }[];
  } {
    const businessMetrics = this.metrics.filter(m => m.category === 'business');
    
    // Métricas de oportunidades
    const opportunityMetrics = businessMetrics.filter(m => m.name === 'opportunities_found');
    const totalOpportunities = opportunityMetrics.reduce((sum, m) => sum + m.value, 0);
    
    // Métricas de spread
    const spreadMetrics = businessMetrics.filter(m => m.name === 'average_spread');
    const averageSpread = spreadMetrics.length > 0 
      ? spreadMetrics.reduce((sum, m) => sum + m.value, 0) / spreadMetrics.length 
      : 0;

    // Top exchanges e símbolos (simulado - seria melhor ter dados reais)
    const topExchanges = [
      { exchange: 'gateio', count: Math.floor(totalOpportunities * 0.4) },
      { exchange: 'mexc', count: Math.floor(totalOpportunities * 0.35) },
      { exchange: 'bitget', count: Math.floor(totalOpportunities * 0.25) }
    ];

    const topSymbols = [
      { symbol: 'BTC/USDT', count: Math.floor(totalOpportunities * 0.15) },
      { symbol: 'ETH/USDT', count: Math.floor(totalOpportunities * 0.12) },
      { symbol: 'BNB/USDT', count: Math.floor(totalOpportunities * 0.08) },
      { symbol: 'SOL/USDT', count: Math.floor(totalOpportunities * 0.06) },
      { symbol: 'ADA/USDT', count: Math.floor(totalOpportunities * 0.05) }
    ];

    return {
      totalOpportunities,
      averageSpread: Math.round(averageSpread * 1000) / 1000,
      topExchanges,
      topSymbols
    };
  }

  public getLatencyMetrics(): {
    apiCalls: { average: number; p95: number; p99: number };
    dataProcessing: { average: number; p95: number; p99: number };
    cacheHitRate: number;
  } {
    const latencyMetrics = this.metrics.filter(m => m.category === 'latency');
    
    // Métricas de API
    const apiMetrics = latencyMetrics.filter(m => m.name === 'api_response_time');
    const apiTimes = apiMetrics.map(m => m.value).sort((a, b) => a - b);
    
    // Métricas de processamento
    const processingMetrics = latencyMetrics.filter(m => m.name === 'data_processing_time');
    const processingTimes = processingMetrics.map(m => m.value).sort((a, b) => a - b);
    
    // Cache hit rate (simulado)
    const cacheHitRate = 95.5; // Seria calculado baseado em métricas reais

    return {
      apiCalls: {
        average: apiTimes.length > 0 ? Math.round(apiTimes.reduce((a, b) => a + b, 0) / apiTimes.length) : 0,
        p95: apiTimes.length > 0 ? apiTimes[Math.floor(apiTimes.length * 0.95)] || 0 : 0,
        p99: apiTimes.length > 0 ? apiTimes[Math.floor(apiTimes.length * 0.99)] || 0 : 0
      },
      dataProcessing: {
        average: processingTimes.length > 0 ? Math.round(processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length) : 0,
        p95: processingTimes.length > 0 ? processingTimes[Math.floor(processingTimes.length * 0.95)] || 0 : 0,
        p99: processingTimes.length > 0 ? processingTimes[Math.floor(processingTimes.length * 0.99)] || 0 : 0
      },
      cacheHitRate
    };
  }

  public clearMetrics(): void {
    this.metrics = [];
    this.requestTimes = [];
    this.errorCount = 0;
    this.totalRequests = 0;
  }

  public getMetricsSummary(): {
    totalMetrics: number;
    categories: { [key: string]: number };
    timeRange: { start: number; end: number };
    memoryUsage: number;
  } {
    const categories = this.metrics.reduce((acc, metric) => {
      acc[metric.category] = (acc[metric.category] || 0) + 1;
      return acc;
    }, {} as { [key: string]: number });

    const timestamps = this.metrics.map(m => m.timestamp);
    const timeRange = {
      start: timestamps.length > 0 ? Math.min(...timestamps) : 0,
      end: timestamps.length > 0 ? Math.max(...timestamps) : 0
    };

    return {
      totalMetrics: this.metrics.length,
      categories,
      timeRange,
      memoryUsage: Math.round(process.memoryUsage().heapUsed / 1024 / 1024)
    };
  }
}