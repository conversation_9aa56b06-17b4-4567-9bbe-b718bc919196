import { EventEmitter } from 'events';

interface LatencyMeasurement {
  timestamp: number;
  latency: number;
  operation: string;
  success: boolean;
}

interface PerformanceMetrics {
  latency: {
    p50: number;
    p90: number;
    p95: number;
    p99: number;
    avg: number;
    min: number;
    max: number;
  };
  throughput: {
    requestsPerSecond: number;
    requestsPerMinute: number;
    successRate: number;
    errorRate: number;
  };
  system: {
    memoryUsage: NodeJS.MemoryUsage;
    cpuUsage: number;
    uptime: number;
  };
  alerts: PerformanceAlert[];
}

interface PerformanceAlert {
  id: string;
  type: 'latency' | 'throughput' | 'error_rate' | 'memory' | 'cpu';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  value: number;
  threshold: number;
}

interface PerformanceThresholds {
  latencyP99: number;      // 1000ms
  latencyP95: number;      // 800ms
  latencyP90: number;      // 600ms
  errorRate: number;       // 5%
  memoryUsage: number;     // 80%
  cpuUsage: number;        // 80%
  throughputMin: number;   // 100 req/min
}

export class PerformanceMonitor extends EventEmitter {
  private static instance: PerformanceMonitor;
  private measurements: LatencyMeasurement[] = [];
  private requestCounts: Map<string, number> = new Map();
  private errorCounts: Map<string, number> = new Map();
  private alerts: PerformanceAlert[] = [];
  private monitoringInterval: NodeJS.Timeout | null = null;
  private alertInterval: NodeJS.Timeout | null = null;
  
  private readonly MAX_MEASUREMENTS = 10000; // Manter últimas 10k medições
  private readonly MAX_ALERTS = 100;         // Manter últimos 100 alertas
  
  private readonly thresholds: PerformanceThresholds = {
    latencyP99: 1000,    // 1s
    latencyP95: 800,     // 800ms
    latencyP90: 600,     // 600ms
    errorRate: 5,        // 5%
    memoryUsage: 80,     // 80%
    cpuUsage: 80,        // 80%
    throughputMin: 100   // 100 req/min
  };

  /**
   * Singleton pattern
   */
  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  constructor() {
    super();
    this.startMonitoring();
    console.log('🚀 PerformanceMonitor: Sistema de monitoramento iniciado');
  }

  /**
   * OTIMIZAÇÃO: Registra medição de latência
   */
  recordLatency(operation: string, latency: number, success: boolean = true): void {
    const measurement: LatencyMeasurement = {
      timestamp: Date.now(),
      latency,
      operation,
      success
    };

    this.measurements.push(measurement);

    // Manter apenas últimas medições
    if (this.measurements.length > this.MAX_MEASUREMENTS) {
      this.measurements.shift();
    }

    // Atualizar contadores
    const timeKey = this.getTimeKey();
    this.requestCounts.set(timeKey, (this.requestCounts.get(timeKey) || 0) + 1);
    
    if (!success) {
      this.errorCounts.set(timeKey, (this.errorCounts.get(timeKey) || 0) + 1);
    }

    // Verificar alertas críticos imediatamente
    if (latency > this.thresholds.latencyP99) {
      this.createAlert('latency', 'critical', 
        `Latência crítica detectada: ${latency}ms (limite: ${this.thresholds.latencyP99}ms)`,
        latency, this.thresholds.latencyP99
      );
    }
  }

  /**
   * OTIMIZAÇÃO: Calcula percentis de latência
   */
  private calculatePercentiles(latencies: number[]): {
    p50: number; p90: number; p95: number; p99: number;
    avg: number; min: number; max: number;
  } {
    if (latencies.length === 0) {
      return { p50: 0, p90: 0, p95: 0, p99: 0, avg: 0, min: 0, max: 0 };
    }

    const sorted = [...latencies].sort((a, b) => a - b);
    const len = sorted.length;

    const p50 = sorted[Math.floor(len * 0.5)];
    const p90 = sorted[Math.floor(len * 0.9)];
    const p95 = sorted[Math.floor(len * 0.95)];
    const p99 = sorted[Math.floor(len * 0.99)];
    
    const avg = latencies.reduce((sum, l) => sum + l, 0) / len;
    const min = sorted[0];
    const max = sorted[len - 1];

    return { p50, p90, p95, p99, avg, min, max };
  }

  /**
   * OTIMIZAÇÃO: Calcula métricas de throughput
   */
  private calculateThroughput(): {
    requestsPerSecond: number;
    requestsPerMinute: number;
    successRate: number;
    errorRate: number;
  } {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;
    const oneSecondAgo = now - 1000;

    // Filtrar medições do último minuto
    const recentMeasurements = this.measurements.filter(m => m.timestamp >= oneMinuteAgo);
    const lastSecondMeasurements = this.measurements.filter(m => m.timestamp >= oneSecondAgo);

    const requestsPerMinute = recentMeasurements.length;
    const requestsPerSecond = lastSecondMeasurements.length;

    const successfulRequests = recentMeasurements.filter(m => m.success).length;
    const successRate = requestsPerMinute > 0 ? (successfulRequests / requestsPerMinute) * 100 : 100;
    const errorRate = 100 - successRate;

    return {
      requestsPerSecond,
      requestsPerMinute,
      successRate,
      errorRate
    };
  }

  /**
   * OTIMIZAÇÃO: Obtém métricas completas do sistema
   */
  getMetrics(): PerformanceMetrics {
    const now = Date.now();
    const oneHourAgo = now - 3600000; // 1 hora

    // Filtrar medições da última hora
    const recentMeasurements = this.measurements.filter(m => m.timestamp >= oneHourAgo);
    const latencies = recentMeasurements.map(m => m.latency);

    const latencyMetrics = this.calculatePercentiles(latencies);
    const throughputMetrics = this.calculateThroughput();

    return {
      latency: latencyMetrics,
      throughput: throughputMetrics,
      system: {
        memoryUsage: process.memoryUsage(),
        cpuUsage: this.getCpuUsage(),
        uptime: process.uptime()
      },
      alerts: this.alerts.slice(-10) // Últimos 10 alertas
    };
  }

  /**
   * OTIMIZAÇÃO: Cria alerta de performance
   */
  private createAlert(
    type: PerformanceAlert['type'],
    severity: PerformanceAlert['severity'],
    message: string,
    value: number,
    threshold: number
  ): void {
    const alert: PerformanceAlert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      severity,
      message,
      timestamp: new Date(),
      value,
      threshold
    };

    this.alerts.push(alert);

    // Manter apenas últimos alertas
    if (this.alerts.length > this.MAX_ALERTS) {
      this.alerts.shift();
    }

    // Emitir evento para notificação
    this.emit('alert', alert);

    console.log(`🚨 PerformanceAlert [${severity.toUpperCase()}]: ${message}`);
  }

  /**
   * OTIMIZAÇÃO: Inicia monitoramento contínuo
   */
  private startMonitoring(): void {
    // Monitoramento a cada 10 segundos
    this.monitoringInterval = setInterval(() => {
      this.checkPerformanceThresholds();
      this.cleanupOldData();
    }, 10000);

    // Verificação de alertas a cada 30 segundos
    this.alertInterval = setInterval(() => {
      this.checkSystemAlerts();
    }, 30000);
  }

  /**
   * OTIMIZAÇÃO: Verifica limites de performance
   */
  private checkPerformanceThresholds(): void {
    const metrics = this.getMetrics();

    // Verificar latência P99
    if (metrics.latency.p99 > this.thresholds.latencyP99) {
      this.createAlert('latency', 'high',
        `Latência P99 acima do limite: ${metrics.latency.p99.toFixed(2)}ms`,
        metrics.latency.p99, this.thresholds.latencyP99
      );
    }

    // Verificar latência P95
    if (metrics.latency.p95 > this.thresholds.latencyP95) {
      this.createAlert('latency', 'medium',
        `Latência P95 acima do limite: ${metrics.latency.p95.toFixed(2)}ms`,
        metrics.latency.p95, this.thresholds.latencyP95
      );
    }

    // Verificar taxa de erro
    if (metrics.throughput.errorRate > this.thresholds.errorRate) {
      this.createAlert('error_rate', 'high',
        `Taxa de erro elevada: ${metrics.throughput.errorRate.toFixed(2)}%`,
        metrics.throughput.errorRate, this.thresholds.errorRate
      );
    }

    // Verificar throughput mínimo
    if (metrics.throughput.requestsPerMinute < this.thresholds.throughputMin) {
      this.createAlert('throughput', 'medium',
        `Throughput baixo: ${metrics.throughput.requestsPerMinute} req/min`,
        metrics.throughput.requestsPerMinute, this.thresholds.throughputMin
      );
    }
  }

  /**
   * OTIMIZAÇÃO: Verifica alertas do sistema
   */
  private checkSystemAlerts(): void {
    const memoryUsage = process.memoryUsage();
    const totalMemory = memoryUsage.heapTotal;
    const usedMemory = memoryUsage.heapUsed;
    const memoryPercent = (usedMemory / totalMemory) * 100;

    if (memoryPercent > this.thresholds.memoryUsage) {
      this.createAlert('memory', 'high',
        `Uso de memória elevado: ${memoryPercent.toFixed(1)}%`,
        memoryPercent, this.thresholds.memoryUsage
      );
    }

    const cpuUsage = this.getCpuUsage();
    if (cpuUsage > this.thresholds.cpuUsage) {
      this.createAlert('cpu', 'high',
        `Uso de CPU elevado: ${cpuUsage.toFixed(1)}%`,
        cpuUsage, this.thresholds.cpuUsage
      );
    }
  }

  /**
   * OTIMIZAÇÃO: Obtém uso de CPU (aproximado)
   */
  private getCpuUsage(): number {
    const usage = process.cpuUsage();
    const total = usage.user + usage.system;
    return (total / 1000000) / process.uptime() * 100; // Aproximação
  }

  /**
   * OTIMIZAÇÃO: Gera chave de tempo para agrupamento
   */
  private getTimeKey(): string {
    return Math.floor(Date.now() / 60000).toString(); // Minuto
  }

  /**
   * OTIMIZAÇÃO: Limpa dados antigos
   */
  private cleanupOldData(): void {
    const oneHourAgo = Date.now() - 3600000;
    
    // Limpar medições antigas
    this.measurements = this.measurements.filter(m => m.timestamp >= oneHourAgo);
    
    // Limpar contadores antigos
    const currentTimeKey = parseInt(this.getTimeKey());
    const oneHourAgoKey = currentTimeKey - 60; // 60 minutos atrás
    
    for (const [key] of this.requestCounts) {
      if (parseInt(key) < oneHourAgoKey) {
        this.requestCounts.delete(key);
        this.errorCounts.delete(key);
      }
    }
  }

  /**
   * OTIMIZAÇÃO: Para o monitoramento
   */
  stop(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    if (this.alertInterval) {
      clearInterval(this.alertInterval);
      this.alertInterval = null;
    }
    
    console.log('🛑 PerformanceMonitor: Monitoramento parado');
  }

  /**
   * OTIMIZAÇÃO: Obtém estatísticas resumidas
   */
  getSummary(): {
    totalRequests: number;
    avgLatency: number;
    currentThroughput: number;
    activeAlerts: number;
    uptime: string;
  } {
    const metrics = this.getMetrics();
    const uptimeHours = Math.floor(metrics.system.uptime / 3600);
    const uptimeMinutes = Math.floor((metrics.system.uptime % 3600) / 60);
    
    return {
      totalRequests: this.measurements.length,
      avgLatency: Math.round(metrics.latency.avg),
      currentThroughput: metrics.throughput.requestsPerMinute,
      activeAlerts: this.alerts.filter(a => 
        Date.now() - a.timestamp.getTime() < 300000 // Últimos 5 minutos
      ).length,
      uptime: `${uptimeHours}h ${uptimeMinutes}m`
    };
  }
}

export default PerformanceMonitor;
