import { WebSocketServer, WebSocket } from 'ws';
import { Server } from 'http';
import type { ArbitrageOpportunity } from '../types/index.js';

interface WebSocketMessage {
  type: 'opportunity' | 'heartbeat' | 'error' | 'metrics';
  data: any;
  timestamp: number;
}

interface ConnectedClient {
  ws: WebSocket;
  id: string;
  connectedAt: Date;
  lastPing: Date;
}

export class WebSocketService {
  private static instance: WebSocketService;
  private wss: WebSocketServer | null = null;
  private clients = new Map<string, ConnectedClient>();
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private metricsInterval: NodeJS.Timeout | null = null;
  
  // Métricas de performance
  private messagesSent = 0;
  private messagesReceived = 0;
  private averageLatency = 0;
  private latencyMeasurements: number[] = [];

  /**
   * Singleton pattern
   */
  static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();
    }
    return WebSocketService.instance;
  }

  /**
   * Inicializa o servidor WebSocket
   */
  initialize(server: Server, port: number = 5001): void {
    this.wss = new WebSocketServer({ 
      server,
      port,
      perMessageDeflate: false // Otimização para latência
    });

    this.wss.on('connection', (ws: WebSocket, request) => {
      const clientId = this.generateClientId();
      const client: ConnectedClient = {
        ws,
        id: clientId,
        connectedAt: new Date(),
        lastPing: new Date()
      };

      this.clients.set(clientId, client);
      console.log(`🔗 WebSocket: Cliente ${clientId} conectado (${this.clients.size} total)`);

      // Configurar handlers do cliente
      this.setupClientHandlers(client);

      // Enviar mensagem de boas-vindas
      this.sendToClient(client, {
        type: 'heartbeat',
        data: { status: 'connected', clientId },
        timestamp: Date.now()
      });
    });

    // Iniciar heartbeat
    this.startHeartbeat();
    
    // Iniciar métricas
    this.startMetricsCollection();

    console.log(`🚀 WebSocketService: Servidor iniciado na porta ${port}`);
  }

  /**
   * Configura handlers para um cliente específico
   */
  private setupClientHandlers(client: ConnectedClient): void {
    const { ws, id } = client;

    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString()) as WebSocketMessage;
        this.handleClientMessage(client, message);
        this.messagesReceived++;
      } catch (error) {
        console.error(`❌ WebSocket: Erro ao processar mensagem do cliente ${id}:`, error);
      }
    });

    ws.on('close', () => {
      this.clients.delete(id);
      console.log(`🔌 WebSocket: Cliente ${id} desconectado (${this.clients.size} restantes)`);
    });

    ws.on('error', (error) => {
      console.error(`❌ WebSocket: Erro no cliente ${id}:`, error);
      this.clients.delete(id);
    });

    ws.on('pong', () => {
      client.lastPing = new Date();
    });
  }

  /**
   * Processa mensagens recebidas dos clientes
   */
  private handleClientMessage(client: ConnectedClient, message: WebSocketMessage): void {
    switch (message.type) {
      case 'heartbeat':
        // Responder ao heartbeat do cliente
        this.sendToClient(client, {
          type: 'heartbeat',
          data: { status: 'alive', serverTime: Date.now() },
          timestamp: Date.now()
        });
        break;

      default:
        console.log(`📨 WebSocket: Mensagem não reconhecida do cliente ${client.id}:`, message.type);
    }
  }

  /**
   * OTIMIZAÇÃO: Broadcast de oportunidade para todos os clientes conectados
   */
  broadcastOpportunity(opportunity: ArbitrageOpportunity): void {
    if (this.clients.size === 0) return;

    const message: WebSocketMessage = {
      type: 'opportunity',
      data: opportunity,
      timestamp: Date.now()
    };

    const startTime = Date.now();
    let successCount = 0;

    this.clients.forEach((client) => {
      if (client.ws.readyState === WebSocket.OPEN) {
        try {
          this.sendToClient(client, message);
          successCount++;
        } catch (error) {
          console.error(`❌ WebSocket: Erro ao enviar para cliente ${client.id}:`, error);
        }
      }
    });

    // Calcular latência
    const latency = Date.now() - startTime;
    this.updateLatencyMetrics(latency);

    this.messagesSent += successCount;
    
    if (successCount > 0) {
      console.log(`⚡ WebSocket: Oportunidade ${opportunity.id} enviada para ${successCount} clientes em ${latency}ms`);
    }
  }

  /**
   * Envia mensagem para um cliente específico
   */
  private sendToClient(client: ConnectedClient, message: WebSocketMessage): void {
    if (client.ws.readyState === WebSocket.OPEN) {
      client.ws.send(JSON.stringify(message));
    }
  }

  /**
   * Broadcast para todos os clientes
   */
  broadcast(message: WebSocketMessage): void {
    this.clients.forEach((client) => {
      this.sendToClient(client, message);
    });
  }

  /**
   * Inicia sistema de heartbeat
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      const now = new Date();
      
      this.clients.forEach((client, id) => {
        if (client.ws.readyState === WebSocket.OPEN) {
          // Verificar se cliente está responsivo (último ping < 60s)
          const timeSinceLastPing = now.getTime() - client.lastPing.getTime();
          
          if (timeSinceLastPing > 60000) {
            console.log(`⚠️ WebSocket: Cliente ${id} não responsivo, desconectando...`);
            client.ws.terminate();
            this.clients.delete(id);
          } else {
            // Enviar ping
            client.ws.ping();
          }
        } else {
          this.clients.delete(id);
        }
      });
    }, 30000); // Heartbeat a cada 30 segundos
  }

  /**
   * Inicia coleta de métricas
   */
  private startMetricsCollection(): void {
    this.metricsInterval = setInterval(() => {
      const metrics = this.getMetrics();
      
      this.broadcast({
        type: 'metrics',
        data: metrics,
        timestamp: Date.now()
      });
    }, 10000); // Métricas a cada 10 segundos
  }

  /**
   * Atualiza métricas de latência
   */
  private updateLatencyMetrics(latency: number): void {
    this.latencyMeasurements.push(latency);
    
    // Manter apenas últimas 100 medições
    if (this.latencyMeasurements.length > 100) {
      this.latencyMeasurements.shift();
    }
    
    // Calcular média
    this.averageLatency = this.latencyMeasurements.reduce((sum, l) => sum + l, 0) / this.latencyMeasurements.length;
  }

  /**
   * Retorna métricas do WebSocket
   */
  getMetrics() {
    return {
      connectedClients: this.clients.size,
      messagesSent: this.messagesSent,
      messagesReceived: this.messagesReceived,
      averageLatency: Math.round(this.averageLatency * 100) / 100,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage()
    };
  }

  /**
   * Gera ID único para cliente
   */
  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Finaliza o serviço WebSocket
   */
  shutdown(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
    }

    this.clients.forEach((client) => {
      client.ws.close();
    });

    if (this.wss) {
      this.wss.close();
    }

    console.log('🔌 WebSocketService: Serviço finalizado');
  }

  /**
   * Retorna status da conexão
   */
  getStatus() {
    return {
      isRunning: this.wss !== null,
      clientCount: this.clients.size,
      port: this.wss?.options.port || null
    };
  }
}
