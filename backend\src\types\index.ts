// Tipos para dados das exchanges
export interface SpotData {
  symbol: string;
  price: number;
  volume: number;
  change24h: number;
  high24h: number;
  low24h: number;
  bid: number;  // Preço de compra (onde vendemos)
  ask: number;  // Preço de venda (onde compramos)
  timestamp: number;
}

export interface FuturesData {
  symbol: string;
  price: number;
  volume: number;
  change24h: number;
  high24h: number;
  low24h: number;
  bid: number;  // Preço de compra (onde vendemos)
  ask: number;  // Preço de venda (onde compramos)
  fundingRate: number;
  openInterest: number;
  timestamp: number;
}

export interface ArbitrageOpportunity {
  symbol: string;
  spotExchange: string;
  futuresExchange: string;
  spotPrice: number;
  futuresPrice: number;
  spread: number;
  spreadPercentage: number;
  volume: number;
  fundingRate?: number;
  profitPotential: number;
  timestamp: number;
  dataAge: number; // Idade dos dados em milissegundos
  type?: 'spot-futures' | 'futures-futures'; // Tipo de arbitragem
}

export interface ExchangeData {
  exchange: string;
  spot: SpotData[];
  futures: FuturesData[];
  timestamp: number;
  status: 'success' | 'error' | 'partial';
  error?: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: number;
}

export interface ExchangeConfig {
  name: string;
  apiKey: string;
  secretKey: string;
  passphrase?: string;
  baseUrl: string;
  spotEndpoint: string;
  futuresEndpoint: string;
  rateLimit: number;
}

export interface HMACConfig {
  method: 'GET' | 'POST';
  path: string;
  timestamp: number;
  body?: string;
}

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}