#!/usr/bin/env node

/**
 * OTIMIZAÇÃO: Script de teste de performance para validar ultra baixa latência
 * 
 * Testa:
 * - Latência P50/P90/P95/P99 < 1s
 * - Throughput > 250 req/s
 * - Taxa de sucesso > 99%
 * - Uptime > 99.9%
 */

import fetch from 'node-fetch';
import { performance } from 'perf_hooks';

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:5001';
const TEST_DURATION = parseInt(process.env.TEST_DURATION) || 60000; // 1 minuto
const CONCURRENT_REQUESTS = parseInt(process.env.CONCURRENT_REQUESTS) || 10;
const TARGET_LATENCY_P99 = 1000; // 1 segundo
const TARGET_THROUGHPUT = 250; // req/s
const TARGET_SUCCESS_RATE = 99; // %

class PerformanceTest {
  constructor() {
    this.results = [];
    this.errors = [];
    this.startTime = null;
    this.endTime = null;
  }

  /**
   * Executa teste de latência para um endpoint
   */
  async testEndpointLatency(endpoint, iterations = 100) {
    console.log(`🧪 Testando latência: ${endpoint} (${iterations} iterações)`);
    
    const latencies = [];
    const errors = [];

    for (let i = 0; i < iterations; i++) {
      const start = performance.now();
      
      try {
        const response = await fetch(`${BACKEND_URL}${endpoint}`, {
          timeout: 10000
        });
        
        const end = performance.now();
        const latency = end - start;
        
        if (response.ok) {
          latencies.push(latency);
        } else {
          errors.push(`HTTP ${response.status}`);
        }
      } catch (error) {
        const end = performance.now();
        const latency = end - start;
        errors.push(error.message);
        latencies.push(latency); // Incluir timeouts na latência
      }
    }

    return this.calculateStats(latencies, errors, iterations);
  }

  /**
   * Executa teste de throughput com requisições concorrentes
   */
  async testThroughput(endpoint, duration = 30000) {
    console.log(`🚀 Testando throughput: ${endpoint} (${duration}ms)`);
    
    const startTime = Date.now();
    const endTime = startTime + duration;
    let requestCount = 0;
    let successCount = 0;
    let errorCount = 0;
    const latencies = [];

    const workers = [];
    
    // Criar workers concorrentes
    for (let i = 0; i < CONCURRENT_REQUESTS; i++) {
      workers.push(this.createWorker(endpoint, endTime, (result) => {
        requestCount++;
        if (result.success) {
          successCount++;
        } else {
          errorCount++;
        }
        latencies.push(result.latency);
      }));
    }

    // Aguardar todos os workers
    await Promise.all(workers);

    const actualDuration = Date.now() - startTime;
    const throughput = (requestCount / actualDuration) * 1000; // req/s
    const successRate = (successCount / requestCount) * 100;

    return {
      requestCount,
      successCount,
      errorCount,
      throughput: Math.round(throughput * 100) / 100,
      successRate: Math.round(successRate * 100) / 100,
      duration: actualDuration,
      latencyStats: this.calculateLatencyStats(latencies)
    };
  }

  /**
   * Worker para requisições concorrentes
   */
  async createWorker(endpoint, endTime, callback) {
    while (Date.now() < endTime) {
      const start = performance.now();
      
      try {
        const response = await fetch(`${BACKEND_URL}${endpoint}`, {
          timeout: 5000
        });
        
        const end = performance.now();
        const latency = end - start;
        
        callback({
          success: response.ok,
          latency,
          status: response.status
        });
        
      } catch (error) {
        const end = performance.now();
        const latency = end - start;
        
        callback({
          success: false,
          latency,
          error: error.message
        });
      }

      // Pequena pausa para evitar sobrecarga
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }

  /**
   * Calcula estatísticas de latência
   */
  calculateStats(latencies, errors, total) {
    if (latencies.length === 0) {
      return {
        count: total,
        successCount: 0,
        errorCount: errors.length,
        successRate: 0,
        latency: { p50: 0, p90: 0, p95: 0, p99: 0, avg: 0, min: 0, max: 0 },
        errors
      };
    }

    const sorted = [...latencies].sort((a, b) => a - b);
    const len = sorted.length;

    return {
      count: total,
      successCount: latencies.length,
      errorCount: errors.length,
      successRate: Math.round((latencies.length / total) * 10000) / 100,
      latency: {
        p50: Math.round(sorted[Math.floor(len * 0.5)] * 100) / 100,
        p90: Math.round(sorted[Math.floor(len * 0.9)] * 100) / 100,
        p95: Math.round(sorted[Math.floor(len * 0.95)] * 100) / 100,
        p99: Math.round(sorted[Math.floor(len * 0.99)] * 100) / 100,
        avg: Math.round((latencies.reduce((sum, l) => sum + l, 0) / len) * 100) / 100,
        min: Math.round(sorted[0] * 100) / 100,
        max: Math.round(sorted[len - 1] * 100) / 100
      },
      errors
    };
  }

  /**
   * Calcula estatísticas de latência simplificadas
   */
  calculateLatencyStats(latencies) {
    if (latencies.length === 0) return null;

    const sorted = [...latencies].sort((a, b) => a - b);
    const len = sorted.length;

    return {
      p50: Math.round(sorted[Math.floor(len * 0.5)] * 100) / 100,
      p90: Math.round(sorted[Math.floor(len * 0.9)] * 100) / 100,
      p95: Math.round(sorted[Math.floor(len * 0.95)] * 100) / 100,
      p99: Math.round(sorted[Math.floor(len * 0.99)] * 100) / 100,
      avg: Math.round((latencies.reduce((sum, l) => sum + l, 0) / len) * 100) / 100
    };
  }

  /**
   * Executa suite completa de testes
   */
  async runFullSuite() {
    console.log('🚀 Iniciando testes de performance de ultra baixa latência...\n');
    
    const results = {
      timestamp: new Date().toISOString(),
      config: {
        targetLatencyP99: TARGET_LATENCY_P99,
        targetThroughput: TARGET_THROUGHPUT,
        targetSuccessRate: TARGET_SUCCESS_RATE,
        testDuration: TEST_DURATION,
        concurrentRequests: CONCURRENT_REQUESTS
      },
      tests: {}
    };

    // Teste 1: Health Check
    console.log('1️⃣ Teste de Health Check');
    results.tests.health = await this.testEndpointLatency('/health', 50);
    this.printTestResult('Health Check', results.tests.health);

    // Teste 2: Oportunidades de Arbitragem (endpoint principal)
    console.log('\n2️⃣ Teste de Oportunidades de Arbitragem');
    results.tests.opportunities = await this.testEndpointLatency('/api/arbitrage/opportunities', 20);
    this.printTestResult('Arbitrage Opportunities', results.tests.opportunities);

    // Teste 3: Dados das Exchanges
    console.log('\n3️⃣ Teste de Dados das Exchanges');
    results.tests.exchanges = await this.testEndpointLatency('/api/exchanges/data', 20);
    this.printTestResult('Exchange Data', results.tests.exchanges);

    // Teste 4: Throughput do endpoint principal
    console.log('\n4️⃣ Teste de Throughput');
    results.tests.throughput = await this.testThroughput('/api/arbitrage/opportunities', 30000);
    this.printThroughputResult('Throughput Test', results.tests.throughput);

    // Teste 5: WebSocket Status
    console.log('\n5️⃣ Teste de WebSocket Status');
    results.tests.websocket = await this.testEndpointLatency('/api/websocket/status', 30);
    this.printTestResult('WebSocket Status', results.tests.websocket);

    // Teste 6: Performance Metrics
    console.log('\n6️⃣ Teste de Performance Metrics');
    results.tests.performance = await this.testEndpointLatency('/api/performance/summary', 30);
    this.printTestResult('Performance Metrics', results.tests.performance);

    // Análise final
    console.log('\n📊 ANÁLISE FINAL DOS RESULTADOS');
    this.analyzeResults(results);

    return results;
  }

  /**
   * Imprime resultado de teste de latência
   */
  printTestResult(testName, result) {
    const { latency, successRate } = result;
    
    console.log(`   ✅ ${testName}:`);
    console.log(`      Sucesso: ${successRate}% (${result.successCount}/${result.count})`);
    console.log(`      Latência P50: ${latency.p50}ms`);
    console.log(`      Latência P90: ${latency.p90}ms`);
    console.log(`      Latência P95: ${latency.p95}ms`);
    console.log(`      Latência P99: ${latency.p99}ms`);
    console.log(`      Média: ${latency.avg}ms`);
    
    if (result.errorCount > 0) {
      console.log(`      ⚠️ Erros: ${result.errorCount}`);
    }
  }

  /**
   * Imprime resultado de teste de throughput
   */
  printThroughputResult(testName, result) {
    console.log(`   ✅ ${testName}:`);
    console.log(`      Requests: ${result.requestCount} em ${result.duration}ms`);
    console.log(`      Throughput: ${result.throughput} req/s`);
    console.log(`      Taxa de Sucesso: ${result.successRate}%`);
    
    if (result.latencyStats) {
      console.log(`      Latência P99: ${result.latencyStats.p99}ms`);
    }
  }

  /**
   * Analisa resultados finais
   */
  analyzeResults(results) {
    const issues = [];
    const successes = [];

    // Verificar latência P99
    Object.entries(results.tests).forEach(([testName, result]) => {
      if (result.latency && result.latency.p99 > TARGET_LATENCY_P99) {
        issues.push(`❌ ${testName}: P99 ${result.latency.p99}ms > ${TARGET_LATENCY_P99}ms`);
      } else if (result.latency) {
        successes.push(`✅ ${testName}: P99 ${result.latency.p99}ms ≤ ${TARGET_LATENCY_P99}ms`);
      }
    });

    // Verificar throughput
    if (results.tests.throughput && results.tests.throughput.throughput < TARGET_THROUGHPUT) {
      issues.push(`❌ Throughput: ${results.tests.throughput.throughput} req/s < ${TARGET_THROUGHPUT} req/s`);
    } else if (results.tests.throughput) {
      successes.push(`✅ Throughput: ${results.tests.throughput.throughput} req/s ≥ ${TARGET_THROUGHPUT} req/s`);
    }

    // Verificar taxa de sucesso
    Object.entries(results.tests).forEach(([testName, result]) => {
      if (result.successRate < TARGET_SUCCESS_RATE) {
        issues.push(`❌ ${testName}: Taxa de sucesso ${result.successRate}% < ${TARGET_SUCCESS_RATE}%`);
      } else {
        successes.push(`✅ ${testName}: Taxa de sucesso ${result.successRate}% ≥ ${TARGET_SUCCESS_RATE}%`);
      }
    });

    console.log('\n🎯 SUCESSOS:');
    successes.forEach(success => console.log(`   ${success}`));

    if (issues.length > 0) {
      console.log('\n⚠️ PROBLEMAS ENCONTRADOS:');
      issues.forEach(issue => console.log(`   ${issue}`));
      console.log('\n❌ TESTE FALHOU - Otimizações precisam de ajustes');
      process.exit(1);
    } else {
      console.log('\n🎉 TODOS OS TESTES PASSARAM!');
      console.log('✅ Sistema atende aos requisitos de ultra baixa latência');
      console.log('✅ Latência P99 < 1s');
      console.log('✅ Throughput > 250 req/s');
      console.log('✅ Taxa de sucesso > 99%');
    }
  }
}

// Executar testes se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  const test = new PerformanceTest();
  
  test.runFullSuite()
    .then(results => {
      console.log('\n📄 Salvando resultados em performance-results.json...');
      // Em um ambiente real, salvaria os resultados
      console.log('✅ Testes concluídos com sucesso!');
    })
    .catch(error => {
      console.error('❌ Erro durante os testes:', error);
      process.exit(1);
    });
}

export default PerformanceTest;
