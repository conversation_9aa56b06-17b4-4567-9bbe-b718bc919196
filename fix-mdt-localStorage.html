<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix MDT - Limpar localStorage</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .result { margin: 10px 0; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .info { background-color: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
        .warning { background-color: #fff3cd; color: #856404; border-left: 4px solid #ffc107; }
        button { margin: 10px 5px; padding: 10px 20px; font-size: 16px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Fix MDT - Limpar localStorage e Aplicar Novos Filtros</h1>
        
        <div class="result info">
            <h3>📋 Problema Identificado</h3>
            <p><strong>MDT Volume:</strong> 39.910.331 (39,9 milhões)</p>
            <p><strong>Filtro Antigo:</strong> 0 - 1.000.000 (1 milhão)</p>
            <p><strong>Resultado:</strong> MDT removida pelo filtro de volume</p>
        </div>
        
        <div class="result success">
            <h3>✅ Correção Aplicada</h3>
            <p><strong>Novo volumeRange:</strong> [0, 100000000] (100 milhões)</p>
            <p><strong>Novo spreadRange:</strong> [0, 100] (100%)</p>
            <p><strong>Novo priceRange:</strong> [0, 1000000] ($1M)</p>
        </div>
        
        <div class="result warning">
            <h3>⚠️ Ação Necessária</h3>
            <p>O localStorage pode estar mantendo os filtros antigos. Use os botões abaixo para corrigir:</p>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="btn-warning" onclick="checkLocalStorage()">🔍 Verificar localStorage</button>
            <button class="btn-primary" onclick="clearFilters()">🧹 Limpar Filtros</button>
            <button class="btn-success" onclick="setNewFilters()">✅ Aplicar Novos Filtros</button>
            <button class="btn-success" onclick="openFrontend()">🌐 Abrir Frontend</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        function checkLocalStorage() {
            const resultsDiv = document.getElementById('results');
            
            try {
                const stored = localStorage.getItem('crypto-arbitrage-filters');
                
                if (stored) {
                    const filters = JSON.parse(stored);
                    resultsDiv.innerHTML = `
                        <div class="result info">
                            <h3>📊 Filtros Atuais no localStorage</h3>
                            <div class="code">${JSON.stringify(filters, null, 2)}</div>
                            <p><strong>Volume Range:</strong> ${filters.volumeRange ? filters.volumeRange.join(' - ') : 'Não definido'}</p>
                            <p><strong>Spread Range:</strong> ${filters.spreadRange ? filters.spreadRange.join(' - ') : 'Não definido'}</p>
                            <p><strong>Price Range:</strong> ${filters.priceRange ? filters.priceRange.join(' - ') : 'Não definido'}</p>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ localStorage Limpo</h3>
                            <p>Nenhum filtro encontrado no localStorage. Os filtros padrão serão usados.</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Erro</h3>
                        <p>Erro ao verificar localStorage: ${error.message}</p>
                    </div>
                `;
            }
        }
        
        function clearFilters() {
            const resultsDiv = document.getElementById('results');
            
            try {
                localStorage.removeItem('crypto-arbitrage-filters');
                resultsDiv.innerHTML = `
                    <div class="result success">
                        <h3>✅ Filtros Limpos</h3>
                        <p>localStorage limpo com sucesso! Os filtros padrão serão aplicados na próxima carga.</p>
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Erro</h3>
                        <p>Erro ao limpar localStorage: ${error.message}</p>
                    </div>
                `;
            }
        }
        
        function setNewFilters() {
            const resultsDiv = document.getElementById('results');
            
            try {
                const newFilters = {
                    searchTerm: '',
                    spotExchange: 'all',
                    futuresExchange: 'all',
                    type: 'all',
                    profitability: 'all',
                    spreadRange: [0, 100],
                    volumeRange: [0, 1000000000], // 1 bilhão
                    priceRange: [0, 1000000],
                    timeframe: '1h',
                    autoRefresh: true,
                    realTimeUpdates: true,
                    notifications: false,
                    showOnlyHighVolume: false,
                    showOnlyRecentData: false,
                    hideStaleData: false
                };
                
                localStorage.setItem('crypto-arbitrage-filters', JSON.stringify(newFilters));
                
                resultsDiv.innerHTML = `
                    <div class="result success">
                        <h3>✅ Novos Filtros Aplicados</h3>
                        <p>Filtros atualizados com sucesso!</p>
                        <div class="code">${JSON.stringify(newFilters, null, 2)}</div>
                        <p><strong>Volume Range:</strong> 0 - 1.000.000.000 (1B) ✅</p>
                        <p><strong>Spread Range:</strong> 0 - 100% ✅</p>
                        <p><strong>Price Range:</strong> $0 - $1.000.000 ✅</p>
                        <p><strong>MDT deve passar agora!</strong> 🎯</p>
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Erro</h3>
                        <p>Erro ao definir novos filtros: ${error.message}</p>
                    </div>
                `;
            }
        }
        
        function openFrontend() {
            window.open('http://localhost:5173', '_blank');
        }
        
        // Verificar automaticamente ao carregar
        checkLocalStorage();
    </script>
</body>
</html>