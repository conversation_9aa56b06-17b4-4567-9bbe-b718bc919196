{"name": "crypto-arbitrage-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 5002", "dev:5002": "vite --port 5002", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview --port 5002", "preview:5002": "vite preview --port 5002", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.17.0", "axios": "^1.6.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "crypto-js": "^4.2.0", "lucide-react": "^0.312.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-window": "^1.8.8", "react-window-infinite-loader": "^1.0.9", "recharts": "^2.10.3", "tailwind-merge": "^2.2.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/crypto-js": "^4.2.2", "@types/node": "^20.10.5", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^23.0.1", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.1.0"}}