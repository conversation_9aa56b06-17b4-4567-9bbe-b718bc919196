<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitor de Performance - Sistema Otimizado</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { font-size: 2.5em; margin: 0; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header p { font-size: 1.2em; opacity: 0.9; margin: 10px 0; }
        
        .metrics-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 20px; 
            margin-bottom: 30px; 
        }
        
        .metric-card { 
            background: rgba(255,255,255,0.1); 
            backdrop-filter: blur(10px);
            border-radius: 15px; 
            padding: 20px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }
        
        .metric-card:hover { transform: translateY(-5px); }
        
        .metric-title { 
            font-size: 1.1em; 
            font-weight: bold; 
            margin-bottom: 15px; 
            display: flex; 
            align-items: center; 
            gap: 10px;
        }
        
        .metric-value { 
            font-size: 2.5em; 
            font-weight: bold; 
            margin: 10px 0; 
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        
        .metric-subtitle { 
            font-size: 0.9em; 
            opacity: 0.8; 
            margin-bottom: 10px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            border-radius: 4px;
            transition: width 0.5s ease;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online { background: #4CAF50; box-shadow: 0 0 10px #4CAF50; }
        .status-warning { background: #FF9800; box-shadow: 0 0 10px #FF9800; }
        .status-error { background: #F44336; box-shadow: 0 0 10px #F44336; }
        
        .test-section {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .test-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .btn-primary { background: #2196F3; color: white; }
        .btn-success { background: #4CAF50; color: white; }
        .btn-warning { background: #FF9800; color: white; }
        .btn-danger { background: #F44336; color: white; }
        
        .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.3); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        
        .log-container {
            background: rgba(0,0,0,0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 4px;
        }
        
        .log-success { background: rgba(76, 175, 80, 0.2); }
        .log-warning { background: rgba(255, 152, 0, 0.2); }
        .log-error { background: rgba(244, 67, 54, 0.2); }
        .log-info { background: rgba(33, 150, 243, 0.2); }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .loading { animation: pulse 1.5s infinite; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Monitor de Performance - Sistema Otimizado</h1>
            <p>Monitoramento em tempo real das otimizações implementadas</p>
            <p><span class="status-indicator status-online"></span>Sistema Online e Otimizado</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-title">⚡ Tempo de Resposta</div>
                <div class="metric-value" id="responseTime">--</div>
                <div class="metric-subtitle">Última requisição</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="responseProgress" style="width: 0%"></div>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">📊 Cache Hit Rate</div>
                <div class="metric-value" id="hitRate">--</div>
                <div class="metric-subtitle">Eficiência do cache</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="hitRateProgress" style="width: 0%"></div>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">🎯 Oportunidades</div>
                <div class="metric-value" id="opportunities">--</div>
                <div class="metric-subtitle">Total encontradas</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="opportunitiesProgress" style="width: 0%"></div>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">💾 Cache Status</div>
                <div class="metric-value" id="cacheSize">--</div>
                <div class="metric-subtitle">Entradas no cache</div>
                <div style="margin-top: 10px;">
                    <div>Hits: <span id="cacheHits">0</span></div>
                    <div>Misses: <span id="cacheMisses">0</span></div>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">🔥 Aceleração</div>
                <div class="metric-value" id="speedup">--</div>
                <div class="metric-subtitle">vs sistema anterior</div>
                <div id="improvement" style="margin-top: 10px; font-size: 1.2em; font-weight: bold;"></div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">📈 MDT Status</div>
                <div class="metric-value" id="mdtStatus">--</div>
                <div class="metric-subtitle">Oportunidade principal</div>
                <div id="mdtDetails" style="margin-top: 10px; font-size: 0.9em;"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🧪 Testes de Performance</h3>
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="testCache()">Testar Cache</button>
                <button class="btn btn-success" onclick="testMDT()">Verificar MDT</button>
                <button class="btn btn-warning" onclick="clearCache()">Limpar Cache</button>
                <button class="btn btn-danger" onclick="stressTest()">Teste de Stress</button>
            </div>
            
            <div class="log-container" id="logContainer">
                <div class="log-entry log-info">Sistema iniciado - Aguardando testes...</div>
            </div>
        </div>
    </div>

    <script>
        let baselineTime = 5172; // Tempo antes da otimização
        let testCount = 0;
        
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function updateMetric(id, value, suffix = '') {
            document.getElementById(id).textContent = value + suffix;
        }
        
        function updateProgress(id, percentage) {
            document.getElementById(id).style.width = percentage + '%';
        }
        
        async function testCache() {
            log('🔄 Iniciando teste de cache...', 'info');
            
            try {
                // Primeira requisição (cache miss esperado)
                log('📦 Teste 1: Cache Miss', 'warning');
                const start1 = Date.now();
                const response1 = await fetch('http://localhost:3003/api/arbitrage/opportunities');
                const data1 = await response1.json();
                const time1 = Date.now() - start1;
                
                log(`⏱️ Cache Miss: ${time1}ms`, 'warning');
                
                // Segunda requisição (cache hit esperado)
                await new Promise(resolve => setTimeout(resolve, 500));
                log('📦 Teste 2: Cache Hit', 'success');
                const start2 = Date.now();
                const response2 = await fetch('http://localhost:3003/api/arbitrage/opportunities');
                const data2 = await response2.json();
                const time2 = Date.now() - start2;
                
                log(`⚡ Cache Hit: ${time2}ms`, 'success');
                
                // Atualizar métricas
                updateMetric('responseTime', time2, 'ms');
                updateProgress('responseProgress', Math.max(0, 100 - (time2 / 100)));
                
                const speedup = baselineTime / time2;
                const improvement = ((baselineTime - time2) / baselineTime) * 100;
                
                updateMetric('speedup', speedup.toFixed(1), 'x');
                updateMetric('improvement', improvement.toFixed(1), '% mais rápido');
                updateMetric('opportunities', data2.opportunities?.length || 0);
                updateProgress('opportunitiesProgress', Math.min(100, (data2.opportunities?.length || 0) / 20));
                
                // Estatísticas do cache
                const cacheStats = await fetch('http://localhost:3003/api/cache/stats');
                const cacheData = await cacheStats.json();
                
                updateMetric('hitRate', cacheData.cache.hitRate.toFixed(1), '%');
                updateProgress('hitRateProgress', cacheData.cache.hitRate);
                updateMetric('cacheSize', cacheData.cache.size);
                updateMetric('cacheHits', cacheData.cache.hitCount);
                updateMetric('cacheMisses', cacheData.cache.missCount);
                
                log(`📊 Cache Stats: ${cacheData.cache.hitRate.toFixed(1)}% hit rate`, 'success');
                log(`🚀 Aceleração: ${speedup.toFixed(1)}x mais rápido!`, 'success');
                
            } catch (error) {
                log(`❌ Erro no teste: ${error.message}`, 'error');
            }
        }
        
        async function testMDT() {
            log('🎯 Verificando MDT...', 'info');
            
            try {
                const response = await fetch('http://localhost:3003/api/arbitrage/opportunities');
                const data = await response.json();
                
                const mdtOpportunities = data.opportunities.filter(opp => opp.symbol === 'MDT/USDT');
                
                if (mdtOpportunities.length > 0) {
                    const mdt = mdtOpportunities[0];
                    updateMetric('mdtStatus', '✅ Encontrada');
                    document.getElementById('mdtDetails').innerHTML = `
                        Spread: ${Math.abs(mdt.spreadPercentage).toFixed(3)}%<br>
                        Volume: ${Math.round(mdt.volume).toLocaleString()}<br>
                        ${mdt.spotExchange} → ${mdt.futuresExchange}
                    `;
                    log(`✅ MDT encontrada: ${Math.abs(mdt.spreadPercentage).toFixed(3)}% spread`, 'success');
                } else {
                    updateMetric('mdtStatus', '❌ Não encontrada');
                    document.getElementById('mdtDetails').innerHTML = 'MDT não está nas oportunidades atuais';
                    log('⚠️ MDT não encontrada nas oportunidades atuais', 'warning');
                }
                
            } catch (error) {
                log(`❌ Erro ao verificar MDT: ${error.message}`, 'error');
            }
        }
        
        async function clearCache() {
            log('🧹 Limpando cache...', 'warning');
            
            try {
                const response = await fetch('http://localhost:3003/api/cache/clear', {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.success) {
                    log('✅ Cache limpo com sucesso', 'success');
                    updateMetric('hitRate', '0', '%');
                    updateProgress('hitRateProgress', 0);
                    updateMetric('cacheSize', '0');
                    updateMetric('cacheHits', '0');
                    updateMetric('cacheMisses', '0');
                } else {
                    log('❌ Erro ao limpar cache', 'error');
                }
                
            } catch (error) {
                log(`❌ Erro: ${error.message}`, 'error');
            }
        }
        
        async function stressTest() {
            log('🔥 Iniciando teste de stress...', 'warning');
            
            const requests = 10;
            const times = [];
            
            for (let i = 1; i <= requests; i++) {
                log(`📡 Requisição ${i}/${requests}`, 'info');
                
                const start = Date.now();
                try {
                    const response = await fetch('http://localhost:3003/api/arbitrage/opportunities');
                    const data = await response.json();
                    const time = Date.now() - start;
                    times.push(time);
                    
                    log(`⏱️ Requisição ${i}: ${time}ms`, time < 100 ? 'success' : 'warning');
                } catch (error) {
                    log(`❌ Requisição ${i} falhou: ${error.message}`, 'error');
                }
                
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
            const minTime = Math.min(...times);
            const maxTime = Math.max(...times);
            
            log(`📊 Teste concluído:`, 'success');
            log(`   Tempo médio: ${avgTime.toFixed(0)}ms`, 'success');
            log(`   Tempo mínimo: ${minTime}ms`, 'success');
            log(`   Tempo máximo: ${maxTime}ms`, 'success');
            log(`   Aceleração média: ${(baselineTime / avgTime).toFixed(1)}x`, 'success');
        }
        
        // Auto-refresh a cada 30 segundos
        setInterval(async () => {
            if (testCount % 6 === 0) { // A cada 3 minutos, teste completo
                await testCache();
                await testMDT();
            } else { // Senão, apenas atualizar métricas básicas
                try {
                    const cacheStats = await fetch('http://localhost:3003/api/cache/stats');
                    const cacheData = await cacheStats.json();
                    
                    updateMetric('hitRate', cacheData.cache.hitRate.toFixed(1), '%');
                    updateProgress('hitRateProgress', cacheData.cache.hitRate);
                    updateMetric('cacheSize', cacheData.cache.size);
                    updateMetric('cacheHits', cacheData.cache.hitCount);
                    updateMetric('cacheMisses', cacheData.cache.missCount);
                } catch (error) {
                    // Silencioso
                }
            }
            testCount++;
        }, 30000);
        
        // Teste inicial
        setTimeout(() => {
            testCache();
            testMDT();
        }, 2000);
    </script>
</body>
</html>