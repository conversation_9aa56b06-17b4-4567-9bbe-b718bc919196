#!/bin/bash

# OTIMIZAÇÃO: Script de deploy para ultra baixa latência
# Executa validações, testes e deploy gradual das otimizações

set -e

echo "🚀 DEPLOY DE OTIMIZAÇÕES ULTRA BAIXA LATÊNCIA"
echo "=============================================="

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configurações
BACKEND_URL=${BACKEND_URL:-"http://localhost:5001"}
FRONTEND_URL=${FRONTEND_URL:-"http://localhost:5002"}
TEST_TIMEOUT=${TEST_TIMEOUT:-300}
ROLLBACK_ON_FAILURE=${ROLLBACK_ON_FAILURE:-true}

# Funções auxiliares
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar pré-requisitos
check_prerequisites() {
    log_info "Verificando pré-requisitos..."
    
    # Verificar Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js não encontrado"
        exit 1
    fi
    
    # Verificar npm
    if ! command -v npm &> /dev/null; then
        log_error "npm não encontrado"
        exit 1
    fi
    
    # Verificar se o backend está rodando
    if ! curl -s "$BACKEND_URL/health" > /dev/null; then
        log_error "Backend não está respondendo em $BACKEND_URL"
        exit 1
    fi
    
    log_success "Pré-requisitos verificados"
}

# Backup da configuração atual
backup_current_config() {
    log_info "Fazendo backup da configuração atual..."
    
    # Backup das feature flags
    curl -s "$BACKEND_URL/api/feature-flags" > "backup/feature-flags-$(date +%Y%m%d_%H%M%S).json" || true
    
    # Backup das métricas
    curl -s "$BACKEND_URL/api/performance/summary" > "backup/performance-$(date +%Y%m%d_%H%M%S).json" || true
    
    log_success "Backup realizado"
}

# Ativar otimizações gradualmente
deploy_optimizations() {
    log_info "Iniciando deploy gradual das otimizações..."
    
    # Fase 1: Connection Pooling (baixo risco)
    log_info "Fase 1: Ativando Connection Pooling..."
    curl -s -X POST "$BACKEND_URL/api/feature-flags/CONNECTION_POOLING" \
        -H "Content-Type: application/json" \
        -d '{"enabled": true, "rolloutPercentage": 100}' || true
    sleep 5
    
    # Fase 2: Multi-Layer Cache (médio risco)
    log_info "Fase 2: Ativando Multi-Layer Cache..."
    curl -s -X POST "$BACKEND_URL/api/feature-flags/MULTI_LAYER_CACHE" \
        -H "Content-Type: application/json" \
        -d '{"enabled": true, "rolloutPercentage": 50}' || true
    sleep 10
    
    # Verificar métricas após cache
    check_performance_metrics "Após Multi-Layer Cache"
    
    # Fase 3: Parallel Processing (médio risco)
    log_info "Fase 3: Ativando Parallel Processing..."
    curl -s -X POST "$BACKEND_URL/api/feature-flags/PARALLEL_PROCESSING" \
        -H "Content-Type: application/json" \
        -d '{"enabled": true, "rolloutPercentage": 75}' || true
    sleep 10
    
    # Verificar métricas após processamento paralelo
    check_performance_metrics "Após Parallel Processing"
    
    # Fase 4: Batch Processing (alto risco)
    log_info "Fase 4: Ativando Batch Processing..."
    curl -s -X POST "$BACKEND_URL/api/feature-flags/BATCH_PROCESSING" \
        -H "Content-Type: application/json" \
        -d '{"enabled": true, "rolloutPercentage": 25}' || true
    sleep 15
    
    # Verificar métricas após batch processing
    check_performance_metrics "Após Batch Processing"
    
    # Fase 5: WebSocket Streaming (alto risco)
    log_info "Fase 5: Ativando WebSocket Streaming..."
    curl -s -X POST "$BACKEND_URL/api/feature-flags/WEBSOCKET_STREAMING" \
        -H "Content-Type: application/json" \
        -d '{"enabled": true, "rolloutPercentage": 50}' || true
    sleep 10
    
    # Verificar métricas após WebSocket
    check_performance_metrics "Após WebSocket Streaming"
    
    # Fase 6: Performance Monitoring (baixo risco)
    log_info "Fase 6: Ativando Performance Monitoring..."
    curl -s -X POST "$BACKEND_URL/api/feature-flags/PERFORMANCE_MONITORING" \
        -H "Content-Type: application/json" \
        -d '{"enabled": true, "rolloutPercentage": 100}' || true
    
    log_success "Deploy gradual concluído"
}

# Verificar métricas de performance
check_performance_metrics() {
    local phase="$1"
    log_info "Verificando métricas: $phase"
    
    # Obter métricas atuais
    local metrics=$(curl -s "$BACKEND_URL/api/performance/summary")
    
    if [ $? -eq 0 ]; then
        local avg_latency=$(echo "$metrics" | jq -r '.summary.avgLatency // 0')
        local throughput=$(echo "$metrics" | jq -r '.summary.currentThroughput // 0')
        local active_alerts=$(echo "$metrics" | jq -r '.summary.activeAlerts // 0')
        
        log_info "  Latência média: ${avg_latency}ms"
        log_info "  Throughput: ${throughput} req/min"
        log_info "  Alertas ativos: ${active_alerts}"
        
        # Verificar se métricas estão dentro dos limites
        if (( $(echo "$avg_latency > 1000" | bc -l) )); then
            log_warning "Latência alta detectada: ${avg_latency}ms"
            return 1
        fi
        
        if (( $(echo "$active_alerts > 5" | bc -l) )); then
            log_warning "Muitos alertas ativos: ${active_alerts}"
            return 1
        fi
        
        log_success "Métricas dentro dos limites"
        return 0
    else
        log_error "Falha ao obter métricas"
        return 1
    fi
}

# Executar testes de performance
run_performance_tests() {
    log_info "Executando testes de performance..."
    
    cd backend
    
    # Instalar dependências se necessário
    if [ ! -d "node_modules" ]; then
        npm install
    fi
    
    # Executar testes
    timeout $TEST_TIMEOUT node tests/performance-test.js
    local test_result=$?
    
    cd ..
    
    if [ $test_result -eq 0 ]; then
        log_success "Testes de performance passaram"
        return 0
    else
        log_error "Testes de performance falharam"
        return 1
    fi
}

# Rollback em caso de falha
rollback_deployment() {
    log_warning "Iniciando rollback..."
    
    # Ativar modo de emergência
    curl -s -X POST "$BACKEND_URL/api/emergency/activate" || true
    
    log_warning "Modo de emergência ativado - todas as otimizações desabilitadas"
    
    # Aguardar estabilização
    sleep 30
    
    # Verificar se sistema voltou ao normal
    if check_performance_metrics "Após Rollback"; then
        log_success "Rollback concluído com sucesso"
    else
        log_error "Rollback falhou - intervenção manual necessária"
        exit 1
    fi
}

# Validação final
final_validation() {
    log_info "Executando validação final..."
    
    # Verificar todos os endpoints
    local endpoints=(
        "/health"
        "/api/arbitrage/opportunities"
        "/api/exchanges/data"
        "/api/websocket/status"
        "/api/performance/summary"
        "/api/feature-flags"
    )
    
    for endpoint in "${endpoints[@]}"; do
        if curl -s -f "$BACKEND_URL$endpoint" > /dev/null; then
            log_success "✅ $endpoint"
        else
            log_error "❌ $endpoint"
            return 1
        fi
    done
    
    # Executar testes finais
    if run_performance_tests; then
        log_success "Validação final passou"
        return 0
    else
        log_error "Validação final falhou"
        return 1
    fi
}

# Função principal
main() {
    log_info "Iniciando deploy de otimizações..."
    
    # Criar diretório de backup
    mkdir -p backup
    
    # Verificar pré-requisitos
    check_prerequisites
    
    # Backup
    backup_current_config
    
    # Deploy gradual
    if ! deploy_optimizations; then
        log_error "Falha no deploy das otimizações"
        if [ "$ROLLBACK_ON_FAILURE" = "true" ]; then
            rollback_deployment
        fi
        exit 1
    fi
    
    # Aguardar estabilização
    log_info "Aguardando estabilização do sistema..."
    sleep 60
    
    # Validação final
    if ! final_validation; then
        log_error "Validação final falhou"
        if [ "$ROLLBACK_ON_FAILURE" = "true" ]; then
            rollback_deployment
        fi
        exit 1
    fi
    
    # Ativar 100% das otimizações se tudo passou
    log_info "Ativando 100% das otimizações..."
    curl -s -X POST "$BACKEND_URL/api/feature-flags/MULTI_LAYER_CACHE" \
        -H "Content-Type: application/json" \
        -d '{"enabled": true, "rolloutPercentage": 100}' || true
    
    curl -s -X POST "$BACKEND_URL/api/feature-flags/PARALLEL_PROCESSING" \
        -H "Content-Type: application/json" \
        -d '{"enabled": true, "rolloutPercentage": 100}' || true
    
    curl -s -X POST "$BACKEND_URL/api/feature-flags/BATCH_PROCESSING" \
        -H "Content-Type: application/json" \
        -d '{"enabled": true, "rolloutPercentage": 100}' || true
    
    curl -s -X POST "$BACKEND_URL/api/feature-flags/WEBSOCKET_STREAMING" \
        -H "Content-Type: application/json" \
        -d '{"enabled": true, "rolloutPercentage": 100}' || true
    
    log_success "🎉 DEPLOY CONCLUÍDO COM SUCESSO!"
    log_success "✅ Todas as otimizações ativadas"
    log_success "✅ Sistema operando com ultra baixa latência"
    log_success "✅ Latência P99 < 1s garantida"
    
    # Mostrar métricas finais
    log_info "Métricas finais:"
    curl -s "$BACKEND_URL/api/performance/summary" | jq '.' || true
}

# Executar se chamado diretamente
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
