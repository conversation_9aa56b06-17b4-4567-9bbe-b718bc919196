// AlertSystem.test.ts - Testes básicos para AlertSystem

import { describe, it, expect } from 'vitest'
import { AlertSystem } from '../services/AlertSystem'

describe('AlertSystem', () => {
  it('should return singleton instance', () => {
    const instance1 = AlertSystem.getInstance()
    const instance2 = AlertSystem.getInstance()
    expect(instance1).toBe(instance2)
  })

  it('should have checkCrossExchangeSpreadAlert method', () => {
    const alertSystem = AlertSystem.getInstance()
    expect(typeof alertSystem.checkCrossExchangeSpreadAlert).toBe('function')
  })

  it('should have triggerAlert method', () => {
    const alertSystem = AlertSystem.getInstance()
    expect(typeof alertSystem.triggerAlert).toBe('function')
  })

  it('should have cleanupAlertHistory method', () => {
    const alertSystem = AlertSystem.getInstance()
    expect(typeof alertSystem.cleanupAlertHistory).toBe('function')
  })
})