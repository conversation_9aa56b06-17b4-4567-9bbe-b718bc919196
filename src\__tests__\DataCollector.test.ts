// DataCollector.test.ts - Testes básicos para DataCollector

import { describe, it, expect } from 'vitest'
import { DataCollector } from '../services/DataCollector'

describe('DataCollector', () => {
  it('should return singleton instance', () => {
    const instance1 = DataCollector.getInstance()
    const instance2 = DataCollector.getInstance()
    expect(instance1).toBe(instance2)
  })

  it('should have collectAllData method', () => {
    const dataCollector = DataCollector.getInstance()
    expect(typeof dataCollector.collectAllData).toBe('function')
  })

  it('should have calculateDashboardMetrics method', () => {
    const dataCollector = DataCollector.getInstance()
    expect(typeof dataCollector.calculateDashboardMetrics).toBe('function')
  })

  it('should have classifyProfitability method', () => {
    const dataCollector = DataCollector.getInstance()
    expect(typeof dataCollector.classifyProfitability).toBe('function')
  })

  it('should classify profitability correctly', () => {
    const dataCollector = DataCollector.getInstance()
    expect(dataCollector.classifyProfitability(1.5)).toBe('high')
    expect(dataCollector.classifyProfitability(0.8)).toBe('medium')
    expect(dataCollector.classifyProfitability(0.3)).toBe('low')
  })
})