// DataNormalizer.test.ts - Testes para normalização de dados

import { describe, it, expect } from 'vitest'
import { DataNormalizer } from '../services/data/DataNormalizer'

describe('DataNormalizer', () => {
  describe('normalizeGateioData', () => {
    it('should normalize Gate.io spot data correctly', () => {
      const rawData = {
        currency_pair: 'BTC_USDT',
        last: '45000.5',
        lowest_ask: '45001.0',
        highest_bid: '44999.5',
        base_volume: '100.5',
        quote_volume: '4522522.5'
      }

      const normalized = DataNormalizer.normalizeGateioData(rawData, 'spot')

      expect(normalized.symbol).toBe('BTC/USDT')
      expect(normalized.price).toBe(45000.5)
      expect(normalized.ask).toBe(45001.0)
      expect(normalized.bid).toBe(44999.5)
      expect(normalized.volume).toBe(4522522.5)
      expect(normalized.exchange).toBe('gateio')
      expect(normalized.type).toBe('spot')
    })

    it('should normalize Gate.io futures data correctly', () => {
      const rawData = {
        contract: 'BTC_USDT',
        last: '45100.0',
        ask1_price: '45101.0',
        bid1_price: '45099.0',
        volume_24h: '1000000',
        funding_rate: '0.0001'
      }

      const normalized = DataNormalizer.normalizeGateioData(rawData, 'futures')

      expect(normalized.symbol).toBe('BTC/USDT')
      expect(normalized.price).toBe(45100.0)
      expect(normalized.ask).toBe(45101.0)
      expect(normalized.bid).toBe(45099.0)
      expect(normalized.volume).toBe(1000000)
      expect(normalized.fundingRate).toBe(0.0001)
      expect(normalized.contractType).toBe('PERP')
    })
  })

  describe('normalizeMexcData', () => {
    it('should normalize MEXC spot data correctly', () => {
      const rawData = {
        symbol: 'BTCUSDT',
        price: '45000.5',
        askPrice: '45001.0',
        bidPrice: '44999.5',
        volume: '100.5',
        quoteVolume: '4522522.5'
      }

      const normalized = DataNormalizer.normalizeMexcData(rawData, 'spot')

      expect(normalized.symbol).toBe('BTC/USDT')
      expect(normalized.price).toBe(45000.5)
      expect(normalized.ask).toBe(45001.0)
      expect(normalized.bid).toBe(44999.5)
      expect(normalized.volume).toBe(4522522.5)
      expect(normalized.exchange).toBe('mexc')
      expect(normalized.type).toBe('spot')
    })

    it('should normalize MEXC futures data correctly', () => {
      const rawData = {
        symbol: 'BTC_USDT',
        lastPrice: '45100.0',
        askPrice: '45101.0',
        bidPrice: '45099.0',
        volume24h: '1000000',
        fundingRate: '0.0001'
      }

      const normalized = DataNormalizer.normalizeMexcData(rawData, 'futures')

      expect(normalized.symbol).toBe('BTC/USDT')
      expect(normalized.price).toBe(45100.0)
      expect(normalized.ask).toBe(45101.0)
      expect(normalized.bid).toBe(45099.0)
      expect(normalized.volume).toBe(1000000)
      expect(normalized.fundingRate).toBe(0.0001)
    })
  })

  describe('normalizeBitgetData', () => {
    it('should normalize Bitget spot data correctly', () => {
      const rawData = {
        symbol: 'BTCUSDT',
        close: '45000.5',
        askPr: '45001.0',
        bidPr: '44999.5',
        baseVol: '100.5',
        quoteVol: '4522522.5'
      }

      const normalized = DataNormalizer.normalizeBitgetData(rawData, 'spot')

      expect(normalized.symbol).toBe('BTC/USDT')
      expect(normalized.price).toBe(45000.5)
      expect(normalized.ask).toBe(45001.0)
      expect(normalized.bid).toBe(44999.5)
      expect(normalized.volume).toBe(4522522.5)
      expect(normalized.exchange).toBe('bitget')
      expect(normalized.type).toBe('spot')
    })

    it('should normalize Bitget futures data correctly', () => {
      const rawData = {
        symbol: 'BTCUSDT_UMCBL',
        last: '45100.0',
        askPr: '45101.0',
        bidPr: '45099.0',
        baseVolume: '1000000',
        fundingRate: '0.0001'
      }

      const normalized = DataNormalizer.normalizeBitgetData(rawData, 'futures')

      expect(normalized.symbol).toBe('BTC/USDT')
      expect(normalized.price).toBe(45100.0)
      expect(normalized.ask).toBe(45101.0)
      expect(normalized.bid).toBe(45099.0)
      expect(normalized.volume).toBe(1000000)
      expect(normalized.fundingRate).toBe(0.0001)
    })
  })

  describe('normalizeSymbol', () => {
    it('should normalize different symbol formats', () => {
      expect(DataNormalizer.normalizeSymbol('BTC_USDT')).toBe('BTC/USDT')
      expect(DataNormalizer.normalizeSymbol('BTCUSDT')).toBe('BTC/USDT')
      expect(DataNormalizer.normalizeSymbol('BTC-USDT')).toBe('BTC/USDT')
      expect(DataNormalizer.normalizeSymbol('BTC/USDT')).toBe('BTC/USDT')
    })

    it('should handle edge cases', () => {
      expect(DataNormalizer.normalizeSymbol('')).toBe('')
      expect(DataNormalizer.normalizeSymbol('BTC')).toBe('BTC')
      expect(DataNormalizer.normalizeSymbol('BTCUSDT_UMCBL')).toBe('BTC/USDT')
    })
  })

  describe('validateNormalizedData', () => {
    it('should validate correct data', () => {
      const data = {
        symbol: 'BTC/USDT',
        price: 45000,
        volume: 1000000,
        bid: 44999,
        ask: 45001,
        exchange: 'gateio' as const,
        type: 'spot' as const,
        timestamp: new Date(),
        responseTime: 100,
        dataQuality: 0.95,
        isStale: false
      }

      expect(DataNormalizer.validateNormalizedData(data)).toBe(true)
    })

    it('should reject invalid data', () => {
      const invalidData = {
        symbol: '',
        price: -1,
        volume: -1000,
        bid: 0,
        ask: 0,
        exchange: 'gateio' as const,
        type: 'spot' as const,
        timestamp: new Date(),
        responseTime: 100,
        dataQuality: 0.95,
        isStale: false
      }

      expect(DataNormalizer.validateNormalizedData(invalidData)).toBe(false)
    })

    it('should reject data with invalid bid/ask spread', () => {
      const data = {
        symbol: 'BTC/USDT',
        price: 45000,
        volume: 1000000,
        bid: 45001, // Bid higher than ask
        ask: 44999,
        exchange: 'gateio' as const,
        type: 'spot' as const,
        timestamp: new Date(),
        responseTime: 100,
        dataQuality: 0.95,
        isStale: false
      }

      expect(DataNormalizer.validateNormalizedData(data)).toBe(false)
    })
  })

  describe('calculateDataQuality', () => {
    it('should calculate high quality score', () => {
      const data = {
        symbol: 'BTC/USDT',
        price: 45000,
        volume: 1000000,
        bid: 44999,
        ask: 45001,
        responseTime: 100,
        timestamp: new Date(Date.now() - 1000) // 1 second ago
      }

      const quality = DataNormalizer.calculateDataQuality(data)
      expect(quality).toBeGreaterThan(0.9)
    })

    it('should calculate lower quality for stale data', () => {
      const data = {
        symbol: 'BTC/USDT',
        price: 45000,
        volume: 1000000,
        bid: 44999,
        ask: 45001,
        responseTime: 100,
        timestamp: new Date(Date.now() - 35000) // 35 seconds ago
      }

      const quality = DataNormalizer.calculateDataQuality(data)
      expect(quality).toBeLessThan(0.9)
    })

    it('should calculate lower quality for slow response', () => {
      const data = {
        symbol: 'BTC/USDT',
        price: 45000,
        volume: 1000000,
        bid: 44999,
        ask: 45001,
        responseTime: 5000, // 5 seconds
        timestamp: new Date()
      }

      const quality = DataNormalizer.calculateDataQuality(data)
      expect(quality).toBeLessThan(0.9)
    })
  })
})