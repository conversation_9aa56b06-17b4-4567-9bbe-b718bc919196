// ExchangeAPI.test.ts - Testes completos para ExchangeAPI

import { ExchangeAPI } from '../services/ExchangeAPI'

describe('ExchangeAPI', () => {
  let exchangeAPI: ExchangeAPI

  beforeEach(() => {
    exchangeAPI = ExchangeAPI.getInstance()
  })

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = ExchangeAPI.getInstance()
      const instance2 = ExchangeAPI.getInstance()
      expect(instance1).toBe(instance2)
    })
  })

  describe('getAllCompleteDataWithFallback', () => {
    it('should return mock data with correct structure', async () => {
      const result = await exchangeAPI.getAllCompleteDataWithFallback()

      expect(result).toHaveProperty('allSpotData')
      expect(result).toHaveProperty('allFuturesData')
      expect(result).toHaveProperty('metadata')
      
      expect(Array.isArray(result.allSpotData)).toBe(true)
      expect(Array.isArray(result.allFuturesData)).toBe(true)
      expect(result.allSpotData.length).toBeGreaterThan(0)
      expect(result.allFuturesData.length).toBeGreaterThan(0)
    }, 15000) // 15s timeout para APIs reais

    it('should include all exchanges in data', async () => {
      const result = await exchangeAPI.getAllCompleteDataWithFallback()
      
      const spotExchanges = new Set(result.allSpotData.map(item => item.exchange))
      const futuresExchanges = new Set(result.allFuturesData.map(item => item.exchange))
      
      // Verificar se pelo menos 1 exchange está funcionando (produção real pode ter falhas temporárias)
      const expectedExchanges = ['gateio', 'mexc', 'bitget']
      const workingSpotExchanges = expectedExchanges.filter(ex => spotExchanges.has(ex))
      const workingFuturesExchanges = expectedExchanges.filter(ex => futuresExchanges.has(ex))
      
      expect(workingSpotExchanges.length).toBeGreaterThanOrEqual(1)
      expect(workingFuturesExchanges.length).toBeGreaterThanOrEqual(1)
      
      // Verificar se temos dados suficientes
      expect(result.allSpotData.length).toBeGreaterThan(100)
      expect(result.allFuturesData.length).toBeGreaterThan(50)
    }, 15000) // 15s timeout para APIs reais

    it('should generate valid price data', async () => {
      const result = await exchangeAPI.getAllCompleteDataWithFallback()
      
      result.allSpotData.forEach(item => {
        expect(item.price).toBeGreaterThan(0)
        expect(item.volume).toBeGreaterThanOrEqual(0) // Volume pode ser 0 em alguns pares
        expect(item.bid).toBeGreaterThan(0)
        expect(item.ask).toBeGreaterThan(0)
        expect(item.bid).toBeLessThan(item.ask)
      })
    }, 15000) // 15s timeout para APIs reais
  })

  describe('getBasePrice', () => {
    it('should return correct base prices', () => {
      expect(exchangeAPI['getBasePrice']('BTC/USDT')).toBe(45000)
      expect(exchangeAPI['getBasePrice']('ETH/USDT')).toBe(2500)
      expect(exchangeAPI['getBasePrice']('UNKNOWN/USDT')).toBe(1)
    })
  })
})