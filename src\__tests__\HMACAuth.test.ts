// HMACAuth.test.ts - Testes para autenticação HMAC

import { describe, it, expect, vi } from 'vitest'
import { HMACAuthStatic as HMACAuth } from '../services/auth/HMACAuth'

// Mock crypto
const mockCrypto = {
  createHmac: vi.fn(() => ({
    update: vi.fn().mockReturnThis(),
    digest: vi.fn(() => 'mocked-signature')
  }))
}

vi.mock('crypto', () => mockCrypto)

describe('HMACAuth', () => {
  describe('createGateioAuth', () => {
    it('should create Gate.io authentication', () => {
      const auth = HMACAuth.createGateioAuth('test-key', 'test-secret')
      
      expect(auth).toBeDefined()
      expect(typeof auth.generateSignature).toBe('function')
      expect(typeof auth.getHeaders).toBe('function')
    })

    it('should generate Gate.io signature', () => {
      const auth = HM<PERSON>Auth.createGateioAuth('test-key', 'test-secret')
      const signature = auth.generateSignature('GET', '/api/v4/spot/tickers', '', '1234567890')
      
      expect(typeof signature).toBe('string')
      expect(signature.length).toBeGreaterThan(0)
    })

    it('should generate Gate.io headers', () => {
      const auth = HMACAuth.createGateioAuth('test-key', 'test-secret')
      const headers = auth.getHeaders('GET', '/api/v4/spot/tickers', '', '1234567890')
      
      expect(headers).toHaveProperty('KEY', 'test-key')
      expect(headers).toHaveProperty('Timestamp')
      expect(headers).toHaveProperty('SIGN')
      expect(typeof headers.SIGN).toBe('string')
    })
  })

  describe('createMexcAuth', () => {
    it('should create MEXC authentication', () => {
      const auth = HMACAuth.createMexcAuth('test-key', 'test-secret')
      
      expect(auth).toBeDefined()
      expect(typeof auth.generateSignature).toBe('function')
      expect(typeof auth.getHeaders).toBe('function')
    })

    it('should generate MEXC signature', () => {
      const auth = HMACAuth.createMexcAuth('test-key', 'test-secret')
      const signature = auth.generateSignature('symbol=BTCUSDT&timestamp=1234567890')
      
      expect(typeof signature).toBe('string')
      expect(signature.length).toBeGreaterThan(0)
    })

    it('should generate MEXC headers', () => {
      const auth = HMACAuth.createMexcAuth('test-key', 'test-secret')
      const headers = auth.getHeaders('symbol=BTCUSDT&timestamp=1234567890', '1234567890')
      
      expect(headers).toHaveProperty('X-MEXC-APIKEY', 'test-key')
      expect(headers).toHaveProperty('Content-Type', 'application/json')
      expect(headers).toHaveProperty('Accept', 'application/json')
    })
  })

  describe('createBitgetAuth', () => {
    it('should create Bitget authentication', () => {
      const auth = HMACAuth.createBitgetAuth('test-key', 'test-secret', 'test-passphrase')
      
      expect(auth).toBeDefined()
      expect(typeof auth.generateSignature).toBe('function')
      expect(typeof auth.getHeaders).toBe('function')
    })

    it('should generate Bitget signature', () => {
      const auth = HMACAuth.createBitgetAuth('test-key', 'test-secret', 'test-passphrase')
      const signature = auth.generateSignature('1234567890', 'GET', '/api/spot/v1/market/tickers', '')
      
      expect(typeof signature).toBe('string')
      expect(signature.length).toBeGreaterThan(0)
    })

    it('should generate Bitget headers', () => {
      const auth = HMACAuth.createBitgetAuth('test-key', 'test-secret', 'test-passphrase')
      const headers = auth.getHeaders('1234567890', 'GET', '/api/spot/v1/market/tickers', '')
      
      expect(headers).toHaveProperty('ACCESS-KEY', 'test-key')
      expect(headers).toHaveProperty('ACCESS-PASSPHRASE', 'test-passphrase')
      expect(headers).toHaveProperty('ACCESS-TIMESTAMP')
      expect(headers).toHaveProperty('ACCESS-SIGN')
      expect(typeof headers['ACCESS-SIGN']).toBe('string')
    })
  })

  describe('validateCredentials', () => {
    it('should validate Gate.io credentials', () => {
      expect(HMACAuth.validateCredentials('gateio', 'key', 'secret')).toBe(true)
      expect(HMACAuth.validateCredentials('gateio', '', 'secret')).toBe(false)
      expect(HMACAuth.validateCredentials('gateio', 'key', '')).toBe(false)
    })

    it('should validate MEXC credentials', () => {
      expect(HMACAuth.validateCredentials('mexc', 'key', 'secret')).toBe(true)
      expect(HMACAuth.validateCredentials('mexc', '', 'secret')).toBe(false)
      expect(HMACAuth.validateCredentials('mexc', 'key', '')).toBe(false)
    })

    it('should validate Bitget credentials', () => {
      expect(HMACAuth.validateCredentials('bitget', 'key', 'secret', 'passphrase')).toBe(true)
      expect(HMACAuth.validateCredentials('bitget', 'key', 'secret')).toBe(false) // Missing passphrase
      expect(HMACAuth.validateCredentials('bitget', '', 'secret', 'passphrase')).toBe(false)
    })

    it('should handle unknown exchange', () => {
      expect(HMACAuth.validateCredentials('unknown' as any, 'key', 'secret')).toBe(false)
    })
  })
})