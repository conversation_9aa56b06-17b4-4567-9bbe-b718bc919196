// calculations.test.ts - Testes para utilitários de cálculo

import { describe, it, expect } from 'vitest'
import { 
  calculateSpread, 
  calculateProfitability, 
  formatCurrency, 
  formatPercentage,
  calculateVolume,
  calculateRisk
} from '../utils/calculations'

describe('calculations utilities', () => {
  describe('calculateSpread', () => {
    it('should calculate positive spread correctly', () => {
      const result = calculateSpread(45000, 45150)
      expect(result).toBeCloseTo(0.333, 2)
    })

    it('should calculate negative spread correctly', () => {
      const result = calculateSpread(45150, 45000)
      expect(result).toBeCloseTo(-0.333, 2)
    })

    it('should handle zero spread', () => {
      const result = calculateSpread(45000, 45000)
      expect(result).toBe(0)
    })

    it('should handle edge cases', () => {
      expect(calculateSpread(0, 100)).toBe(Infinity)
      expect(calculateSpread(100, 0)).toBe(-100)
    })
  })

  describe('calculateProfitability', () => {
    it('should classify high profitability', () => {
      expect(calculateProfitability(1.5)).toBe('high')
      expect(calculateProfitability(2.0)).toBe('high')
    })

    it('should classify medium profitability', () => {
      expect(calculateProfitability(0.8)).toBe('medium')
      expect(calculateProfitability(0.5)).toBe('medium')
    })

    it('should classify low profitability', () => {
      expect(calculateProfitability(0.3)).toBe('low')
      expect(calculateProfitability(0.1)).toBe('low')
    })

    it('should handle negative values', () => {
      expect(calculateProfitability(-1.5)).toBe('high')
      expect(calculateProfitability(-0.8)).toBe('medium')
      expect(calculateProfitability(-0.3)).toBe('low')
    })
  })

  describe('formatCurrency', () => {
    it('should format currency correctly', () => {
      expect(formatCurrency(1234.56)).toBe('$1,234.56')
      expect(formatCurrency(1000000)).toBe('$1,000,000.00')
      expect(formatCurrency(0.123456)).toBe('$0.12')
    })

    it('should handle negative values', () => {
      expect(formatCurrency(-1234.56)).toBe('-$1,234.56')
    })

    it('should handle zero', () => {
      expect(formatCurrency(0)).toBe('$0.00')
    })
  })

  describe('formatPercentage', () => {
    it('should format percentage correctly', () => {
      expect(formatPercentage(0.1234)).toBe('12.34%')
      expect(formatPercentage(1.5)).toBe('150.00%')
      expect(formatPercentage(0)).toBe('0.00%')
    })

    it('should handle negative percentages', () => {
      expect(formatPercentage(-0.1234)).toBe('-12.34%')
    })

    it('should respect decimal places', () => {
      expect(formatPercentage(0.123456, 3)).toBe('12.346%')
      expect(formatPercentage(0.123456, 1)).toBe('12.3%')
    })
  })

  describe('calculateVolume', () => {
    it('should return minimum volume', () => {
      expect(calculateVolume(100000, 80000)).toBe(80000)
      expect(calculateVolume(50000, 75000)).toBe(50000)
    })

    it('should handle equal volumes', () => {
      expect(calculateVolume(100000, 100000)).toBe(100000)
    })

    it('should handle zero volumes', () => {
      expect(calculateVolume(0, 100000)).toBe(0)
      expect(calculateVolume(100000, 0)).toBe(0)
    })
  })

  describe('calculateRisk', () => {
    it('should calculate risk score correctly', () => {
      const result = calculateRisk(1.5, 100000, 0.1)
      expect(result).toBeGreaterThan(0)
      expect(result).toBeLessThanOrEqual(100)
    })

    it('should return higher risk for higher spread', () => {
      const risk1 = calculateRisk(0.5, 100000, 0.1)
      const risk2 = calculateRisk(2.0, 100000, 0.1)
      expect(risk2).toBeGreaterThan(risk1)
    })

    it('should return higher risk for lower volume', () => {
      const risk1 = calculateRisk(1.0, 100000, 0.1)
      const risk2 = calculateRisk(1.0, 10000, 0.1)
      expect(risk2).toBeGreaterThan(risk1)
    })

    it('should handle edge cases', () => {
      expect(calculateRisk(0, 0, 0)).toBe(0)
      expect(calculateRisk(10, 1000000, 0.01)).toBeLessThanOrEqual(100)
    })
  })
})