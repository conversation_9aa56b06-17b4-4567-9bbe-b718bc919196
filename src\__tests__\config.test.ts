// Testes para configurações centralizadas

import { describe, it, expect } from 'vitest'
import {
  EXCHANGE_CONFIG,
  CACHE_CONFIG,
  VALIDATION_THRESHOLDS,
  PROFITABILITY_CONFIG,
  ALERT_CONFIG,
  PERFORMANCE_CONFIG,
  CURRENCY_CATEGORIES,
  getExchangeConfig,
  classifyProfitability,
  isValidCrossExchangeOpportunity,
  getCurrencyCategory,
  getMinRequirements,
  shouldProcessSymbol,
  getExchangeUrls,
  getExchangeFee,
  getRateLimit
} from '@/config/arbitrage'

describe('Configurações Centralizadas', () => {
  describe('Exchange Configuration', () => {
    it('should have all required exchanges configured', () => {
      expect(EXCHANGE_CONFIG.gateio).toBeDefined()
      expect(EXCHANGE_CONFIG.mexc).toBeDefined()
      expect(EXCHANGE_CONFIG.bitget).toBeDefined()
    })

    it('should have correct exchange properties', () => {
      const gateio = EXCHANGE_CONFIG.gateio
      expect(gateio.name).toBe('Gate.io')
      expect(gateio.spotPairs).toBe(2670)
      expect(gateio.futuresPairs).toBe(602)
      expect(gateio.fee).toBe(0.1)
      expect(gateio.api.authType).toBe('HMAC_SHA512')
    })

    it('should return correct exchange config', () => {
      const config = getExchangeConfig('gateio')
      expect(config.name).toBe('Gate.io')
      expect(config.api.baseUrl).toBe('https://api.gateio.ws')
    })
  })

  describe('Cache Configuration', () => {
    it('should have multi-layer cache config', () => {
      expect(CACHE_CONFIG.L1.TTL).toBe(2000)
      expect(CACHE_CONFIG.L2.TTL).toBe(5000)
      expect(CACHE_CONFIG.L3.TTL).toBe(10000)
    })

    it('should have reasonable cache limits', () => {
      expect(CACHE_CONFIG.L1.MAX_ENTRIES).toBeLessThanOrEqual(1000)
      expect(CACHE_CONFIG.L2.MAX_ENTRIES).toBeLessThanOrEqual(5000)
      expect(CACHE_CONFIG.L3.MAX_ENTRIES).toBeLessThanOrEqual(10000)
    })
  })

  describe('Validation Thresholds', () => {
    it('should have correct validation thresholds', () => {
      expect(VALIDATION_THRESHOLDS.MIN_SPREAD_PERCENTAGE).toBe(0.05)
      expect(VALIDATION_THRESHOLDS.MIN_VOLUME_USD).toBe(1000)
      expect(VALIDATION_THRESHOLDS.MAX_DATA_AGE_MS).toBe(15000)
    })

    it('should validate cross-exchange opportunities correctly', () => {
      expect(isValidCrossExchangeOpportunity('gateio', 'mexc', 0.1, 5000, 10000)).toBe(true)
      expect(isValidCrossExchangeOpportunity('gateio', 'gateio', 0.1, 5000, 10000)).toBe(false)
      expect(isValidCrossExchangeOpportunity('gateio', 'mexc', 0.01, 5000, 10000)).toBe(false)
    })
  })

  describe('Profitability Classification', () => {
    it('should classify profitability correctly', () => {
      expect(classifyProfitability(1.5)).toBe('high')
      expect(classifyProfitability(0.7)).toBe('medium')
      expect(classifyProfitability(0.1)).toBe('low')
    })

    it('should have correct profitability thresholds', () => {
      expect(PROFITABILITY_CONFIG.HIGH.threshold).toBe(1.0)
      expect(PROFITABILITY_CONFIG.MEDIUM.threshold).toBe(0.5)
      expect(PROFITABILITY_CONFIG.LOW.threshold).toBe(0.05)
    })
  })

  describe('Alert Configuration', () => {
    it('should have alert thresholds configured', () => {
      expect(ALERT_CONFIG.THRESHOLDS.VISUAL_ALERT).toBe(0.5)
      expect(ALERT_CONFIG.THRESHOLDS.SOUND_ALERT).toBe(1.0)
      expect(ALERT_CONFIG.THRESHOLDS.HIGH_PRIORITY).toBe(2.0)
    })

    it('should have notification settings', () => {
      expect(ALERT_CONFIG.NOTIFICATIONS.SOUND_ENABLED).toBe(true)
      expect(ALERT_CONFIG.NOTIFICATIONS.VIBRATION_ENABLED).toBe(true)
      expect(ALERT_CONFIG.NOTIFICATIONS.PUSH_NOTIFICATIONS).toBe(true)
    })
  })

  describe('Performance Configuration', () => {
    it('should have performance targets', () => {
      expect(PERFORMANCE_CONFIG.TARGETS.INITIAL_LOAD_TIME).toBe(2000)
      expect(PERFORMANCE_CONFIG.TARGETS.UPDATE_LATENCY).toBe(100)
    })

    it('should have optimization settings', () => {
      expect(PERFORMANCE_CONFIG.OPTIMIZATION.VIRTUALIZATION_THRESHOLD).toBe(100)
      expect(PERFORMANCE_CONFIG.OPTIMIZATION.DEBOUNCE_DELAY).toBe(300)
    })
  })

  describe('Currency Categories', () => {
    it('should categorize major currencies correctly', () => {
      expect(getCurrencyCategory('BTC/USDT')).toBe('MAJOR')
      expect(getCurrencyCategory('ETH/USDT')).toBe('MAJOR')
    })

    it('should categorize popular currencies correctly', () => {
      expect(getCurrencyCategory('DOGE/USDT')).toBe('POPULAR')
      expect(getCurrencyCategory('MATIC/USDT')).toBe('POPULAR')
    })

    it('should categorize unknown currencies as altcoins', () => {
      expect(getCurrencyCategory('UNKNOWN/USDT')).toBe('ALTCOINS')
    })

    it('should return correct minimum requirements', () => {
      const majorReq = getMinRequirements('BTC/USDT')
      expect(majorReq.minVolume).toBe(10000)
      expect(majorReq.minSpread).toBe(0.03)

      const altcoinReq = getMinRequirements('UNKNOWN/USDT')
      expect(altcoinReq.minVolume).toBe(1000)
      expect(altcoinReq.minSpread).toBe(0.1)
    })

    it('should determine if symbol should be processed', () => {
      expect(shouldProcessSymbol('BTC/USDT', 50000)).toBe(true)
      expect(shouldProcessSymbol('BTC/USDT', 5000)).toBe(false)
      expect(shouldProcessSymbol('UNKNOWN/USDT', 2000)).toBe(true)
    })
  })

  describe('Utility Functions', () => {
    it('should generate correct exchange URLs', () => {
      const urls = getExchangeUrls('gateio', 'BTC/USDT')
      expect(urls.spot).toContain('gate.io')
      expect(urls.futures).toContain('gate.io')
      expect(urls.spot).toContain('BTC_USDT')
    })

    it('should return correct exchange fees', () => {
      expect(getExchangeFee('gateio')).toBe(0.1)
      expect(getExchangeFee('mexc')).toBe(0.1)
      expect(getExchangeFee('bitget')).toBe(0.1)
    })

    it('should return correct rate limits', () => {
      const rateLimit = getRateLimit('gateio')
      expect(rateLimit.requestsPerMinute).toBe(100)
      expect(rateLimit.requestsPerSecond).toBe(10)
    })
  })
})