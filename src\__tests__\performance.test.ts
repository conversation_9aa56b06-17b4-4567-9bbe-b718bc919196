// performance.test.ts - Testes para utilitários de performance

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { 
  measureExecutionTime,
  debounce,
  throttle,
  memoize,
  createSimpleCache as createCache,
  formatDuration
} from '../utils/performance'

describe('performance utilities', () => {
  beforeEach(() => {
    vi.clearAllTimers()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('measureExecutionTime', () => {
    it('should measure execution time of sync function', () => {
      const syncFn = () => {
        // Simulate some work
        let sum = 0
        for (let i = 0; i < 1000; i++) {
          sum += i
        }
        return sum
      }

      const result = measureExecutionTime(syncFn)
      expect(result.result).toBe(499500)
      expect(result.executionTime).toBeGreaterThan(0)
    })

    it('should measure execution time of async function', async () => {
      const asyncFn = async () => {
        return 'done'
      }

      const result = await measureExecutionTime(asyncFn)
      expect(result.result).toBe('done')
      expect(result.executionTime).toBeGreaterThanOrEqual(0)
    })
  })

  describe('debounce', () => {
    it('should debounce function calls', () => {
      const fn = vi.fn()
      const debouncedFn = debounce(fn, 100)

      debouncedFn()
      debouncedFn()
      debouncedFn()

      expect(fn).not.toHaveBeenCalled()

      vi.advanceTimersByTime(100)
      expect(fn).toHaveBeenCalledTimes(1)
    })

    it('should pass arguments correctly', () => {
      const fn = vi.fn()
      const debouncedFn = debounce(fn, 100)

      debouncedFn('arg1', 'arg2')
      vi.advanceTimersByTime(100)

      expect(fn).toHaveBeenCalledWith('arg1', 'arg2')
    })

    it('should reset timer on subsequent calls', () => {
      const fn = vi.fn()
      const debouncedFn = debounce(fn, 100)

      debouncedFn()
      vi.advanceTimersByTime(50)
      debouncedFn()
      vi.advanceTimersByTime(50)

      expect(fn).not.toHaveBeenCalled()

      vi.advanceTimersByTime(50)
      expect(fn).toHaveBeenCalledTimes(1)
    })
  })

  describe('throttle', () => {
    it('should throttle function calls', () => {
      const fn = vi.fn()
      const throttledFn = throttle(fn, 100)

      throttledFn()
      throttledFn()
      throttledFn()

      expect(fn).toHaveBeenCalledTimes(1)

      vi.advanceTimersByTime(100)
      throttledFn()

      expect(fn).toHaveBeenCalledTimes(2)
    })

    it('should pass arguments correctly', () => {
      const fn = vi.fn()
      const throttledFn = throttle(fn, 100)

      throttledFn('arg1', 'arg2')
      expect(fn).toHaveBeenCalledWith('arg1', 'arg2')
    })
  })

  describe('memoize', () => {
    it('should cache function results', () => {
      const expensiveFn = vi.fn((x: number) => x * 2)
      const memoizedFn = memoize(expensiveFn)

      const result1 = memoizedFn(5)
      const result2 = memoizedFn(5)

      expect(result1).toBe(10)
      expect(result2).toBe(10)
      expect(expensiveFn).toHaveBeenCalledTimes(1)
    })

    it('should handle different arguments', () => {
      const expensiveFn = vi.fn((x: number) => x * 2)
      const memoizedFn = memoize(expensiveFn)

      memoizedFn(5)
      memoizedFn(10)
      memoizedFn(5)

      expect(expensiveFn).toHaveBeenCalledTimes(2)
    })

    it('should handle complex arguments', () => {
      const expensiveFn = vi.fn((obj: { x: number, y: number }) => obj.x + obj.y)
      const memoizedFn = memoize(expensiveFn)

      const arg1 = { x: 1, y: 2 }
      const arg2 = { x: 1, y: 2 }

      memoizedFn(arg1)
      memoizedFn(arg2)

      expect(expensiveFn).toHaveBeenCalledTimes(1) // Same content, should be cached
    })
  })

  describe('createCache', () => {
    it('should create a cache with get and set methods', () => {
      const cache = createCache<string, number>(100)

      cache.set('key1', 42)
      expect(cache.get('key1')).toBe(42)
      expect(cache.has('key1')).toBe(true)
      expect(cache.has('key2')).toBe(false)
    })

    it('should respect max size', () => {
      const cache = createCache<string, number>(2)

      cache.set('key1', 1)
      cache.set('key2', 2)
      cache.set('key3', 3)

      expect(cache.size()).toBe(2)
      expect(cache.has('key1')).toBe(false) // Should be evicted
      expect(cache.has('key2')).toBe(true)
      expect(cache.has('key3')).toBe(true)
    })

    it('should clear cache', () => {
      const cache = createCache<string, number>(10)

      cache.set('key1', 1)
      cache.set('key2', 2)
      cache.clear()

      expect(cache.size()).toBe(0)
      expect(cache.has('key1')).toBe(false)
    })
  })

  describe('formatDuration', () => {
    it('should format milliseconds correctly', () => {
      expect(formatDuration(500)).toBe('500ms')
      expect(formatDuration(1500)).toBe('1.5s')
      expect(formatDuration(65000)).toBe('1m 5s')
      expect(formatDuration(3665000)).toBe('1h 1m 5s')
    })

    it('should handle zero duration', () => {
      expect(formatDuration(0)).toBe('0ms')
    })

    it('should handle large durations', () => {
      expect(formatDuration(90061000)).toBe('1d 1h 1m 1s')
    })
  })
})