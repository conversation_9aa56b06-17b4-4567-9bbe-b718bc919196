// Teste básico para verificar se a configuração está funcionando

import { describe, it, expect } from 'vitest'
import { formatCurrency, formatPercentage } from '@/utils/formatters'
import { calculateSpread, calculateProfitability } from '@/utils/calculations'
import { isValidSpread, validateArbitrageOpportunity } from '@/utils/validation'
import { EXCHANGE_CONFIG } from '@/config/arbitrage'

describe('Project Setup', () => {
  it('should have correct exchange configuration', () => {
    expect(EXCHANGE_CONFIG.gateio.name).toBe('Gate.io')
    expect(EXCHANGE_CONFIG.mexc.name).toBe('MEXC')
    expect(EXCHANGE_CONFIG.bitget.name).toBe('Bitget')
  })

  it('should format currency correctly', () => {
    expect(formatCurrency(1234.56)).toBe('$1,234.56')
    expect(formatCurrency(0.123456)).toBe('$0.123456')
  })

  it('should format percentage correctly', () => {
    expect(formatPercentage(1.234)).toBe('1.23%')
    expect(formatPercentage(0.05, 3)).toBe('0.050%')
  })

  it('should calculate spread correctly', () => {
    const spread = calculateSpread(100, 101)
    expect(spread).toBe(1) // 1%
  })

  it('should classify profitability correctly', () => {
    expect(calculateProfitability(1.5)).toBe('high')
    expect(calculateProfitability(0.7)).toBe('medium')
    expect(calculateProfitability(0.1)).toBe('low')
  })

  it('should validate spread correctly', () => {
    expect(isValidSpread(0.1)).toBe(true)
    expect(isValidSpread(0.01)).toBe(false)
  })

  it('should validate arbitrage opportunity', () => {
    const opportunity = {
      spotExchange: 'gateio',
      futuresExchange: 'mexc',
      spreadPercentage: 0.5,
      volume: 5000,
      dataAge: 10000,
      spotPrice: 100,
      futuresPrice: 100.5,
      symbol: 'BTC/USDT'
    }

    const validation = validateArbitrageOpportunity(opportunity)
    expect(validation.isValid).toBe(true)
    expect(validation.errors).toHaveLength(0)
  })

  it('should reject invalid arbitrage opportunity', () => {
    const opportunity = {
      spotExchange: 'gateio',
      futuresExchange: 'gateio', // Same exchange
      spreadPercentage: 0.01, // Too low
      volume: 500, // Too low
      dataAge: 20000, // Too old
      spotPrice: 0, // Invalid
      futuresPrice: 100,
      symbol: 'INVALID'
    }

    const validation = validateArbitrageOpportunity(opportunity)
    expect(validation.isValid).toBe(false)
    expect(validation.errors.length).toBeGreaterThan(0)
  })
})