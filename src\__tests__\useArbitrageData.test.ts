// useArbitrageData.test.ts - Testes básicos para hook useArbitrageData

import { describe, it, expect } from 'vitest'
import { useArbitrageData } from '../hooks/useArbitrageData'

// Mock básico para React Query
vi.mock('@tanstack/react-query', () => ({
  useQuery: () => ({
    data: null,
    isLoading: false,
    isError: false,
    error: null,
    isRefetching: false,
    refetch: vi.fn(),
    dataUpdatedAt: Date.now()
  })
}))

describe('useArbitrageData', () => {
  it('should be a function', () => {
    expect(typeof useArbitrageData).toBe('function')
  })

  it('should return hook result structure', () => {
    // Este teste é básico devido às limitações de testar hooks sem setup completo
    expect(useArbitrageData).toBeDefined()
  })
})