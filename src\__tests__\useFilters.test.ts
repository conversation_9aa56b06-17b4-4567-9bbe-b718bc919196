// useFilters.test.ts - Testes completos para hook useFilters

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useFilters } from '../hooks/useFilters'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
Object.defineProperty(window, 'localStorage', { value: localStorageMock })

describe('useFilters', () => {
  const mockOpportunities = [
    {
      id: '1',
      symbol: 'BTC/USDT',
      spotExchange: 'mexc' as const,
      futuresExchange: 'gateio' as const,
      profitability: 'high' as const,
      spreadPercentage: 1.5,
      spotVolume: 100000,
      futuresVolume: 80000,
      spotPrice: 45000,
      futuresPrice: 45150,
      dataAge: 5000,
      isValid: true,
      type: 'spot-futures-cross' as const
    },
    {
      id: '2',
      symbol: 'ETH/USDT',
      spotExchange: 'gateio' as const,
      futuresExchange: 'bitget' as const,
      profitability: 'medium' as const,
      spreadPercentage: 0.8,
      spotVolume: 50000,
      futuresVolume: 60000,
      spotPrice: 2500,
      futuresPrice: 2520,
      dataAge: 10000,
      isValid: true,
      type: 'spot-futures-cross' as const
    }
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  it('should initialize with default filters', () => {
    const { result } = renderHook(() => useFilters(mockOpportunities))
    
    expect(result.current.filters.searchTerm).toBe('')
    expect(result.current.filters.spotExchange).toBe('all')
    expect(result.current.filters.futuresExchange).toBe('all')
    expect(result.current.filters.profitability).toBe('all')
  })

  it('should filter opportunities by search term', () => {
    const { result } = renderHook(() => useFilters(mockOpportunities))
    
    act(() => {
      result.current.updateFilter('searchTerm', 'BTC')
    })
    
    expect(result.current.filters.searchTerm).toBe('BTC')
  })

  it('should filter opportunities by exchange', () => {
    const { result } = renderHook(() => useFilters(mockOpportunities))
    
    act(() => {
      result.current.updateFilter('spotExchange', 'mexc')
    })
    
    expect(result.current.filters.spotExchange).toBe('mexc')
  })

  it('should filter opportunities by profitability', () => {
    const { result } = renderHook(() => useFilters(mockOpportunities))
    
    act(() => {
      result.current.updateFilter('profitability', 'high')
    })
    
    expect(result.current.filters.profitability).toBe('high')
  })

  it('should count active filters', () => {
    const { result } = renderHook(() => useFilters(mockOpportunities))
    
    act(() => {
      result.current.updateFilter('searchTerm', 'BTC')
      result.current.updateFilter('spotExchange', 'mexc')
    })
    
    expect(result.current.activeFiltersCount).toBeGreaterThan(0)
  })

  it('should reset filters', () => {
    const { result } = renderHook(() => useFilters(mockOpportunities))
    
    act(() => {
      result.current.updateFilter('searchTerm', 'BTC')
      result.current.resetFilters()
    })
    
    expect(result.current.filters.searchTerm).toBe('')
  })

  it('should extract unique exchanges', () => {
    const { result } = renderHook(() => useFilters(mockOpportunities))
    
    expect(result.current.exchanges.spot).toContain('mexc')
    expect(result.current.exchanges.spot).toContain('gateio')
    expect(result.current.exchanges.futures).toContain('gateio')
    expect(result.current.exchanges.futures).toContain('bitget')
  })

  it('should filter opportunities correctly', () => {
    const { result } = renderHook(() => useFilters(mockOpportunities))
    
    // Should return all opportunities initially
    expect(result.current.filteredOpportunities).toHaveLength(2)
    
    act(() => {
      result.current.updateFilter('profitability', 'high')
    })
    
    // Should filter to only high profitability
    expect(result.current.filteredOpportunities).toHaveLength(1)
    expect(result.current.filteredOpportunities[0].profitability).toBe('high')
  })
})