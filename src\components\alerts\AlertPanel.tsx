import React, { useState } from 'react'
import { Bell, AlertTriangle } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { Badge } from '../ui/Badge'

const AlertPanel = () => {
  const [alerts] = useState([
    {
      id: '1',
      type: 'spread_threshold',
      priority: 'high',
      message: 'BTC/USDT spread exceeded 2.5% threshold',
      timestamp: Date.now() - 300000,
      resolved: false
    }
  ])

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="w-5 h-5" />
          Real-Time Alerts
        </CardTitle>
      </CardHeader>
      <CardContent>
        {alerts.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Bell className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>No alerts found</p>
          </div>
        ) : (
          <div className="space-y-3">
            {alerts.map((alert) => (
              <div key={alert.id} className="p-4 border rounded-lg">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="w-4 h-4 mt-1" />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge className="bg-orange-500 text-white">
                        {alert.priority.toUpperCase()}
                      </Badge>
                    </div>
                    <p className="text-sm font-medium">{alert.message}</p>
                    <div className="text-xs text-gray-500 mt-1">
                      {new Date(alert.timestamp).toLocaleString()}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default AlertPanel