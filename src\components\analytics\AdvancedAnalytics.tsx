// AdvancedAnalytics - Análises avançadas de mercado e performance

import React, { useState, useMemo } from 'react'
import { TrendingUp, TrendingDown, BarChart3, PieChart, Activity, Target } from 'lucide-react'
import { Card } from '@/components/ui/Card'
import { <PERSON><PERSON> } from '@/components/ui/Button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs'
import { Badge } from '@/components/ui/Badge'
import type { ArbitrageOpportunity } from '@/types/arbitrage'

interface AdvancedAnalyticsProps {
  opportunities: ArbitrageOpportunity[]
  className?: string
}

interface MarketAnalysis {
  volatility: number
  trend: 'bullish' | 'bearish' | 'neutral'
  correlation: number
  momentum: number
  riskScore: number
}

interface PerformanceMetrics {
  totalVolume: number
  averageSpread: number
  profitabilityDistribution: Record<string, number>
  exchangePerformance: Record<string, number>
  topSymbols: Array<{ symbol: string; count: number; avgSpread: number }>
}

export function AdvancedAnalytics({ opportunities, className = '' }: AdvancedAnalyticsProps) {
  const [selectedTimeframe, setSelectedTimeframe] = useState<'1h' | '4h' | '1d' | '7d'>('4h')

  // Análise de mercado
  const marketAnalysis = useMemo((): MarketAnalysis => {
    if (opportunities.length === 0) {
      return {
        volatility: 0,
        trend: 'neutral',
        correlation: 0,
        momentum: 0,
        riskScore: 0
      }
    }

    const spreads = opportunities.map(opp => Math.abs(opp.spreadPercentage))
    const avgSpread = spreads.reduce((sum, spread) => sum + spread, 0) / spreads.length
    
    // Calcular volatilidade baseada na variação dos spreads
    const variance = spreads.reduce((sum, spread) => sum + Math.pow(spread - avgSpread, 2), 0) / spreads.length
    const volatility = Math.sqrt(variance)

    // Determinar tendência baseada na quantidade de oportunidades (só spreads positivos)
    const totalOpportunities = opportunities.length
    const trendRatio = totalOpportunities > 0 ? 1 : 0 // Sempre positivo agora
    
    let trend: 'bullish' | 'bearish' | 'neutral' = 'neutral'
    if (totalOpportunities > 10) trend = 'bullish'
    else if (totalOpportunities < 3) trend = 'bearish'

    // Calcular correlação entre exchanges (simplificado)
    const correlation = Math.min(0.95, 0.7 + (avgSpread * 0.1))

    // Momentum baseado na quantidade de oportunidades de alta rentabilidade
    const highProfitCount = opportunities.filter(opp => opp.profitability === 'HIGH').length
    const momentum = Math.min(100, (highProfitCount / opportunities.length) * 100)

    // Risk score baseado na volatilidade e distribuição
    const riskScore = Math.min(100, volatility * 20 + (avgSpread * 10))

    return {
      volatility: Math.round(volatility * 100) / 100,
      trend,
      correlation: Math.round(correlation * 100) / 100,
      momentum: Math.round(momentum),
      riskScore: Math.round(riskScore)
    }
  }, [opportunities])

  // Métricas de performance
  const performanceMetrics = useMemo((): PerformanceMetrics => {
    if (opportunities.length === 0) {
      return {
        totalVolume: 0,
        averageSpread: 0,
        profitabilityDistribution: {},
        exchangePerformance: {},
        topSymbols: []
      }
    }

    const totalVolume = opportunities.reduce((sum, opp) => 
      sum + Math.min(opp.spotVolume, opp.futuresVolume), 0
    )

    const averageSpread = opportunities.reduce((sum, opp) => 
      sum + opp.spreadPercentage, 0
    ) / opportunities.length

    // Distribuição de rentabilidade
    const profitabilityDistribution = opportunities.reduce((acc, opp) => {
      acc[opp.profitability] = (acc[opp.profitability] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // Performance por exchange
    const exchangePerformance = opportunities.reduce((acc, opp) => {
      const key = `${opp.spotExchange}-${opp.futuresExchange}`
      if (!acc[key]) acc[key] = []
      acc[key].push(opp.spreadPercentage)
      return acc
    }, {} as Record<string, number[]>)

    const exchangeAvgs = Object.entries(exchangePerformance).reduce((acc, [key, spreads]) => {
      acc[key] = spreads.reduce((sum, spread) => sum + spread, 0) / spreads.length
      return acc
    }, {} as Record<string, number>)

    // Top símbolos
    const symbolStats = opportunities.reduce((acc, opp) => {
      if (!acc[opp.symbol]) {
        acc[opp.symbol] = { count: 0, totalSpread: 0 }
      }
      acc[opp.symbol].count++
      acc[opp.symbol].totalSpread += opp.spreadPercentage
      return acc
    }, {} as Record<string, { count: number; totalSpread: number }>)

    const topSymbols = Object.entries(symbolStats)
      .map(([symbol, stats]) => ({
        symbol,
        count: stats.count,
        avgSpread: stats.totalSpread / stats.count
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)

    return {
      totalVolume,
      averageSpread,
      profitabilityDistribution,
      exchangePerformance: exchangeAvgs,
      topSymbols
    }
  }, [opportunities])

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'bullish': return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'bearish': return <TrendingDown className="h-4 w-4 text-red-500" />
      default: return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'bullish': return 'text-green-600'
      case 'bearish': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getRiskColor = (score: number) => {
    if (score < 30) return 'text-green-600'
    if (score < 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Análises Avançadas</h2>
          <p className="text-gray-600">Insights detalhados de mercado e performance</p>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500">Timeframe:</span>
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value as any)}
            className="px-3 py-1 border rounded-md text-sm"
          >
            <option value="1h">1 Hora</option>
            <option value="4h">4 Horas</option>
            <option value="1d">1 Dia</option>
            <option value="7d">7 Dias</option>
          </select>
        </div>
      </div>

      <Tabs defaultValue="market" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="market">Análise de Mercado</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="risk">Gestão de Risco</TabsTrigger>
        </TabsList>

        <TabsContent value="market" className="space-y-4">
          {/* Market Analysis Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-500">Tendência</span>
                {getTrendIcon(marketAnalysis.trend)}
              </div>
              <div className={`text-2xl font-bold ${getTrendColor(marketAnalysis.trend)}`}>
                {marketAnalysis.trend.toUpperCase()}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Baseado em {opportunities.length} oportunidades
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-500">Volatilidade</span>
                <BarChart3 className="h-4 w-4 text-blue-500" />
              </div>
              <div className="text-2xl font-bold text-blue-600">
                {marketAnalysis.volatility}%
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Variação dos spreads
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-500">Correlação</span>
                <PieChart className="h-4 w-4 text-purple-500" />
              </div>
              <div className="text-2xl font-bold text-purple-600">
                {(marketAnalysis.correlation * 100).toFixed(0)}%
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Entre exchanges
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-500">Momentum</span>
                <Target className="h-4 w-4 text-orange-500" />
              </div>
              <div className="text-2xl font-bold text-orange-600">
                {marketAnalysis.momentum}%
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Oportunidades high-profit
              </div>
            </Card>
          </div>

          {/* Top Symbols */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Top Símbolos por Atividade</h3>
            <div className="space-y-3">
              {performanceMetrics.topSymbols.map((symbol, index) => (
                <div key={symbol.symbol} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="w-8 h-8 rounded-full flex items-center justify-center">
                      {index + 1}
                    </Badge>
                    <span className="font-medium">{symbol.symbol}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{symbol.count} oportunidades</div>
                    <div className="text-xs text-gray-500">
                      Spread médio: {symbol.avgSpread.toFixed(3)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          {/* Performance Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="p-4">
              <div className="text-sm text-gray-500 mb-1">Volume Total</div>
              <div className="text-2xl font-bold text-blue-600">
                ${(performanceMetrics.totalVolume / 1000000).toFixed(1)}M
              </div>
            </Card>

            <Card className="p-4">
              <div className="text-sm text-gray-500 mb-1">Spread Médio</div>
              <div className="text-2xl font-bold text-green-600">
                {performanceMetrics.averageSpread.toFixed(3)}%
              </div>
            </Card>

            <Card className="p-4">
              <div className="text-sm text-gray-500 mb-1">Oportunidades</div>
              <div className="text-2xl font-bold text-purple-600">
                {opportunities.length}
              </div>
            </Card>
          </div>

          {/* Profitability Distribution */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Distribuição de Rentabilidade</h3>
            <div className="space-y-3">
              {Object.entries(performanceMetrics.profitabilityDistribution).map(([level, count]) => (
                <div key={level} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Badge 
                      variant={level === 'high' ? 'default' : level === 'medium' ? 'secondary' : 'outline'}
                      className={
                        level === 'high' ? 'bg-green-100 text-green-800' :
                        level === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-blue-100 text-blue-800'
                      }
                    >
                      {level.toUpperCase()}
                    </Badge>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{count} oportunidades</div>
                    <div className="text-xs text-gray-500">
                      {((count / opportunities.length) * 100).toFixed(1)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="risk" className="space-y-4">
          {/* Risk Analysis */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Análise de Risco</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="text-sm text-gray-500 mb-2">Score de Risco Geral</div>
                <div className={`text-3xl font-bold ${getRiskColor(marketAnalysis.riskScore)}`}>
                  {marketAnalysis.riskScore}/100
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {marketAnalysis.riskScore < 30 ? 'Baixo Risco' :
                   marketAnalysis.riskScore < 60 ? 'Risco Moderado' : 'Alto Risco'}
                </div>
              </div>

              <div>
                <div className="text-sm text-gray-500 mb-2">Recomendação</div>
                <div className="text-sm">
                  {marketAnalysis.riskScore < 30 ? 
                    '✅ Condições favoráveis para arbitragem' :
                   marketAnalysis.riskScore < 60 ? 
                    '⚠️ Monitorar volatilidade de perto' :
                    '🚨 Considerar reduzir exposição'}
                </div>
              </div>
            </div>

            <div className="mt-6 space-y-3">
              <h4 className="font-medium">Fatores de Risco:</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Volatilidade atual: {marketAnalysis.volatility}%</li>
                <li>• Correlação entre exchanges: {(marketAnalysis.correlation * 100).toFixed(0)}%</li>
                <li>• Distribuição de oportunidades: {Object.keys(performanceMetrics.profitabilityDistribution).length} níveis</li>
                <li>• Volume médio por oportunidade: ${(performanceMetrics.totalVolume / opportunities.length / 1000).toFixed(0)}K</li>
              </ul>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default AdvancedAnalytics