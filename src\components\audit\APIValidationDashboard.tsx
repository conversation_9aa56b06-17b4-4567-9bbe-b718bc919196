// APIValidationDashboard - Dashboard Visual para Validação de APIs das Exchanges

import React, { useState, useEffect } from 'react'
import { Card } from '../ui/Card'
import { Button } from '../ui/Button'
import { Badge } from '../ui/Badge'
import { 
  Wifi, 
  WifiOff, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Clock,
  Database,
  Zap,
  Shield,
  Activity,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Eye,
  EyeOff
} from 'lucide-react'

interface ExchangeValidation {
  name: string
  status: 'online' | 'offline' | 'degraded'
  responseTime: number
  lastCheck: Date
  endpoints: {
    spot: EndpointValidation
    futures: EndpointValidation
    auth: EndpointValidation
  }
  dataQuality: DataQualityValidation
}

interface EndpointValidation {
  url: string
  status: 'healthy' | 'warning' | 'error'
  responseTime: number
  lastResponse: any
  errorCount: number
}

interface DataValidation {
  available: boolean
  pairCount: number
  lastUpdate: Date
  freshness: number // em segundos
  completeness: number // percentual
}

interface DataQualityValidation {
  totalPairs: number
  validPairs: number
  dataCompleteness: number
  priceAccuracy: number
  timestampConsistency: number
  duplicateCount: number
  errorRate: number
  score: number
}

const APIValidationDashboard: React.FC = () => {
  const [validations, setValidations] = useState<ExchangeValidation[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [expandedExchange, setExpandedExchange] = useState<string | null>(null)

  // Mock data para demonstração
  const mockValidations: ExchangeValidation[] = [
    {
      name: 'Gate.io',
      status: 'online',
      responseTime: 245,
      lastCheck: new Date(),
      endpoints: {
        spot: {
          url: '/api/v4/spot/currency_pairs',
          status: 'healthy',
          responseTime: 180,
          lastResponse: { pairs: 2670 },
          errorCount: 0
        },
        futures: {
          url: '/api/v4/futures/contracts',
          status: 'healthy',
          responseTime: 220,
          lastResponse: { contracts: 602 },
          errorCount: 0
        },
        auth: {
          url: '/api/v4/spot/accounts',
          status: 'healthy',
          responseTime: 310,
          lastResponse: { authenticated: true },
          errorCount: 0
        }
      },
      dataQuality: {
        totalPairs: 3272,
        validPairs: 3268,
        dataCompleteness: 99.8,
        priceAccuracy: 99.9,
        timestampConsistency: 99.7,
        duplicateCount: 2,
        errorRate: 0.1,
        score: 98.5
      }
    },
    {
      name: 'MEXC',
      status: 'online',
      responseTime: 189,
      lastCheck: new Date(),
      endpoints: {
        spot: {
          url: '/api/v3/exchangeInfo',
          status: 'healthy',
          responseTime: 165,
          lastResponse: { symbols: 2429 },
          errorCount: 0
        },
        futures: {
          url: '/api/v1/exchangeInfo',
          status: 'warning',
          responseTime: 450,
          lastResponse: { symbols: 787 },
          errorCount: 2
        },
        auth: {
          url: '/api/v3/account',
          status: 'healthy',
          responseTime: 195,
          lastResponse: { authenticated: true },
          errorCount: 0
        }
      },
      dataQuality: {
        totalPairs: 3216,
        validPairs: 3210,
        dataCompleteness: 99.6,
        priceAccuracy: 99.8,
        timestampConsistency: 98.9,
        duplicateCount: 4,
        errorRate: 0.2,
        score: 97.8
      }
    },
    {
      name: 'Bitget',
      status: 'degraded',
      responseTime: 890,
      lastCheck: new Date(),
      endpoints: {
        spot: {
          url: '/api/spot/v1/public/products',
          status: 'warning',
          responseTime: 780,
          lastResponse: { data: 799 },
          errorCount: 1
        },
        futures: {
          url: '/api/mix/v1/market/contracts',
          status: 'healthy',
          responseTime: 320,
          lastResponse: { data: 513 },
          errorCount: 0
        },
        auth: {
          url: '/api/spot/v1/account/assets',
          status: 'error',
          responseTime: 1200,
          lastResponse: { error: 'timeout' },
          errorCount: 5
        }
      },
      dataQuality: {
        totalPairs: 1312,
        validPairs: 1305,
        dataCompleteness: 98.2,
        priceAccuracy: 99.1,
        timestampConsistency: 97.8,
        duplicateCount: 3,
        errorRate: 0.5,
        score: 95.2
      }
    }
  ]

  const runValidation = async () => {
    setIsLoading(true)
    
    try {
      // Simular validação das APIs
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      setValidations(mockValidations)
      setLastUpdate(new Date())
      
    } catch (error) {
      console.error('Error running validation:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    runValidation()
    
    if (autoRefresh) {
      const interval = setInterval(runValidation, 30000) // 30 segundos
      return () => clearInterval(interval)
    }
  }, [autoRefresh])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
      case 'healthy': return 'text-green-600'
      case 'degraded':
      case 'warning': return 'text-yellow-600'
      case 'offline':
      case 'error': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
      case 'healthy': return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'degraded':
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-600" />
      case 'offline':
      case 'error': return <XCircle className="w-5 h-5 text-red-600" />
      default: return <Activity className="w-5 h-5 text-gray-600" />
    }
  }

  const getResponseTimeColor = (time: number) => {
    if (time < 300) return 'text-green-600'
    if (time < 600) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreColor = (score: number) => {
    if (score >= 95) return 'text-green-600'
    if (score >= 85) return 'text-yellow-600'
    return 'text-red-600'
  }

  const toggleExpanded = (exchangeName: string) => {
    setExpandedExchange(expandedExchange === exchangeName ? null : exchangeName)
  }

  if (isLoading && validations.length === 0) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-48 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const overallStatus = validations.every(v => v.status === 'online') ? 'online' : 
                      validations.some(v => v.status === 'offline') ? 'critical' : 'warning'
  
  const avgResponseTime = validations.reduce((acc, v) => acc + v.responseTime, 0) / validations.length
  const totalPairs = validations.reduce((acc, v) => acc + v.dataQuality.totalPairs, 0)
  const avgScore = validations.reduce((acc, v) => acc + v.dataQuality.score, 0) / validations.length

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
            <Shield className="w-6 h-6" />
            Validação de APIs
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Monitoramento em tempo real das APIs das exchanges
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            {autoRefresh ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
            Auto-refresh
          </Button>
          {lastUpdate && (
            <span className="text-sm text-gray-500">
              {lastUpdate.toLocaleTimeString()}
            </span>
          )}
          <Button onClick={runValidation} disabled={isLoading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Validar
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Status Geral</p>
              <p className={`text-lg font-semibold ${getStatusColor(overallStatus)}`}>
                {overallStatus.toUpperCase()}
              </p>
            </div>
            {getStatusIcon(overallStatus)}
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Tempo Médio</p>
              <p className={`text-lg font-semibold ${getResponseTimeColor(avgResponseTime)}`}>
                {Math.round(avgResponseTime)}ms
              </p>
            </div>
            <Clock className={`w-8 h-8 ${getResponseTimeColor(avgResponseTime)}`} />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total de Pares</p>
              <p className="text-lg font-semibold text-blue-600">
                {totalPairs.toLocaleString()}
              </p>
            </div>
            <Database className="w-8 h-8 text-blue-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Score Médio</p>
              <p className={`text-lg font-semibold ${getScoreColor(avgScore)}`}>
                {avgScore.toFixed(1)}%
              </p>
            </div>
            <Zap className={`w-8 h-8 ${getScoreColor(avgScore)}`} />
          </div>
        </Card>
      </div>

      {/* Exchange Details */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {validations.map((validation, index) => (
          <Card key={index} className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <h3 className="text-lg font-semibold">{validation.name}</h3>
                {getStatusIcon(validation.status)}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => toggleExpanded(validation.name)}
              >
                {expandedExchange === validation.name ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </Button>
            </div>

            {/* Status Summary */}
            <div className="space-y-3 mb-4">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Status:</span>
                <Badge variant={validation.status === 'online' ? 'success' : validation.status === 'degraded' ? 'warning' : 'destructive'}>
                  {validation.status}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Tempo de Resposta:</span>
                <span className={`text-sm font-medium ${getResponseTimeColor(validation.responseTime)}`}>
                  {validation.responseTime}ms
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Score de Qualidade:</span>
                <span className={`text-sm font-medium ${getScoreColor(validation.dataQuality.score)}`}>
                  {validation.dataQuality.score}%
                </span>
              </div>
            </div>

            {/* Expanded Details */}
            {expandedExchange === validation.name && (
              <div className="space-y-4 border-t pt-4">
                {/* Endpoints */}
                <div>
                  <h4 className="font-medium mb-2">Endpoints</h4>
                  <div className="space-y-2">
                    {Object.entries(validation.endpoints).map(([type, endpoint]) => (
                      <div key={type} className="flex items-center justify-between text-sm">
                        <span className="capitalize">{type}:</span>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(endpoint.status)}
                          <span className={getResponseTimeColor(endpoint.responseTime)}>
                            {endpoint.responseTime}ms
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Data Quality */}
                <div>
                  <h4 className="font-medium mb-2">Qualidade dos Dados</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Pares Válidos:</span>
                      <span>{validation.dataQuality.validPairs}/{validation.dataQuality.totalPairs}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Completude:</span>
                      <span>{validation.dataQuality.dataCompleteness}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Precisão:</span>
                      <span>{validation.dataQuality.priceAccuracy}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Taxa de Erro:</span>
                      <span className={validation.dataQuality.errorRate > 1 ? 'text-red-600' : 'text-green-600'}>
                        {validation.dataQuality.errorRate}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </Card>
        ))}
      </div>
    </div>
  )
}

export default APIValidationDashboard