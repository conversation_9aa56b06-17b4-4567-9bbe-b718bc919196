// CriticalFunctionDashboard - Dashboard para Validação de Funcionalidades Críticas

import React, { useState, useEffect } from 'react'
import { Card } from '../ui/Card'
import { Button } from '../ui/Button'
import { Badge } from '../ui/Badge'
import { 
  Zap, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Play,
  Square,
  Activity,
  Clock,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Eye,
  EyeOff,
  Target,
  Shield,
  Database,
  Wifi
} from 'lucide-react'

interface CriticalFunction {
  id: string
  name: string
  description: string
  category: 'data' | 'calculation' | 'api' | 'auth' | 'alert'
  status: 'passed' | 'failed' | 'warning' | 'running'
  lastRun: Date
  duration: number
  errorCount: number
  successRate: number
  details: TestDetail[]
}

interface TestDetail {
  step: string
  status: 'passed' | 'failed' | 'warning'
  duration: number
  message?: string
  data?: any
}

const CriticalFunctionDashboard: React.FC = () => {
  const [functions, setFunctions] = useState<CriticalFunction[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [selectedFunction, setSelectedFunction] = useState<string | null>(null)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)

  // Mock data para demonstração
  const mockFunctions: CriticalFunction[] = [
    {
      id: 'data-collection',
      name: 'Coleta de Dados',
      description: 'Validação da coleta de dados das 3 exchanges',
      category: 'data',
      status: 'passed',
      lastRun: new Date(),
      duration: 2340,
      errorCount: 0,
      successRate: 100,
      details: [
        { step: 'Conectar Gate.io', status: 'passed', duration: 450 },
        { step: 'Conectar MEXC', status: 'passed', duration: 380 },
        { step: 'Conectar Bitget', status: 'passed', duration: 520 },
        { step: 'Validar dados spot', status: 'passed', duration: 680 },
        { step: 'Validar dados futuros', status: 'passed', duration: 310 }
      ]
    },
    {
      id: 'spread-calculation',
      name: 'Cálculo de Spread',
      description: 'Validação dos cálculos de arbitragem cross-exchange',
      category: 'calculation',
      status: 'passed',
      lastRun: new Date(),
      duration: 1890,
      errorCount: 0,
      successRate: 99.8,
      details: [
        { step: 'Calcular spreads spot vs futuros', status: 'passed', duration: 560 },
        { step: 'Classificar rentabilidade', status: 'passed', duration: 340 },
        { step: 'Gerar estratégias', status: 'passed', duration: 420 },
        { step: 'Validar precisão', status: 'warning', duration: 570, message: 'Pequenas variações detectadas' }
      ]
    },
    {
      id: 'api-authentication',
      name: 'Autenticação HMAC',
      description: 'Validação da autenticação HMAC nas 3 exchanges',
      category: 'auth',
      status: 'warning',
      lastRun: new Date(),
      duration: 3200,
      errorCount: 1,
      successRate: 95.2,
      details: [
        { step: 'Auth Gate.io (SHA512)', status: 'passed', duration: 890 },
        { step: 'Auth MEXC (SHA256)', status: 'passed', duration: 760 },
        { step: 'Auth Bitget (SHA256+Base64)', status: 'warning', duration: 1550, message: 'Timeout ocasional' }
      ]
    },
    {
      id: 'alert-system',
      name: 'Sistema de Alertas',
      description: 'Validação do sistema de notificações e alertas',
      category: 'alert',
      status: 'passed',
      lastRun: new Date(),
      duration: 1200,
      errorCount: 0,
      successRate: 100,
      details: [
        { step: 'Detectar oportunidades', status: 'passed', duration: 340 },
        { step: 'Filtrar por critérios', status: 'passed', duration: 280 },
        { step: 'Enviar notificações', status: 'passed', duration: 580 }
      ]
    },
    {
      id: 'cross-exchange-arbitrage',
      name: 'Arbitragem Cross-Exchange',
      description: 'Validação completa do fluxo de arbitragem entre exchanges',
      category: 'calculation',
      status: 'passed',
      lastRun: new Date(),
      duration: 4500,
      errorCount: 0,
      successRate: 98.9,
      details: [
        { step: 'Identificar pares comuns', status: 'passed', duration: 890 },
        { step: 'Calcular spreads cross-exchange', status: 'passed', duration: 1200 },
        { step: 'Validar oportunidades', status: 'passed', duration: 980 },
        { step: 'Gerar estratégias de execução', status: 'passed', duration: 1430 }
      ]
    }
  ]

  const runTests = async (functionId?: string) => {
    setIsRunning(true)
    
    try {
      // Simular execução dos testes
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      setFunctions(mockFunctions)
      setLastUpdate(new Date())
      
    } catch (error) {
      console.error('Error running tests:', error)
    } finally {
      setIsRunning(false)
    }
  }

  useEffect(() => {
    runTests()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'passed': return 'text-green-600'
      case 'warning': return 'text-yellow-600'
      case 'failed': return 'text-red-600'
      case 'running': return 'text-blue-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'passed': return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-600" />
      case 'failed': return <XCircle className="w-5 h-5 text-red-600" />
      case 'running': return <Activity className="w-5 h-5 text-blue-600 animate-spin" />
      default: return <Activity className="w-5 h-5 text-gray-600" />
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'data': return <Database className="w-5 h-5" />
      case 'calculation': return <Target className="w-5 h-5" />
      case 'api': return <Wifi className="w-5 h-5" />
      case 'auth': return <Shield className="w-5 h-5" />
      case 'alert': return <Zap className="w-5 h-5" />
      default: return <Activity className="w-5 h-5" />
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'data': return 'bg-blue-100 text-blue-800'
      case 'calculation': return 'bg-green-100 text-green-800'
      case 'api': return 'bg-purple-100 text-purple-800'
      case 'auth': return 'bg-red-100 text-red-800'
      case 'alert': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const overallStatus = functions.every(f => f.status === 'passed') ? 'passed' : 
                      functions.some(f => f.status === 'failed') ? 'failed' : 'warning'
  
  const avgSuccessRate = functions.reduce((acc, f) => acc + f.successRate, 0) / functions.length
  const totalErrors = functions.reduce((acc, f) => acc + f.errorCount, 0)
  const avgDuration = functions.reduce((acc, f) => acc + f.duration, 0) / functions.length

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
            <Target className="w-6 h-6" />
            Funções Críticas
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Validação das funcionalidades essenciais do sistema
          </p>
        </div>
        <div className="flex items-center gap-3">
          {lastUpdate && (
            <span className="text-sm text-gray-500">
              {lastUpdate.toLocaleTimeString()}
            </span>
          )}
          <Button onClick={() => runTests()} disabled={isRunning}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isRunning ? 'animate-spin' : ''}`} />
            {isRunning ? 'Executando...' : 'Executar Testes'}
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Status Geral</p>
              <p className={`text-lg font-semibold ${getStatusColor(overallStatus)}`}>
                {overallStatus.toUpperCase()}
              </p>
            </div>
            {getStatusIcon(overallStatus)}
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Taxa de Sucesso</p>
              <p className={`text-lg font-semibold ${avgSuccessRate >= 95 ? 'text-green-600' : 'text-yellow-600'}`}>
                {avgSuccessRate.toFixed(1)}%
              </p>
            </div>
            <TrendingUp className={`w-8 h-8 ${avgSuccessRate >= 95 ? 'text-green-600' : 'text-yellow-600'}`} />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total de Erros</p>
              <p className={`text-lg font-semibold ${totalErrors === 0 ? 'text-green-600' : 'text-red-600'}`}>
                {totalErrors}
              </p>
            </div>
            <XCircle className={`w-8 h-8 ${totalErrors === 0 ? 'text-green-600' : 'text-red-600'}`} />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Tempo Médio</p>
              <p className="text-lg font-semibold text-blue-600">
                {(avgDuration / 1000).toFixed(1)}s
              </p>
            </div>
            <Clock className="w-8 h-8 text-blue-600" />
          </div>
        </Card>
      </div>

      {/* Functions List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {functions.map((func) => (
          <Card key={func.id} className="p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-start gap-3">
                {getCategoryIcon(func.category)}
                <div>
                  <h3 className="text-lg font-semibold">{func.name}</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {func.description}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge className={getCategoryColor(func.category)}>
                  {func.category}
                </Badge>
                {getStatusIcon(func.status)}
              </div>
            </div>

            {/* Function Stats */}
            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="text-center">
                <p className="text-sm text-gray-600 dark:text-gray-400">Sucesso</p>
                <p className={`text-lg font-semibold ${func.successRate >= 95 ? 'text-green-600' : 'text-yellow-600'}`}>
                  {func.successRate}%
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600 dark:text-gray-400">Duração</p>
                <p className="text-lg font-semibold text-blue-600">
                  {(func.duration / 1000).toFixed(1)}s
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600 dark:text-gray-400">Erros</p>
                <p className={`text-lg font-semibold ${func.errorCount === 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {func.errorCount}
                </p>
              </div>
            </div>

            {/* Test Details */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Detalhes dos Testes</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedFunction(selectedFunction === func.id ? null : func.id)}
                >
                  {selectedFunction === func.id ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </Button>
              </div>

              {selectedFunction === func.id && (
                <div className="space-y-2 border-t pt-3">
                  {func.details.map((detail, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(detail.status)}
                        <span className="text-sm">{detail.step}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <Clock className="w-3 h-3" />
                        {detail.duration}ms
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => runTests(func.id)}
                disabled={isRunning}
              >
                <Play className="w-4 h-4 mr-1" />
                Executar
              </Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
}

export default CriticalFunctionDashboard