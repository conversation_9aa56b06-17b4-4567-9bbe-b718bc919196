// ProjectStructureDashboard - Dashboard Visual para Análise de Estrutura do Projeto

import React, { useState, useEffect } from 'react'
import { Card } from '../ui/Card'
import { Button } from '../ui/Button'
import { Badge } from '../ui/Badge'
import { 
  FolderOpen, 
  FileText, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Package,
  Code,
  TestTube,
  Settings,
  TrendingUp,
  TrendingDown,
  Minus,
  RefreshCw,
  Download,
  Eye,
  EyeOff
} from 'lucide-react'

interface ProjectStructureAnalysis {
  timestamp: number
  overallScore: number
  status: 'excellent' | 'good' | 'needs_improvement' | 'critical'
  structure: StructureAnalysis
  dependencies: DependencyAnalysis
  codeQuality: CodeQualityAnalysis
  organization: OrganizationAnalysis
  recommendations: string[]
}

interface StructureAnalysis {
  totalFiles: number
  totalDirectories: number
  filesByType: Record<string, number>
  directoryStructure: DirectoryNode[]
  missingDirectories: string[]
  unexpectedFiles: string[]
  structureScore: number
}

interface DirectoryNode {
  name: string
  path: string
  type: 'directory' | 'file'
  size?: number
  children?: DirectoryNode[]
  purpose?: string
}

interface DependencyAnalysis {
  totalDependencies: number
  productionDeps: number
  devDependencies: number
  outdatedDeps: string[]
  vulnerabilities: string[]
  unusedDeps: string[]
  dependencyScore: number
  packageJsonExists: boolean
  lockFileExists: boolean
}

interface CodeQualityAnalysis {
  totalLinesOfCode: number
  codeFiles: number
  testFiles: number
  testCoverage: number
  duplicatedCode: number
  complexityScore: number
  maintainabilityIndex: number
  technicalDebt: string[]
  codeQualityScore: number
}

interface OrganizationAnalysis {
  followsConventions: boolean
  hasProperNaming: boolean
  hasDocumentation: boolean
  hasTests: boolean
  hasTypeScript: boolean
  hasLinting: boolean
  hasFormatting: boolean
  organizationScore: number
  issues: string[]
}

const ProjectStructureDashboard: React.FC = () => {
  const [analysis, setAnalysis] = useState<ProjectStructureAnalysis | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['overview']))

  // Simular análise (em produção, chamaria o ProjectStructureAnalyzer)
  const runAnalysis = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // Simular delay de análise
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Dados simulados baseados no projeto atual
      const mockAnalysis: ProjectStructureAnalysis = {
        timestamp: Date.now(),
        overallScore: 87,
        status: 'good',
        structure: {
          totalFiles: 156,
          totalDirectories: 28,
          filesByType: {
            '.ts': 45,
            '.tsx': 38,
            '.js': 12,
            '.jsx': 8,
            '.json': 15,
            '.md': 18,
            '.css': 6,
            '.html': 3,
            '': 11 // arquivos sem extensão
          },
          directoryStructure: [
            {
              name: 'src',
              path: 'src',
              type: 'directory',
              purpose: 'Source code',
              children: [
                { name: 'components', path: 'src/components', type: 'directory', purpose: 'React components' },
                { name: 'hooks', path: 'src/hooks', type: 'directory', purpose: 'Custom React hooks' },
                { name: 'services', path: 'src/services', type: 'directory', purpose: 'Business logic and API calls' },
                { name: 'types', path: 'src/types', type: 'directory', purpose: 'TypeScript type definitions' },
                { name: 'utils', path: 'src/utils', type: 'directory', purpose: 'Utility functions' },
                { name: '__tests__', path: 'src/__tests__', type: 'directory', purpose: 'Test files' }
              ]
            },
            {
              name: 'public',
              path: 'public',
              type: 'directory',
              purpose: 'Static assets'
            },
            {
              name: '.kiro',
              path: '.kiro',
              type: 'directory',
              purpose: 'Kiro configuration'
            }
          ],
          missingDirectories: ['docs', 'scripts'],
          unexpectedFiles: ['TASK_18.1_COMPLETA.md', 'FASE_4_COMPLETA.md'],
          structureScore: 85
        },
        dependencies: {
          totalDependencies: 42,
          productionDeps: 28,
          devDependencies: 14,
          outdatedDeps: ['react-query@3.39.0'],
          vulnerabilities: [],
          unusedDeps: ['lodash'],
          dependencyScore: 90,
          packageJsonExists: true,
          lockFileExists: true
        },
        codeQuality: {
          totalLinesOfCode: 12450,
          codeFiles: 91,
          testFiles: 8,
          testCoverage: 72,
          duplicatedCode: 249,
          complexityScore: 78,
          maintainabilityIndex: 82,
          technicalDebt: ['Low test coverage in services', 'High complexity in DataCollector'],
          codeQualityScore: 77
        },
        organization: {
          followsConventions: true,
          hasProperNaming: true,
          hasDocumentation: true,
          hasTests: true,
          hasTypeScript: true,
          hasLinting: false,
          hasFormatting: false,
          organizationScore: 85,
          issues: ['Missing ESLint configuration', 'Missing Prettier configuration']
        },
        recommendations: [
          'Add ESLint and Prettier configuration for code quality',
          'Increase test coverage from 72% to at least 80%',
          'Create docs directory for project documentation',
          'Remove unused dependency: lodash',
          'Update outdated dependency: react-query',
          'Organize or remove task completion files from root',
          'Add scripts directory for build and deployment scripts'
        ]
      }
      
      setAnalysis(mockAnalysis)
    } catch (err) {
      setError('Failed to analyze project structure')
    } finally {
      setLoading(false)
    }
  }

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(section)) {
      newExpanded.delete(section)
    } else {
      newExpanded.add(section)
    }
    setExpandedSections(newExpanded)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600'
      case 'good': return 'text-blue-600'
      case 'needs_improvement': return 'text-yellow-600'
      case 'critical': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 75) return 'text-blue-600'
    if (score >= 50) return 'text-yellow-600'
    return 'text-red-600'
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const exportReport = () => {
    if (!analysis) return
    
    const report = {
      timestamp: new Date(analysis.timestamp).toISOString(),
      overallScore: analysis.overallScore,
      status: analysis.status,
      summary: {
        totalFiles: analysis.structure.totalFiles,
        totalDirectories: analysis.structure.totalDirectories,
        totalLinesOfCode: analysis.codeQuality.totalLinesOfCode,
        testCoverage: analysis.codeQuality.testCoverage,
        totalDependencies: analysis.dependencies.totalDependencies
      },
      recommendations: analysis.recommendations
    }
    
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `project-structure-analysis-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  useEffect(() => {
    runAnalysis()
  }, [])

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Project Structure Analysis</h2>
          <div className="flex items-center space-x-2">
            <RefreshCw className="w-4 h-4 animate-spin" />
            <span className="text-sm text-gray-600">Analyzing project structure...</span>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map(i => (
            <Card key={i} className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Project Structure Analysis</h2>
          <Button onClick={runAnalysis} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry Analysis
          </Button>
        </div>
        
        <Card className="p-6">
          <div className="flex items-center space-x-3 text-red-600">
            <XCircle className="w-6 h-6" />
            <div>
              <h3 className="font-semibold">Analysis Failed</h3>
              <p className="text-sm text-gray-600">{error}</p>
            </div>
          </div>
        </Card>
      </div>
    )
  }

  if (!analysis) return null

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Project Structure Analysis</h2>
          <p className="text-sm text-gray-600">
            Last analyzed: {new Date(analysis.timestamp).toLocaleString()}
          </p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={exportReport} variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
          <Button onClick={runAnalysis} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Re-analyze
          </Button>
        </div>
      </div>

      {/* Overall Score */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold mb-2">Overall Project Health</h3>
            <div className="flex items-center space-x-4">
              <div className={`text-3xl font-bold ${getScoreColor(analysis.overallScore)}`}>
                {analysis.overallScore}%
              </div>
              <Badge 
                variant={analysis.status === 'excellent' ? 'success' : 
                        analysis.status === 'good' ? 'info' :
                        analysis.status === 'needs_improvement' ? 'warning' : 'error'}
              >
                {analysis.status.replace('_', ' ').toUpperCase()}
              </Badge>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-600 mb-1">Score Breakdown</div>
            <div className="space-y-1 text-xs">
              <div>Structure: {analysis.structure.structureScore}%</div>
              <div>Dependencies: {analysis.dependencies.dependencyScore}%</div>
              <div>Code Quality: {analysis.codeQuality.codeQualityScore}%</div>
              <div>Organization: {analysis.organization.organizationScore}%</div>
            </div>
          </div>
        </div>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <FileText className="w-8 h-8 text-blue-600" />
            <div>
              <div className="text-2xl font-bold">{analysis.structure.totalFiles}</div>
              <div className="text-sm text-gray-600">Total Files</div>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <FolderOpen className="w-8 h-8 text-green-600" />
            <div>
              <div className="text-2xl font-bold">{analysis.structure.totalDirectories}</div>
              <div className="text-sm text-gray-600">Directories</div>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <Code className="w-8 h-8 text-purple-600" />
            <div>
              <div className="text-2xl font-bold">{analysis.codeQuality.totalLinesOfCode.toLocaleString()}</div>
              <div className="text-sm text-gray-600">Lines of Code</div>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <TestTube className="w-8 h-8 text-orange-600" />
            <div>
              <div className="text-2xl font-bold">{analysis.codeQuality.testCoverage}%</div>
              <div className="text-sm text-gray-600">Test Coverage</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Detailed Sections */}
      <div className="space-y-4">
        {/* Structure Analysis */}
        <Card className="overflow-hidden">
          <div 
            className="p-4 cursor-pointer hover:bg-gray-50 flex items-center justify-between"
            onClick={() => toggleSection('structure')}
          >
            <div className="flex items-center space-x-3">
              <FolderOpen className="w-5 h-5 text-blue-600" />
              <h3 className="font-semibold">Project Structure</h3>
              <Badge variant="info">{analysis.structure.structureScore}%</Badge>
            </div>
            {expandedSections.has('structure') ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          </div>
          
          {expandedSections.has('structure') && (
            <div className="p-4 border-t bg-gray-50">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-3">File Types Distribution</h4>
                  <div className="space-y-2">
                    {Object.entries(analysis.structure.filesByType).map(([ext, count]) => (
                      <div key={ext} className="flex justify-between items-center">
                        <span className="text-sm">{ext || 'No extension'}</span>
                        <Badge variant="outline">{count}</Badge>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-3">Issues Found</h4>
                  <div className="space-y-2">
                    {analysis.structure.missingDirectories.length > 0 && (
                      <div className="flex items-start space-x-2">
                        <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5" />
                        <div>
                          <div className="text-sm font-medium">Missing Directories</div>
                          <div className="text-xs text-gray-600">
                            {analysis.structure.missingDirectories.join(', ')}
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {analysis.structure.unexpectedFiles.length > 0 && (
                      <div className="flex items-start space-x-2">
                        <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5" />
                        <div>
                          <div className="text-sm font-medium">Unexpected Root Files</div>
                          <div className="text-xs text-gray-600">
                            {analysis.structure.unexpectedFiles.join(', ')}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </Card>

        {/* Dependencies Analysis */}
        <Card className="overflow-hidden">
          <div 
            className="p-4 cursor-pointer hover:bg-gray-50 flex items-center justify-between"
            onClick={() => toggleSection('dependencies')}
          >
            <div className="flex items-center space-x-3">
              <Package className="w-5 h-5 text-green-600" />
              <h3 className="font-semibold">Dependencies</h3>
              <Badge variant="success">{analysis.dependencies.dependencyScore}%</Badge>
            </div>
            {expandedSections.has('dependencies') ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          </div>
          
          {expandedSections.has('dependencies') && (
            <div className="p-4 border-t bg-gray-50">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-medium mb-3">Overview</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Total Dependencies</span>
                      <span className="font-medium">{analysis.dependencies.totalDependencies}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Production</span>
                      <span className="font-medium">{analysis.dependencies.productionDeps}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Development</span>
                      <span className="font-medium">{analysis.dependencies.devDependencies}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-3">Health Checks</h4>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      {analysis.dependencies.packageJsonExists ? 
                        <CheckCircle className="w-4 h-4 text-green-600" /> : 
                        <XCircle className="w-4 h-4 text-red-600" />
                      }
                      <span className="text-sm">package.json exists</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      {analysis.dependencies.lockFileExists ? 
                        <CheckCircle className="w-4 h-4 text-green-600" /> : 
                        <XCircle className="w-4 h-4 text-red-600" />
                      }
                      <span className="text-sm">Lock file exists</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-3">Issues</h4>
                  <div className="space-y-2">
                    {analysis.dependencies.outdatedDeps.length > 0 && (
                      <div className="text-sm">
                        <span className="text-yellow-600">Outdated:</span> {analysis.dependencies.outdatedDeps.length}
                      </div>
                    )}
                    {analysis.dependencies.vulnerabilities.length > 0 && (
                      <div className="text-sm">
                        <span className="text-red-600">Vulnerabilities:</span> {analysis.dependencies.vulnerabilities.length}
                      </div>
                    )}
                    {analysis.dependencies.unusedDeps.length > 0 && (
                      <div className="text-sm">
                        <span className="text-gray-600">Unused:</span> {analysis.dependencies.unusedDeps.length}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </Card>

        {/* Code Quality Analysis */}
        <Card className="overflow-hidden">
          <div 
            className="p-4 cursor-pointer hover:bg-gray-50 flex items-center justify-between"
            onClick={() => toggleSection('quality')}
          >
            <div className="flex items-center space-x-3">
              <Code className="w-5 h-5 text-purple-600" />
              <h3 className="font-semibold">Code Quality</h3>
              <Badge variant="warning">{analysis.codeQuality.codeQualityScore}%</Badge>
            </div>
            {expandedSections.has('quality') ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          </div>
          
          {expandedSections.has('quality') && (
            <div className="p-4 border-t bg-gray-50">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div>
                  <h4 className="font-medium mb-3">Metrics</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Code Files</span>
                      <span className="font-medium">{analysis.codeQuality.codeFiles}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Test Files</span>
                      <span className="font-medium">{analysis.codeQuality.testFiles}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Lines of Code</span>
                      <span className="font-medium">{analysis.codeQuality.totalLinesOfCode.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-3">Quality Scores</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Test Coverage</span>
                      <span className={`font-medium ${getScoreColor(analysis.codeQuality.testCoverage)}`}>
                        {analysis.codeQuality.testCoverage}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Complexity</span>
                      <span className={`font-medium ${getScoreColor(analysis.codeQuality.complexityScore)}`}>
                        {analysis.codeQuality.complexityScore}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Maintainability</span>
                      <span className={`font-medium ${getScoreColor(analysis.codeQuality.maintainabilityIndex)}`}>
                        {analysis.codeQuality.maintainabilityIndex}%
                      </span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-3">Issues</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Duplicated Code</span>
                      <span className="font-medium text-yellow-600">{analysis.codeQuality.duplicatedCode} lines</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Technical Debt</span>
                      <span className="font-medium text-red-600">{analysis.codeQuality.technicalDebt.length} items</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-3">Technical Debt</h4>
                  <div className="space-y-1">
                    {analysis.codeQuality.technicalDebt.map((debt, index) => (
                      <div key={index} className="text-xs text-gray-600 flex items-start space-x-1">
                        <AlertTriangle className="w-3 h-3 text-yellow-600 mt-0.5 flex-shrink-0" />
                        <span>{debt}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </Card>

        {/* Organization Analysis */}
        <Card className="overflow-hidden">
          <div 
            className="p-4 cursor-pointer hover:bg-gray-50 flex items-center justify-between"
            onClick={() => toggleSection('organization')}
          >
            <div className="flex items-center space-x-3">
              <Settings className="w-5 h-5 text-gray-600" />
              <h3 className="font-semibold">Project Organization</h3>
              <Badge variant="info">{analysis.organization.organizationScore}%</Badge>
            </div>
            {expandedSections.has('organization') ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          </div>
          
          {expandedSections.has('organization') && (
            <div className="p-4 border-t bg-gray-50">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-3">Configuration Checks</h4>
                  <div className="space-y-2">
                    {[
                      { key: 'followsConventions', label: 'Follows Conventions' },
                      { key: 'hasProperNaming', label: 'Proper Naming' },
                      { key: 'hasDocumentation', label: 'Documentation' },
                      { key: 'hasTests', label: 'Tests' },
                      { key: 'hasTypeScript', label: 'TypeScript' },
                      { key: 'hasLinting', label: 'Linting' },
                      { key: 'hasFormatting', label: 'Formatting' }
                    ].map(({ key, label }) => (
                      <div key={key} className="flex items-center space-x-2">
                        {analysis.organization[key as keyof OrganizationAnalysis] ? 
                          <CheckCircle className="w-4 h-4 text-green-600" /> : 
                          <XCircle className="w-4 h-4 text-red-600" />
                        }
                        <span className="text-sm">{label}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-3">Issues to Address</h4>
                  <div className="space-y-2">
                    {analysis.organization.issues.map((issue, index) => (
                      <div key={index} className="flex items-start space-x-2">
                        <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5" />
                        <span className="text-sm text-gray-600">{issue}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </Card>

        {/* Recommendations */}
        <Card className="overflow-hidden">
          <div 
            className="p-4 cursor-pointer hover:bg-gray-50 flex items-center justify-between"
            onClick={() => toggleSection('recommendations')}
          >
            <div className="flex items-center space-x-3">
              <TrendingUp className="w-5 h-5 text-blue-600" />
              <h3 className="font-semibold">Recommendations</h3>
              <Badge variant="outline">{analysis.recommendations.length}</Badge>
            </div>
            {expandedSections.has('recommendations') ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          </div>
          
          {expandedSections.has('recommendations') && (
            <div className="p-4 border-t bg-gray-50">
              <div className="space-y-3">
                {analysis.recommendations.map((recommendation, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 bg-white rounded-lg border">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-xs font-medium text-blue-600">{index + 1}</span>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-700">{recommendation}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </Card>
      </div>
    </div>
  )
}

export default ProjectStructureDashboard