// SpecAuditDashboard - Dashboard de Auditoria das Specs

import { useState, useEffect } from 'react'
import { FileText, CheckCircle, AlertTriangle, XCircle, TrendingUp, Award, Target, Zap, FolderOpen, Activity, Shield } from 'lucide-react'
import { Card } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'
import { Tabs } from '@/components/ui/Tabs'
import ProjectStructureDashboard from './ProjectStructureDashboard'
import APIValidationDashboard from './APIValidationDashboard'
import IntegrationValidationDashboard from './IntegrationValidationDashboard'
import CriticalFunctionDashboard from './CriticalFunctionDashboard'
import type { SpecAuditResult } from '@/services/audit/SpecAuditor'

interface SpecAuditDashboardProps {
  className?: string
}

export function SpecAuditDashboard({ className = '' }: SpecAuditDashboardProps) {
  const [auditResults, setAuditResults] = useState<SpecAuditResult[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)

  // Simular dados de auditoria (em produção, viria do SpecAuditor)
  const mockAuditResults: SpecAuditResult[] = [
    {
      specName: 'arbitragem-spotfutures',
      timestamp: Date.now(),
      overallScore: 92,
      status: 'excellent',
      files: {
        requirements: {
          exists: true,
          size: 15420,
          lastModified: new Date(),
          score: 95,
          issues: []
        },
        architecture: {
          exists: true,
          size: 8750,
          lastModified: new Date(),
          score: 90,
          issues: ['Minor documentation gaps']
        },
        implementation: {
          exists: true,
          size: 12300,
          lastModified: new Date(),
          score: 88,
          issues: ['Some edge cases not covered']
        }
      },
      metrics: {
        completeness: 92,
        consistency: 94,
        clarity: 90,
        implementability: 89
      },
      recommendations: [
        'Add more detailed error handling scenarios',
        'Include performance benchmarks',
        'Expand testing coverage documentation'
      ]
    }
  ]

  const loadAuditData = async () => {
    setIsLoading(true)
    
    try {
      // Em produção, aqui chamaria o SpecAuditor
      // const auditor = new SpecAuditor()
      // const results = await auditor.auditSpec('arbitragem-spotfutures')
      
      // Por enquanto, usar dados mock
      setAuditResults(mockAuditResults)
      setLastUpdate(new Date())
      
    } catch (error) {
      console.error('Error loading audit data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    loadAuditData()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600'
      case 'good': return 'text-blue-600'
      case 'warning': return 'text-yellow-600'
      case 'critical': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent': return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'good': return <CheckCircle className="w-5 h-5 text-blue-600" />
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-600" />
      case 'critical': return <XCircle className="w-5 h-5 text-red-600" />
      default: return <Activity className="w-5 h-5 text-gray-600" />
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 80) return 'text-blue-600'
    if (score >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (isLoading) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`p-6 space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
            <Shield className="w-6 h-6" />
            Auditoria de Especificações
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Validação completa das specs do sistema de arbitragem
          </p>
        </div>
        <div className="flex items-center gap-3">
          {lastUpdate && (
            <span className="text-sm text-gray-500">
              Última atualização: {lastUpdate.toLocaleTimeString()}
            </span>
          )}
          <Button onClick={loadAuditData} disabled={isLoading}>
            <Activity className="w-4 h-4 mr-2" />
            Atualizar
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      {auditResults.map((result, index) => (
        <div key={index} className="space-y-6">
          {/* Status Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Score Geral</p>
                  <p className={`text-2xl font-bold ${getScoreColor(result.overallScore)}`}>
                    {result.overallScore}%
                  </p>
                </div>
                <Award className={`w-8 h-8 ${getScoreColor(result.overallScore)}`} />
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Status</p>
                  <p className={`text-lg font-semibold ${getStatusColor(result.status)}`}>
                    {result.status.toUpperCase()}
                  </p>
                </div>
                {getStatusIcon(result.status)}
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Completude</p>
                  <p className={`text-lg font-semibold ${getScoreColor(result.metrics.completeness)}`}>
                    {result.metrics.completeness}%
                  </p>
                </div>
                <Target className={`w-8 h-8 ${getScoreColor(result.metrics.completeness)}`} />
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Implementabilidade</p>
                  <p className={`text-lg font-semibold ${getScoreColor(result.metrics.implementability)}`}>
                    {result.metrics.implementability}%
                  </p>
                </div>
                <Zap className={`w-8 h-8 ${getScoreColor(result.metrics.implementability)}`} />
              </div>
            </Card>
          </div>

          {/* Files Analysis */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Análise de Arquivos
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {Object.entries(result.files).map(([fileName, fileData]) => (
                <div key={fileName} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium capitalize">{fileName}</h4>
                    <Badge variant={fileData.exists ? 'success' : 'destructive'}>
                      {fileData.exists ? 'Existe' : 'Ausente'}
                    </Badge>
                  </div>
                  {fileData.exists && (
                    <>
                      <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                        <span>Score: {fileData.score}%</span>
                        <span>{(fileData.size / 1024).toFixed(1)}KB</span>
                      </div>
                      {fileData.issues.length > 0 && (
                        <div className="space-y-1">
                          <p className="text-xs font-medium text-yellow-600">Issues:</p>
                          {fileData.issues.map((issue, i) => (
                            <p key={i} className="text-xs text-gray-600 dark:text-gray-400">
                              • {issue}
                            </p>
                          ))}
                        </div>
                      )}
                    </>
                  )}
                </div>
              ))}
            </div>
          </Card>

          {/* Metrics Details */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Métricas Detalhadas
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Object.entries(result.metrics).map(([metric, value]) => (
                <div key={metric} className="text-center">
                  <p className="text-sm text-gray-600 dark:text-gray-400 capitalize mb-1">
                    {metric}
                  </p>
                  <div className="relative w-16 h-16 mx-auto">
                    <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                      <path
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeDasharray={`${value}, 100`}
                        className={getScoreColor(value)}
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className={`text-sm font-semibold ${getScoreColor(value)}`}>
                        {value}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Recommendations */}
          {result.recommendations.length > 0 && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <FolderOpen className="w-5 h-5" />
                Recomendações
              </h3>
              <div className="space-y-2">
                {result.recommendations.map((rec, i) => (
                  <div key={i} className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-gray-700 dark:text-gray-300">{rec}</p>
                  </div>
                ))}
              </div>
            </Card>
          )}
        </div>
      ))}

      {/* Detailed Dashboards */}
      <Tabs defaultValue="structure" className="w-full">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            <button className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
              Estrutura do Projeto
            </button>
            <button className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
              Validação de APIs
            </button>
            <button className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
              Integração
            </button>
            <button className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
              Funções Críticas
            </button>
          </nav>
        </div>
        
        <div className="mt-6">
          <div>
            <ProjectStructureDashboard />
          </div>
        </div>
      </Tabs>
    </div>
  )
}

export default SpecAuditDashboard