// AuthStatus - Componente para Mostrar Status da Autenticação das Exchanges

import { useState, useEffect } from 'react'
import { Shield, ShieldCheck, ShieldX, Key, AlertTriangle, CheckCircle, Info } from 'lucide-react'
import { Card } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'
import { AuthFactory } from '@/services/auth/HMACAuth'
import { EXCHANGE_ENDPOINTS, getTotalPairs } from '@/config/exchangeEndpoints'

interface AuthStatusProps {
  className?: string
  showDetails?: boolean
}

interface ExchangeAuthStatus {
  exchange: string
  name: string
  configured: boolean
  authMethod: string
  pairs: {
    spot: number
    futures: number
    total: number
  }
  error?: string
}

export function AuthStatus({ className = '', showDetails = true }: AuthStatusProps) {
  const [authStatuses, setAuthStatuses] = useState<ExchangeAuthStatus[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [lastCheck, setLastCheck] = useState<Date | null>(null)

  // Verificar status da autenticação
  const checkAuthStatus = async () => {
    setIsLoading(true)
    
    try {
      const credentials = AuthFactory.checkCredentials()
      const statuses: ExchangeAuthStatus[] = []
      
      // Gate.io
      try {
        const gateioAuth = credentials.gateio ? AuthFactory.createGateioAuth() : null
        statuses.push({
          exchange: 'gateio',
          name: 'Gate.io',
          configured: credentials.gateio,
          authMethod: 'HMAC SHA512',
          pairs: EXCHANGE_ENDPOINTS.gateio.pairs,
          error: !credentials.gateio ? 'API credentials not configured' : undefined
        })
      } catch (error) {
        statuses.push({
          exchange: 'gateio',
          name: 'Gate.io',
          configured: false,
          authMethod: 'HMAC SHA512',
          pairs: EXCHANGE_ENDPOINTS.gateio.pairs,
          error: error instanceof Error ? error.message : 'Authentication error'
        })
      }
      
      // MEXC
      try {
        const mexcAuth = credentials.mexc ? AuthFactory.createMexcAuth() : null
        statuses.push({
          exchange: 'mexc',
          name: 'MEXC',
          configured: credentials.mexc,
          authMethod: 'HMAC SHA256',
          pairs: EXCHANGE_ENDPOINTS.mexc.pairs,
          error: !credentials.mexc ? 'API credentials not configured' : undefined
        })
      } catch (error) {
        statuses.push({
          exchange: 'mexc',
          name: 'MEXC',
          configured: false,
          authMethod: 'HMAC SHA256',
          pairs: EXCHANGE_ENDPOINTS.mexc.pairs,
          error: error instanceof Error ? error.message : 'Authentication error'
        })
      }
      
      // Bitget
      try {
        const bitgetAuth = credentials.bitget ? AuthFactory.createBitgetAuth() : null
        statuses.push({
          exchange: 'bitget',
          name: 'Bitget',
          configured: credentials.bitget,
          authMethod: 'HMAC SHA256 + Base64',
          pairs: EXCHANGE_ENDPOINTS.bitget.pairs,
          error: !credentials.bitget ? 'API credentials not configured' : undefined
        })
      } catch (error) {
        statuses.push({
          exchange: 'bitget',
          name: 'Bitget',
          configured: false,
          authMethod: 'HMAC SHA256 + Base64',
          pairs: EXCHANGE_ENDPOINTS.bitget.pairs,
          error: error instanceof Error ? error.message : 'Authentication error'
        })
      }
      
      setAuthStatuses(statuses)
      setLastCheck(new Date())
      
    } catch (error) {
      console.error('Error checking auth status:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Verificar status na montagem
  useEffect(() => {
    checkAuthStatus()
  }, [])

  // Calcular estatísticas
  const stats = {
    total: authStatuses.length,
    configured: authStatuses.filter(s => s.configured).length,
    totalPairs: authStatuses.reduce((sum, s) => sum + s.pairs.total, 0),
    configuredPairs: authStatuses.filter(s => s.configured).reduce((sum, s) => sum + s.pairs.total, 0)
  }

  // Ícone de status
  const getStatusIcon = (status: ExchangeAuthStatus) => {
    if (status.configured) {
      return <ShieldCheck className="h-5 w-5 text-green-600" />
    } else {
      return <ShieldX className="h-5 w-5 text-red-600" />
    }
  }

  // Badge de status
  const getStatusBadge = (status: ExchangeAuthStatus) => {
    if (status.configured) {
      return <Badge variant="success">Configurado</Badge>
    } else {
      return <Badge variant="destructive">Não Configurado</Badge>
    }
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header com estatísticas */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold">Status da Autenticação HMAC</h3>
          </div>
          <Button
            onClick={checkAuthStatus}
            size="sm"
            variant="outline"
            disabled={isLoading}
          >
            {isLoading ? 'Verificando...' : 'Atualizar'}
          </Button>
        </div>

        {/* Estatísticas gerais */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{stats.configured}/{stats.total}</div>
            <div className="text-sm text-gray-500">Exchanges</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.configuredPairs.toLocaleString()}</div>
            <div className="text-sm text-gray-500">Pares Disponíveis</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {stats.total > 0 ? Math.round((stats.configured / stats.total) * 100) : 0}%
            </div>
            <div className="text-sm text-gray-500">Taxa de Sucesso</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{stats.totalPairs.toLocaleString()}</div>
            <div className="text-sm text-gray-500">Total de Pares</div>
          </div>
        </div>

        {lastCheck && (
          <div className="text-xs text-gray-500 text-center">
            Última verificação: {lastCheck.toLocaleString('pt-BR')}
          </div>
        )}
      </Card>

      {/* Status individual das exchanges */}
      {showDetails && (
        <div className="grid gap-4">
          {authStatuses.map((status) => (
            <Card key={status.exchange} className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  {getStatusIcon(status)}
                  <div>
                    <h4 className="font-medium text-gray-900">{status.name}</h4>
                    <p className="text-sm text-gray-500">{status.authMethod}</p>
                  </div>
                </div>
                {getStatusBadge(status)}
              </div>

              {/* Detalhes dos pares */}
              <div className="grid grid-cols-3 gap-4 mb-3">
                <div className="text-center">
                  <div className="text-lg font-semibold text-blue-600">{status.pairs.spot.toLocaleString()}</div>
                  <div className="text-xs text-gray-500">Spot</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-purple-600">{status.pairs.futures.toLocaleString()}</div>
                  <div className="text-xs text-gray-500">Futures</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-gray-900">{status.pairs.total.toLocaleString()}</div>
                  <div className="text-xs text-gray-500">Total</div>
                </div>
              </div>

              {/* Erro ou informações adicionais */}
              {status.error && (
                <div className="flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                  <AlertTriangle className="h-4 w-4" />
                  {status.error}
                </div>
              )}

              {status.configured && (
                <div className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-700">
                  <CheckCircle className="h-4 w-4" />
                  Autenticação HMAC configurada e funcionando
                </div>
              )}
            </Card>
          ))}
        </div>
      )}

      {/* Informações sobre configuração */}
      {stats.configured < stats.total && (
        <Card className="p-4 border-yellow-200 bg-yellow-50">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-yellow-800 mb-2">Configuração Necessária</h4>
              <p className="text-sm text-yellow-700 mb-3">
                Para usar as APIs reais das exchanges, configure as chaves de API no arquivo .env:
              </p>
              <div className="bg-yellow-100 p-3 rounded text-xs font-mono text-yellow-800">
                <div>VITE_GATEIO_API_KEY=your_key</div>
                <div>VITE_GATEIO_SECRET_KEY=your_secret</div>
                <div>VITE_MEXC_API_KEY=your_key</div>
                <div>VITE_MEXC_SECRET_KEY=your_secret</div>
                <div>VITE_BITGET_API_KEY=your_key</div>
                <div>VITE_BITGET_SECRET_KEY=your_secret</div>
                <div>VITE_BITGET_PASSPHRASE=your_passphrase</div>
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}

export default AuthStatus