// ChartAnalytics - Componente para Análise Avançada de Gráficos

import { useMemo } from 'react'
import { TrendingUp, TrendingDown, Activity, Zap, Target, AlertTriangle } from 'lucide-react'
import { Card } from '@/components/ui/Card'
import type { ChartDataPoint, ChartMetrics } from '@/hooks/useChartData'

interface ChartAnalyticsProps {
  data: ChartDataPoint[]
  metrics: ChartMetrics
  symbol: string
  spotExchange: string
  futuresExchange: string
  timeframe: string
}

interface TradingSignal {
  type: 'buy' | 'sell' | 'hold'
  strength: 'weak' | 'medium' | 'strong'
  reason: string
  confidence: number
}

interface PriceLevel {
  price: number
  type: 'support' | 'resistance'
  strength: number
  touches: number
}

export function ChartAnalytics({
  data,
  metrics,
  symbol,
  spotExchange,
  futuresExchange,
  timeframe
}: ChartAnalyticsProps) {

  // Análise de sinais de trading
  const tradingSignal = useMemo((): TradingSignal => {
    if (data.length < 10) {
      return {
        type: 'hold',
        strength: 'weak',
        reason: 'Dados insuficientes para análise',
        confidence: 0
      }
    }

    const recentData = data.slice(-10) // Últimos 10 pontos
    const avgSpread = recentData.reduce((sum, d) => sum + d.spreadPercentage, 0) / recentData.length
    const spreadTrend = recentData[recentData.length - 1].spreadPercentage - recentData[0].spreadPercentage

    // Lógica de sinal baseada em spread e tendência
    if (Math.abs(avgSpread) > 1.0 && spreadTrend > 0.1) {
      return {
        type: 'buy',
        strength: 'strong',
        reason: 'Spread alto e crescente - oportunidade de arbitragem',
        confidence: 85
      }
    } else if (Math.abs(avgSpread) > 0.5 && metrics.spreadVolatility < 0.3) {
      return {
        type: 'buy',
        strength: 'medium',
        reason: 'Spread moderado com baixa volatilidade',
        confidence: 65
      }
    } else if (Math.abs(avgSpread) < 0.2 && spreadTrend < -0.1) {
      return {
        type: 'sell',
        strength: 'medium',
        reason: 'Spread baixo e decrescente - fechar posições',
        confidence: 70
      }
    } else {
      return {
        type: 'hold',
        strength: 'weak',
        reason: 'Condições neutras - aguardar melhor oportunidade',
        confidence: 45
      }
    }
  }, [data, metrics])

  // Análise de níveis de suporte e resistência
  const priceLevels = useMemo((): PriceLevel[] => {
    if (data.length < 20) return []

    const levels: PriceLevel[] = []
    const spotPrices = data.map(d => d.spotPrice)
    const futuresPrices = data.map(d => d.futuresPrice)
    
    // Encontrar máximos e mínimos locais
    const findLocalExtremes = (prices: number[], type: 'max' | 'min') => {
      const extremes: { price: number; index: number }[] = []
      
      for (let i = 2; i < prices.length - 2; i++) {
        const current = prices[i]
        const prev2 = prices[i - 2]
        const prev1 = prices[i - 1]
        const next1 = prices[i + 1]
        const next2 = prices[i + 2]
        
        if (type === 'max') {
          if (current > prev2 && current > prev1 && current > next1 && current > next2) {
            extremes.push({ price: current, index: i })
          }
        } else {
          if (current < prev2 && current < prev1 && current < next1 && current < next2) {
            extremes.push({ price: current, index: i })
          }
        }
      }
      
      return extremes
    }

    // Analisar spot
    const spotMaxs = findLocalExtremes(spotPrices, 'max')
    const spotMins = findLocalExtremes(spotPrices, 'min')
    
    // Agrupar níveis próximos
    const groupLevels = (extremes: { price: number; index: number }[], type: 'support' | 'resistance') => {
      const grouped = new Map<number, number>()
      const tolerance = 0.01 // 1% de tolerância
      
      extremes.forEach(({ price }) => {
        let found = false
        for (const [level, count] of grouped.entries()) {
          if (Math.abs(price - level) / level < tolerance) {
            grouped.set(level, count + 1)
            found = true
            break
          }
        }
        if (!found) {
          grouped.set(price, 1)
        }
      })
      
      return Array.from(grouped.entries()).map(([price, touches]) => ({
        price,
        type,
        strength: Math.min(touches * 20, 100),
        touches
      }))
    }

    levels.push(...groupLevels(spotMaxs, 'resistance'))
    levels.push(...groupLevels(spotMins, 'support'))
    
    return levels.sort((a, b) => b.strength - a.strength).slice(0, 5)
  }, [data])

  // Análise de padrões
  const patterns = useMemo(() => {
    const patterns: { name: string; description: string; probability: number }[] = []
    
    if (metrics.inversions > 3) {
      patterns.push({
        name: 'Alta Volatilidade',
        description: 'Múltiplas inversões de spread detectadas',
        probability: Math.min(metrics.inversions * 15, 90)
      })
    }
    
    if (metrics.correlationScore > 0.8) {
      patterns.push({
        name: 'Alta Correlação',
        description: 'Preços spot e futuros altamente correlacionados',
        probability: metrics.correlationScore * 100
      })
    }
    
    if (metrics.spreadVolatility > 0.5) {
      patterns.push({
        name: 'Spread Volátil',
        description: 'Alta volatilidade no spread - oportunidades frequentes',
        probability: Math.min(metrics.spreadVolatility * 100, 95)
      })
    }
    
    const recentTrend = data.slice(-5)
    if (recentTrend.length >= 5) {
      const trendUp = recentTrend.every((d, i) => i === 0 || d.spreadPercentage > recentTrend[i - 1].spreadPercentage)
      const trendDown = recentTrend.every((d, i) => i === 0 || d.spreadPercentage < recentTrend[i - 1].spreadPercentage)
      
      if (trendUp) {
        patterns.push({
          name: 'Tendência de Alta',
          description: 'Spread em tendência crescente consistente',
          probability: 75
        })
      } else if (trendDown) {
        patterns.push({
          name: 'Tendência de Baixa',
          description: 'Spread em tendência decrescente consistente',
          probability: 75
        })
      }
    }
    
    return patterns.sort((a, b) => b.probability - a.probability)
  }, [data, metrics])

  // Cálculo de risco/retorno
  const riskReturn = useMemo(() => {
    const spreads = data.map(d => Math.abs(d.spreadPercentage))
    const avgReturn = spreads.reduce((sum, s) => sum + s, 0) / spreads.length
    const maxDrawdown = Math.max(...spreads) - Math.min(...spreads)
    const sharpeRatio = avgReturn / (metrics.spreadVolatility || 1)
    
    return {
      expectedReturn: avgReturn,
      maxDrawdown,
      sharpeRatio,
      riskLevel: maxDrawdown > 2 ? 'high' : maxDrawdown > 1 ? 'medium' : 'low'
    }
  }, [data, metrics])

  return (
    <div className="space-y-6">
      {/* Sinal de Trading */}
      <Card className="p-4">
        <div className="flex items-center gap-3 mb-4">
          <div className={`p-2 rounded-full ${
            tradingSignal.type === 'buy' ? 'bg-green-100' :
            tradingSignal.type === 'sell' ? 'bg-red-100' :
            'bg-gray-100'
          }`}>
            {tradingSignal.type === 'buy' ? (
              <TrendingUp className="h-5 w-5 text-green-600" />
            ) : tradingSignal.type === 'sell' ? (
              <TrendingDown className="h-5 w-5 text-red-600" />
            ) : (
              <Activity className="h-5 w-5 text-gray-600" />
            )}
          </div>
          <div>
            <h3 className="font-semibold">
              Sinal: {tradingSignal.type.toUpperCase()}
            </h3>
            <p className="text-sm text-gray-600">
              Força: {tradingSignal.strength} | Confiança: {tradingSignal.confidence}%
            </p>
          </div>
        </div>
        <p className="text-sm text-gray-700">{tradingSignal.reason}</p>
        
        <div className="mt-3">
          <div className="flex justify-between text-xs text-gray-500 mb-1">
            <span>Confiança</span>
            <span>{tradingSignal.confidence}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full ${
                tradingSignal.confidence > 70 ? 'bg-green-500' :
                tradingSignal.confidence > 50 ? 'bg-yellow-500' :
                'bg-red-500'
              }`}
              style={{ width: `${tradingSignal.confidence}%` }}
            />
          </div>
        </div>
      </Card>

      {/* Análise de Risco/Retorno */}
      <Card className="p-4">
        <h3 className="font-semibold mb-4 flex items-center gap-2">
          <Target className="h-4 w-4" />
          Análise de Risco/Retorno
        </h3>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-gray-500">Retorno Esperado</p>
            <p className="font-bold text-green-600">
              {riskReturn.expectedReturn.toFixed(3)}%
            </p>
          </div>
          <div>
            <p className="text-xs text-gray-500">Max Drawdown</p>
            <p className="font-bold text-red-600">
              {riskReturn.maxDrawdown.toFixed(3)}%
            </p>
          </div>
          <div>
            <p className="text-xs text-gray-500">Sharpe Ratio</p>
            <p className="font-bold text-blue-600">
              {riskReturn.sharpeRatio.toFixed(2)}
            </p>
          </div>
          <div>
            <p className="text-xs text-gray-500">Nível de Risco</p>
            <span className={`px-2 py-1 rounded text-xs font-medium ${
              riskReturn.riskLevel === 'high' ? 'bg-red-100 text-red-800' :
              riskReturn.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' :
              'bg-green-100 text-green-800'
            }`}>
              {riskReturn.riskLevel.toUpperCase()}
            </span>
          </div>
        </div>
      </Card>

      {/* Níveis de Suporte e Resistência */}
      {priceLevels.length > 0 && (
        <Card className="p-4">
          <h3 className="font-semibold mb-4 flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Níveis Importantes
          </h3>
          
          <div className="space-y-2">
            {priceLevels.map((level, index) => (
              <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    level.type === 'resistance' ? 'bg-red-500' : 'bg-green-500'
                  }`} />
                  <span className="text-sm font-medium">
                    ${level.price.toFixed(2)}
                  </span>
                  <span className="text-xs text-gray-500">
                    ({level.type})
                  </span>
                </div>
                <div className="text-xs text-gray-500">
                  {level.touches} toques | {level.strength}% força
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Padrões Detectados */}
      {patterns.length > 0 && (
        <Card className="p-4">
          <h3 className="font-semibold mb-4 flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            Padrões Detectados
          </h3>
          
          <div className="space-y-3">
            {patterns.map((pattern, index) => (
              <div key={index} className="border-l-4 border-blue-500 pl-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-sm">{pattern.name}</h4>
                  <span className="text-xs text-gray-500">
                    {pattern.probability.toFixed(0)}%
                  </span>
                </div>
                <p className="text-xs text-gray-600 mt-1">
                  {pattern.description}
                </p>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Resumo Executivo */}
      <Card className="p-4 bg-blue-50 border-blue-200">
        <h3 className="font-semibold mb-3 text-blue-900">Resumo Executivo</h3>
        <div className="text-sm text-blue-800 space-y-2">
          <p>
            <strong>{symbol}</strong> apresenta spread médio de <strong>{metrics.averageSpread.toFixed(3)}%</strong> 
            entre {spotExchange.toUpperCase()} (spot) e {futuresExchange.toUpperCase()} (futures).
          </p>
          <p>
            Volatilidade do spread: <strong>{metrics.spreadVolatility.toFixed(3)}%</strong> com 
            <strong> {metrics.inversions} inversões</strong> no período de {timeframe}.
          </p>
          <p>
            Recomendação: <strong>{tradingSignal.type.toUpperCase()}</strong> com 
            confiança de <strong>{tradingSignal.confidence}%</strong>.
          </p>
        </div>
      </Card>
    </div>
  )
}

export default ChartAnalytics