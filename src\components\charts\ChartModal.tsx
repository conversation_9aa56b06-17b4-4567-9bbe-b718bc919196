// ChartModal - Modal Responsivo para Gráficos Cross-Exchange

import { useState, useMemo } from 'react'
import { X, Download, ZoomIn, ZoomOut, RotateCcw, TrendingUp, TrendingDown, Activity } from 'lucide-react'
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Area,
  ComposedChart,
  ReferenceLine
} from 'recharts'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs'
import { useChartData } from '@/hooks/useChartData'
import { ChartAnalytics } from './ChartAnalytics'
import type { ArbitrageOpportunity } from '@/types/arbitrage'

interface ChartModalProps {
  isOpen: boolean
  onClose: () => void
  opportunity: ArbitrageOpportunity
  className?: string
}

type TimeframeOption = '1m' | '5m' | '15m' | '1h' | '4h' | '1d'

const TIMEFRAME_OPTIONS: { value: TimeframeOption; label: string }[] = [
  { value: '1m', label: '1 Minuto' },
  { value: '5m', label: '5 Minutos' },
  { value: '15m', label: '15 Minutos' },
  { value: '1h', label: '1 Hora' },
  { value: '4h', label: '4 Horas' },
  { value: '1d', label: '1 Dia' }
]

export function ChartModal({ 
  isOpen, 
  onClose, 
  opportunity, 
  className = '' 
}: ChartModalProps) {
  const [timeframe, setTimeframe] = useState<TimeframeOption>('1h')
  const [showSpreadArea, setShowSpreadArea] = useState(true)
  const [zoomLevel, setZoomLevel] = useState(1)

  // Hook para dados do gráfico
  const {
    data: chartData,
    metrics,
    isLoading,
    isError,
    error
  } = useChartData({
    symbol: opportunity.symbol,
    spotExchange: opportunity.spotExchange,
    futuresExchange: opportunity.futuresExchange,
    timeframe,
    enabled: isOpen
  })

  // Processar dados para o gráfico
  const processedData = useMemo(() => {
    return chartData.map(point => ({
      ...point,
      time: new Date(point.timestamp).toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit',
        ...(timeframe === '1d' ? { day: '2-digit', month: '2-digit' } : {})
      }),
      spreadAbs: Math.abs(point.spreadPercentage)
    }))
  }, [chartData, timeframe])

  // Configurações de cores baseadas na rentabilidade
  const getSpreadColor = (spread: number) => {
    const absSpread = Math.abs(spread)
    if (absSpread > 1.0) return '#10b981' // Verde para high
    if (absSpread > 0.5) return '#f59e0b' // Amarelo para medium
    return '#3b82f6' // Azul para low
  }

  // Tooltip customizado
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <Card className="p-4 shadow-lg border">
          <div className="space-y-2">
            <p className="font-semibold text-sm">{label}</p>
            <div className="grid grid-cols-2 gap-4 text-xs">
              <div>
                <p className="text-gray-500">Spot ({opportunity.spotExchange.toUpperCase()})</p>
                <p className="font-medium">${data.spotPrice.toFixed(4)}</p>
              </div>
              <div>
                <p className="text-gray-500">Futures ({opportunity.futuresExchange.toUpperCase()})</p>
                <p className="font-medium">${data.futuresPrice.toFixed(4)}</p>
              </div>
            </div>
            <div className="border-t pt-2">
              <p className="text-gray-500">Spread</p>
              <p className={`font-bold ${data.spreadPercentage > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {data.spreadPercentage > 0 ? '+' : ''}{data.spreadPercentage.toFixed(3)}%
              </p>
            </div>
            <div>
              <p className="text-gray-500">Volume</p>
              <p className="font-medium">${data.volume.toLocaleString()}</p>
            </div>
            <div>
              <p className="text-gray-500">Rentabilidade</p>
              <span className={`px-2 py-1 rounded text-xs font-medium ${
                data.profitability === 'HIGH' ? 'bg-green-100 text-green-800' :
                data.profitability === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
                'bg-blue-100 text-blue-800'
              }`}>
                {data.profitability.toUpperCase()}
              </span>
            </div>
          </div>
        </Card>
      )
    }
    return null
  }

  // Função para exportar dados
  const exportData = () => {
    const csvContent = [
      ['Timestamp', 'Spot Price', 'Futures Price', 'Spread %', 'Volume', 'Profitability'],
      ...chartData.map(point => [
        point.datetime,
        point.spotPrice.toFixed(4),
        point.futuresPrice.toFixed(4),
        point.spreadPercentage.toFixed(3),
        point.volume.toFixed(0),
        point.profitability
      ])
    ].map(row => row.join(',')).join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${opportunity.symbol}_${opportunity.spotExchange}_${opportunity.futuresExchange}_${timeframe}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }

  // Função para resetar zoom
  const resetZoom = () => {
    setZoomLevel(1)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden ${className}`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-bold text-gray-900">
              Gráfico Cross-Exchange: {opportunity.symbol}
            </h2>
            <p className="text-sm text-gray-600">
              {opportunity.spotExchange.toUpperCase()} (Spot) → {opportunity.futuresExchange.toUpperCase()} (Futures)
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <Select value={timeframe} onValueChange={(value) => setTimeframe(value as TimeframeOption)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {TIMEFRAME_OPTIONS.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSpreadArea(!showSpreadArea)}
            >
              <Activity className="h-4 w-4 mr-2" />
              {showSpreadArea ? 'Ocultar' : 'Mostrar'} Área
            </Button>
            
            <Button variant="outline" size="sm" onClick={exportData}>
              <Download className="h-4 w-4 mr-2" />
              Exportar
            </Button>
            
            <Button variant="outline" size="sm" onClick={resetZoom}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {isLoading ? (
            <div className="flex items-center justify-center h-96">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Carregando dados históricos...</span>
            </div>
          ) : isError ? (
            <div className="flex items-center justify-center h-96">
              <div className="text-center">
                <p className="text-red-600 mb-2">Erro ao carregar dados do gráfico</p>
                <p className="text-sm text-gray-500">{error?.message}</p>
              </div>
            </div>
          ) : (
            <Tabs defaultValue="chart" className="space-y-6">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="chart">Gráfico</TabsTrigger>
                <TabsTrigger value="analytics">Análise Avançada</TabsTrigger>
              </TabsList>

              <TabsContent value="chart" className="space-y-6">
              {/* Métricas principais */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card className="p-4">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-xs text-gray-500">Spread Médio</p>
                      <p className="font-bold text-green-600">
                        {metrics.averageSpread.toFixed(3)}%
                      </p>
                    </div>
                  </div>
                </Card>
                
                <Card className="p-4">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-xs text-gray-500">Máximo</p>
                      <p className="font-bold text-blue-600">
                        {metrics.maxSpread.toFixed(3)}%
                      </p>
                    </div>
                  </div>
                </Card>
                
                <Card className="p-4">
                  <div className="flex items-center gap-2">
                    <TrendingDown className="h-4 w-4 text-orange-600" />
                    <div>
                      <p className="text-xs text-gray-500">Volatilidade</p>
                      <p className="font-bold text-orange-600">
                        {metrics.spreadVolatility.toFixed(3)}%
                      </p>
                    </div>
                  </div>
                </Card>
                
                <Card className="p-4">
                  <div className="flex items-center gap-2">
                    <Activity className="h-4 w-4 text-purple-600" />
                    <div>
                      <p className="text-xs text-gray-500">Inversões</p>
                      <p className="font-bold text-purple-600">
                        {metrics.inversions}
                      </p>
                    </div>
                  </div>
                </Card>
              </div>

              {/* Gráfico principal */}
              <Card className="p-4">
                <div className="h-96">
                  <ResponsiveContainer width="100%" height="100%">
                    <ComposedChart data={processedData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis 
                        dataKey="time" 
                        stroke="#666"
                        fontSize={12}
                        interval="preserveStartEnd"
                      />
                      <YAxis 
                        yAxisId="price"
                        orientation="left"
                        stroke="#666"
                        fontSize={12}
                        tickFormatter={(value) => `$${value.toFixed(0)}`}
                      />
                      <YAxis 
                        yAxisId="spread"
                        orientation="right"
                        stroke="#666"
                        fontSize={12}
                        tickFormatter={(value) => `${value.toFixed(2)}%`}
                      />
                      
                      <Tooltip content={<CustomTooltip />} />
                      
                      <Legend />
                      
                      {/* Área do spread (opcional) */}
                      {showSpreadArea && (
                        <Area
                          yAxisId="spread"
                          type="monotone"
                          dataKey="spreadAbs"
                          fill="rgba(59, 130, 246, 0.1)"
                          stroke="none"
                        />
                      )}
                      
                      {/* Linhas de preço */}
                      <Line
                        yAxisId="price"
                        type="monotone"
                        dataKey="spotPrice"
                        stroke="#10b981"
                        strokeWidth={2}
                        dot={false}
                        name={`Spot (${opportunity.spotExchange.toUpperCase()})`}
                      />
                      
                      <Line
                        yAxisId="price"
                        type="monotone"
                        dataKey="futuresPrice"
                        stroke="#f59e0b"
                        strokeWidth={2}
                        dot={false}
                        name={`Futures (${opportunity.futuresExchange.toUpperCase()})`}
                      />
                      
                      {/* Linha do spread */}
                      <Line
                        yAxisId="spread"
                        type="monotone"
                        dataKey="spreadPercentage"
                        stroke="#3b82f6"
                        strokeWidth={2}
                        dot={false}
                        name="Spread %"
                      />
                      
                      {/* Linha de referência zero */}
                      <ReferenceLine yAxisId="spread" y={0} stroke="#666" strokeDasharray="2 2" />
                    </ComposedChart>
                  </ResponsiveContainer>
                </div>
              </Card>

              {/* Métricas detalhadas */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="p-4">
                  <h4 className="font-semibold mb-3">Eventos Detectados</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Aberturas:</span>
                      <span className="font-medium text-green-600">{metrics.openings}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Fechamentos:</span>
                      <span className="font-medium text-red-600">{metrics.closings}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Inversões:</span>
                      <span className="font-medium text-purple-600">{metrics.inversions}</span>
                    </div>
                  </div>
                </Card>
                
                <Card className="p-4">
                  <h4 className="font-semibold mb-3">Análise de Tendência</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Direção:</span>
                      <span className={`font-medium flex items-center gap-1 ${
                        metrics.trendDirection === 'up' ? 'text-green-600' :
                        metrics.trendDirection === 'down' ? 'text-red-600' :
                        'text-gray-600'
                      }`}>
                        {metrics.trendDirection === 'up' ? <TrendingUp className="h-3 w-3" /> :
                         metrics.trendDirection === 'down' ? <TrendingDown className="h-3 w-3" /> :
                         <Activity className="h-3 w-3" />}
                        {metrics.trendDirection === 'up' ? 'Subindo' :
                         metrics.trendDirection === 'down' ? 'Descendo' :
                         'Lateral'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Correlação:</span>
                      <span className="font-medium">
                        {(metrics.correlationScore * 100).toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </Card>
                
                <Card className="p-4">
                  <h4 className="font-semibold mb-3">Volume Total</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Período:</span>
                      <span className="font-medium">{TIMEFRAME_OPTIONS.find(t => t.value === timeframe)?.label}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Volume:</span>
                      <span className="font-medium">
                        ${metrics.totalVolume.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Pontos:</span>
                      <span className="font-medium">{chartData.length}</span>
                    </div>
                  </div>
                </Card>
              </div>
              </TabsContent>

              <TabsContent value="analytics">
                <ChartAnalytics
                  data={chartData}
                  metrics={metrics}
                  symbol={opportunity.symbol}
                  spotExchange={opportunity.spotExchange}
                  futuresExchange={opportunity.futuresExchange}
                  timeframe={timeframe}
                />
              </TabsContent>
            </Tabs>
          )}
        </div>
      </div>
    </div>
  )
}

export default ChartModal