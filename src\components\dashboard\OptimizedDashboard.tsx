// OTIMIZAÇÃO: Dashboard principal com performance otimizada

import React, { memo, useMemo, useCallback, useState } from 'react'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { VirtualizedOpportunityTable } from '@/components/opportunities/VirtualizedOpportunityTable'
import { useArbitrageData } from '@/hooks/useArbitrageData'
import { useOptimizedFilters } from '@/hooks/useOptimizedFilters'
import { 
  TrendingUp, 
  Zap, 
  Activity, 
  Wifi, 
  WifiOff, 
  RefreshCw,
  Filter,
  X
} from 'lucide-react'

// OTIMIZAÇÃO: Componente de métricas memoizado
const MetricsCard = memo<{
  title: string
  value: string | number
  subtitle?: string
  icon: React.ReactNode
  variant?: 'default' | 'success' | 'warning' | 'error'
}>(({ title, value, subtitle, icon, variant = 'default' }) => {
  const cardClass = useMemo(() => {
    switch (variant) {
      case 'success': return 'border-green-500 bg-green-50 dark:bg-green-950'
      case 'warning': return 'border-yellow-500 bg-yellow-50 dark:bg-yellow-950'
      case 'error': return 'border-red-500 bg-red-50 dark:bg-red-950'
      default: return ''
    }
  }, [variant])

  return (
    <Card className={`p-4 ${cardClass}`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
          {subtitle && (
            <p className="text-xs text-muted-foreground">{subtitle}</p>
          )}
        </div>
        <div className="text-muted-foreground">
          {icon}
        </div>
      </div>
    </Card>
  )
})

MetricsCard.displayName = 'MetricsCard'

// OTIMIZAÇÃO: Componente de status WebSocket memoizado
const WebSocketStatus = memo<{
  status: 'connecting' | 'connected' | 'disconnected' | 'error'
  metrics: {
    connectedClients: number
    messagesSent: number
    messagesReceived: number
    averageLatency: number
  }
  realTimeUpdates: number
}>(({ status, metrics, realTimeUpdates }) => {
  const statusConfig = useMemo(() => {
    switch (status) {
      case 'connected':
        return { 
          icon: <Wifi className="w-4 h-4" />, 
          color: 'text-green-600', 
          bg: 'bg-green-100 dark:bg-green-900',
          text: 'Conectado' 
        }
      case 'connecting':
        return { 
          icon: <RefreshCw className="w-4 h-4 animate-spin" />, 
          color: 'text-yellow-600', 
          bg: 'bg-yellow-100 dark:bg-yellow-900',
          text: 'Conectando...' 
        }
      case 'error':
        return { 
          icon: <WifiOff className="w-4 h-4" />, 
          color: 'text-red-600', 
          bg: 'bg-red-100 dark:bg-red-900',
          text: 'Erro' 
        }
      default:
        return { 
          icon: <WifiOff className="w-4 h-4" />, 
          color: 'text-gray-600', 
          bg: 'bg-gray-100 dark:bg-gray-900',
          text: 'Desconectado' 
        }
    }
  }, [status])

  return (
    <div className={`flex items-center gap-2 px-3 py-1 rounded-full ${statusConfig.bg}`}>
      <div className={statusConfig.color}>
        {statusConfig.icon}
      </div>
      <span className={`text-sm font-medium ${statusConfig.color}`}>
        {statusConfig.text}
      </span>
      {status === 'connected' && (
        <Badge variant="secondary" size="sm">
          {realTimeUpdates} updates
        </Badge>
      )}
    </div>
  )
})

WebSocketStatus.displayName = 'WebSocketStatus'

// OTIMIZAÇÃO: Componente principal do dashboard
export const OptimizedDashboard = memo(() => {
  const [selectedOpportunity, setSelectedOpportunity] = useState<any>(null)

  // OTIMIZAÇÃO: Hook de dados com WebSocket habilitado
  const {
    opportunities,
    metrics,
    isLoading,
    isError,
    error,
    lastUpdate,
    dataAge,
    connectionStatus,
    performanceMetrics,
    webSocketStatus,
    webSocketMetrics,
    realTimeUpdates,
    refetch
  } = useArbitrageData({
    enableWebSocket: true,
    refetchInterval: 1000, // 1 segundo
    staleTime: 500
  })

  // OTIMIZAÇÃO: Hook de filtros otimizado
  const {
    filters,
    filteredOpportunities,
    filterStats,
    filterOptions,
    updateFilter,
    resetFilters,
    applyQuickFilter,
    isFiltering
  } = useOptimizedFilters(opportunities)

  // OTIMIZAÇÃO: Métricas calculadas memoizadas
  const dashboardMetrics = useMemo(() => {
    const total = opportunities.length
    const highProfit = opportunities.filter(opp => 
      Math.abs(opp.spreadPercentage) >= 1.0
    ).length
    const avgSpread = opportunities.length > 0 
      ? opportunities.reduce((sum, opp) => sum + Math.abs(opp.spreadPercentage), 0) / opportunities.length
      : 0
    const totalVolume = opportunities.reduce((sum, opp) => sum + (opp.spotVolume || 0), 0)

    return {
      total,
      highProfit,
      avgSpread: avgSpread.toFixed(3),
      totalVolume: totalVolume >= 1000000 
        ? `$${(totalVolume / 1000000).toFixed(1)}M`
        : `$${(totalVolume / 1000).toFixed(1)}K`
    }
  }, [opportunities])

  // OTIMIZAÇÃO: Callback para seleção de oportunidade
  const handleOpportunityClick = useCallback((opportunity: any) => {
    setSelectedOpportunity(opportunity)
  }, [])

  // OTIMIZAÇÃO: Callback para refresh manual
  const handleRefresh = useCallback(() => {
    refetch()
  }, [refetch])

  if (isError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Activity className="w-12 h-12 mx-auto text-red-500 mb-4" />
          <h3 className="text-lg font-semibold mb-2 text-red-600">Erro na Conexão</h3>
          <p className="text-muted-foreground mb-4">
            {error?.message || 'Erro desconhecido'}
          </p>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Tentar Novamente
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header com Status */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Dashboard de Arbitragem</h1>
          <p className="text-muted-foreground">
            Sistema otimizado para ultra baixa latência
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <WebSocketStatus 
            status={webSocketStatus}
            metrics={webSocketMetrics}
            realTimeUpdates={realTimeUpdates}
          />
          
          <Badge variant="outline" className="flex items-center gap-1">
            <Zap className="w-3 h-3" />
            {performanceMetrics.responseTime}ms
          </Badge>
          
          <Button 
            onClick={handleRefresh} 
            variant="outline" 
            size="sm"
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
        </div>
      </div>

      {/* Métricas Principais */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricsCard
          title="Total de Oportunidades"
          value={dashboardMetrics.total.toLocaleString()}
          subtitle="Cross-exchange"
          icon={<TrendingUp className="w-6 h-6" />}
          variant="default"
        />
        
        <MetricsCard
          title="Alta Rentabilidade"
          value={dashboardMetrics.highProfit}
          subtitle=">1% spread"
          icon={<Zap className="w-6 h-6" />}
          variant="success"
        />
        
        <MetricsCard
          title="Spread Médio"
          value={`${dashboardMetrics.avgSpread}%`}
          subtitle="Todas as exchanges"
          icon={<Activity className="w-6 h-6" />}
          variant="default"
        />
        
        <MetricsCard
          title="Volume Total"
          value={dashboardMetrics.totalVolume}
          subtitle="Últimas 24h"
          icon={<TrendingUp className="w-6 h-6" />}
          variant="default"
        />
      </div>

      {/* Filtros Rápidos */}
      <div className="flex items-center gap-2 flex-wrap">
        <Button
          onClick={() => applyQuickFilter('high-profit')}
          variant="outline"
          size="sm"
        >
          Alta Rentabilidade
        </Button>
        <Button
          onClick={() => applyQuickFilter('recent')}
          variant="outline"
          size="sm"
        >
          Mais Recentes
        </Button>
        <Button
          onClick={() => applyQuickFilter('high-volume')}
          variant="outline"
          size="sm"
        >
          Alto Volume
        </Button>
        <Button
          onClick={() => applyQuickFilter('cross-exchange')}
          variant="outline"
          size="sm"
        >
          Cross-Exchange
        </Button>
        
        {isFiltering && (
          <Button
            onClick={resetFilters}
            variant="ghost"
            size="sm"
            className="text-red-600"
          >
            <X className="w-4 h-4 mr-1" />
            Limpar Filtros
          </Button>
        )}
      </div>

      {/* Filtros Detalhados */}
      <Card className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <Filter className="w-4 h-4" />
          <span className="font-semibold">Filtros Avançados</span>
          <Badge variant="secondary">
            {filterStats.filtered}/{filterStats.total} oportunidades
          </Badge>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Input
            placeholder="Buscar símbolo..."
            value={filters.searchTerm}
            onChange={(e) => updateFilter('searchTerm', e.target.value)}
          />
          
          <Select
            value={filters.spotExchange}
            onValueChange={(value) => updateFilter('spotExchange', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Exchange Spot" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Todas</SelectItem>
              {filterOptions.spotExchanges.map(exchange => (
                <SelectItem key={exchange} value={exchange}>
                  {exchange.toUpperCase()}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select
            value={filters.futuresExchange}
            onValueChange={(value) => updateFilter('futuresExchange', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Exchange Futures" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Todas</SelectItem>
              {filterOptions.futuresExchanges.map(exchange => (
                <SelectItem key={exchange} value={exchange}>
                  {exchange.toUpperCase()}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Input
            type="number"
            placeholder="Spread mínimo (%)"
            value={filters.minSpread}
            onChange={(e) => updateFilter('minSpread', Number(e.target.value))}
            step="0.1"
            min="0"
          />
        </div>
      </Card>

      {/* Tabela de Oportunidades Virtualizada */}
      <VirtualizedOpportunityTable
        opportunities={filteredOpportunities}
        height={600}
        onOpportunityClick={handleOpportunityClick}
        className="w-full"
      />

      {/* Informações de Performance */}
      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <span className="font-semibold">Última Atualização:</span>
            <br />
            {lastUpdate?.toLocaleTimeString() || 'Nunca'}
          </div>
          <div>
            <span className="font-semibold">Idade dos Dados:</span>
            <br />
            {dataAge < 1000 ? `${dataAge}ms` : `${(dataAge / 1000).toFixed(1)}s`}
          </div>
          <div>
            <span className="font-semibold">Status da Conexão:</span>
            <br />
            <Badge variant={connectionStatus === 'connected' ? 'default' : 'destructive'}>
              {connectionStatus}
            </Badge>
          </div>
        </div>
      </Card>
    </div>
  )
})

OptimizedDashboard.displayName = 'OptimizedDashboard'

export default OptimizedDashboard
