// SimpleDashboard - Dashboard Simplificado para Teste e Produção

import React, { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { RefreshCw, TrendingUp, Activity, Zap } from 'lucide-react'
import useArbitrageData from '@/hooks/useArbitrageData'

export function SimpleDashboard() {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Hook de dados de arbitragem
  const {
    opportunities,
    metrics,
    isLoading: dataLoading,
    isError,
    error: dataError,
    refetch,
    connectionStatus
  } = useArbitrageData({
    enabled: true,
    refetchInterval: 30000, // 30 segundos
    enableAlerts: false // Desabilitar alertas por enquanto
  })

  useEffect(() => {
    // Simular carregamento inicial
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 2000)

    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    if (isError && dataError) {
      setError(dataError.message)
    } else {
      setError(null)
    }
  }, [isError, dataError])

  const handleRefresh = () => {
    setError(null)
    refetch()
  }

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
            <h2 className="text-xl font-semibold mb-2">Carregando Sistema de Arbitragem</h2>
            <p className="text-gray-600">Inicializando conexões com exchanges...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Sistema de Arbitragem</h1>
          <p className="text-gray-600 mt-1">
            Monitoramento de oportunidades cross-exchange em tempo real
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
            connectionStatus === 'connected' ? 'bg-green-100 text-green-700' :
            connectionStatus === 'connecting' ? 'bg-yellow-100 text-yellow-700' :
            'bg-red-100 text-red-700'
          }`}>
            <div className={`w-2 h-2 rounded-full ${
              connectionStatus === 'connected' ? 'bg-green-500' :
              connectionStatus === 'connecting' ? 'bg-yellow-500' :
              'bg-red-500'
            }`} />
            <span className="capitalize">{connectionStatus}</span>
          </div>
          <Button onClick={handleRefresh} variant="outline" disabled={dataLoading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${dataLoading ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="p-4 border-red-200 bg-red-50">
          <div className="flex items-center space-x-2 text-red-700">
            <Activity className="w-5 h-5" />
            <div>
              <h3 className="font-semibold">Erro na Coleta de Dados</h3>
              <p className="text-sm mt-1">{error}</p>
              <Button 
                onClick={handleRefresh} 
                variant="outline" 
                size="sm" 
                className="mt-2"
              >
                Tentar Novamente
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <TrendingUp className="w-8 h-8 text-blue-600" />
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {opportunities?.length || 0}
              </div>
              <div className="text-sm text-gray-600">Oportunidades</div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <Zap className="w-8 h-8 text-green-600" />
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {metrics?.spotFuturesCrossExchange || 0}
              </div>
              <div className="text-sm text-gray-600">Spot-Futures</div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <Activity className="w-8 h-8 text-purple-600" />
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {metrics?.futuresFuturesCrossExchange || 0}
              </div>
              <div className="text-sm text-gray-600">Futures-Futures</div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <TrendingUp className="w-8 h-8 text-orange-600" />
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {metrics?.averageSpread ? `${metrics.averageSpread.toFixed(2)}%` : '0.00%'}
              </div>
              <div className="text-sm text-gray-600">Spread Médio</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Opportunities Table */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Oportunidades de Arbitragem</h2>
          <div className="text-sm text-gray-600">
            {dataLoading ? 'Atualizando...' : `${opportunities?.length || 0} oportunidades encontradas`}
          </div>
        </div>

        {dataLoading ? (
          <div className="flex items-center justify-center py-12">
            <RefreshCw className="w-6 h-6 animate-spin text-blue-600 mr-2" />
            <span className="text-gray-600">Carregando oportunidades...</span>
          </div>
        ) : opportunities && opportunities.length > 0 ? (
          <div className="space-y-3">
            {opportunities.slice(0, 10).map((opportunity, index) => (
              <div 
                key={opportunity.id || index}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="flex-1">
                  <div className="font-semibold text-gray-900">
                    {opportunity.symbol}
                  </div>
                  <div className="text-sm text-gray-600">
                    {opportunity.spotExchange} → {opportunity.futuresExchange}
                  </div>
                </div>
                
                <div className="text-right">
                  <div className={`font-bold ${
                    opportunity.spreadPercentage > 1 ? 'text-green-600' :
                    opportunity.spreadPercentage > 0.5 ? 'text-blue-600' :
                    'text-gray-600'
                  }`}>
                    {opportunity.spreadPercentage > 0 ? '+' : ''}{opportunity.spreadPercentage.toFixed(2)}%
                  </div>
                  <div className="text-sm text-gray-500">
                    {opportunity.profitability}
                  </div>
                </div>
              </div>
            ))}
            
            {opportunities.length > 10 && (
              <div className="text-center py-4">
                <p className="text-gray-600">
                  E mais {opportunities.length - 10} oportunidades...
                </p>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-12">
            <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 mb-2">
              Nenhuma oportunidade encontrada
            </h3>
            <p className="text-gray-500 mb-4">
              O sistema está coletando dados das exchanges. Aguarde alguns instantes.
            </p>
            <Button onClick={handleRefresh} variant="outline">
              <RefreshCw className="w-4 h-4 mr-2" />
              Atualizar Agora
            </Button>
          </div>
        )}
      </Card>

      {/* Debug Info */}
      {process.env.NODE_ENV === 'development' && (
        <Card className="p-4 bg-gray-50">
          <h3 className="font-semibold mb-2">Debug Info</h3>
          <div className="text-sm space-y-1">
            <p>Connection Status: {connectionStatus}</p>
            <p>Data Loading: {dataLoading ? 'Yes' : 'No'}</p>
            <p>Has Error: {isError ? 'Yes' : 'No'}</p>
            <p>Opportunities Count: {opportunities?.length || 0}</p>
            <p>Metrics Available: {metrics ? 'Yes' : 'No'}</p>
          </div>
        </Card>
      )}
    </div>
  )
}