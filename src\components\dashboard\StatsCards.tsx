// StatsCards - Cards de Estatísticas do Dashboard

import { TrendingUp, Activity, BarChart3, Zap } from 'lucide-react'
import { Card } from '@/components/ui/Card'
import type { DashboardMetrics } from '@/types/arbitrage'

interface StatsCardsProps {
  metrics: DashboardMetrics | null
  isLoading?: boolean
}

interface StatCardProps {
  title: string
  value: string
  icon: React.ComponentType<any>
  color: string
  bgColor: string
  description: string
  trend?: {
    value: number
    isPositive: boolean
  }
  tooltip?: string
}

function StatCard({ 
  title, 
  value, 
  icon: Icon, 
  color, 
  bgColor, 
  description, 
  trend,
  tooltip 
}: StatCardProps) {
  return (
    <Card className="p-6 hover:shadow-md transition-all duration-200 group">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <p className="text-sm font-medium text-gray-600">
              {title}
            </p>
            {tooltip && (
              <div className="relative group/tooltip">
                <div className="w-4 h-4 rounded-full bg-gray-200 flex items-center justify-center text-xs text-gray-500 cursor-help">
                  ?
                </div>
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover/tooltip:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                  {tooltip}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                </div>
              </div>
            )}
          </div>
          
          <div className="flex items-baseline gap-2 mb-1">
            <p className="text-2xl font-bold text-gray-900 group-hover:scale-105 transition-transform duration-200">
              {value}
            </p>
            {trend && (
              <span className={`text-sm font-medium flex items-center ${
                trend.isPositive ? 'text-green-600' : 'text-red-600'
              }`}>
                {trend.isPositive ? '↗' : '↘'} {Math.abs(trend.value)}%
              </span>
            )}
          </div>
          
          <p className="text-xs text-gray-500 leading-tight">
            {description}
          </p>
        </div>
        
        <div className={`p-3 rounded-full ${bgColor} group-hover:scale-110 transition-transform duration-200`}>
          <Icon className={`h-6 w-6 ${color}`} />
        </div>
      </div>
    </Card>
  )
}

function LoadingStatCard() {
  return (
    <Card className="p-6">
      <div className="animate-pulse">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-full"></div>
          </div>
          <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
        </div>
      </div>
    </Card>
  )
}

export function StatsCards({ metrics, isLoading }: StatsCardsProps) {
  if (isLoading || !metrics) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[1, 2, 3, 4].map(i => (
          <LoadingStatCard key={i} />
        ))}
      </div>
    )
  }

  const stats = [
    {
      title: 'Total Oportunidades',
      value: (metrics.totalOpportunities || 0).toLocaleString(),
      icon: TrendingUp,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      description: 'Oportunidades cross-exchange detectadas',
      trend: {
        value: 12.5,
        isPositive: true
      },
      tooltip: 'Total de oportunidades de arbitragem entre diferentes exchanges'
    },
    {
      title: 'Spot-Futures Cross',
      value: (metrics.spotFuturesCount || 0).toLocaleString(),
      icon: Activity,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      description: 'Arbitragem spot vs futuros entre exchanges',
      trend: {
        value: 8.3,
        isPositive: true
      },
      tooltip: 'Oportunidades entre mercado spot de uma exchange e futuros de outra'
    },
    {
      title: 'Futures-Futures Cross',
      value: (metrics.futuresFuturesCount || 0).toLocaleString(),
      icon: BarChart3,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      description: 'Arbitragem futuros vs futuros entre exchanges',
      trend: {
        value: 15.7,
        isPositive: true
      },
      tooltip: 'Oportunidades entre mercados de futuros de diferentes exchanges'
    },
    {
      title: 'Spread Médio',
      value: `${(metrics.averageSpread || 0).toFixed(3)}%`,
      icon: Zap,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      description: 'Spread médio das oportunidades',
      trend: {
        value: 2.1,
        isPositive: false
      },
      tooltip: 'Diferença percentual média entre preços nas diferentes exchanges'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => (
        <StatCard
          key={index}
          title={stat.title}
          value={stat.value}
          icon={stat.icon}
          color={stat.color}
          bgColor={stat.bgColor}
          description={stat.description}
          trend={stat.trend}
          tooltip={stat.tooltip}
        />
      ))}
    </div>
  )
}

export default StatsCards