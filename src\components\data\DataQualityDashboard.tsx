// DataQualityDashboard - Dashboard de Qualidade dos Dados das Exchanges

import { useState, useEffect } from 'react'
import { BarChart3, AlertTriangle, CheckCircle, Clock, Database, TrendingUp, Wifi } from 'lucide-react'
import { Card } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'
import { ExchangeAPI } from '@/services/ExchangeAPI'
import type { DataQualityMetrics } from '@/services/data/DataQualityMonitor'

interface DataQualityDashboardProps {
  className?: string
}

interface QualityAlert {
  exchange: string
  severity: 'low' | 'medium' | 'high'
  message: string
  timestamp: number
}

export function DataQualityDashboard({ className = '' }: DataQualityDashboardProps) {
  const [qualityMetrics, setQualityMetrics] = useState<Record<string, DataQualityMetrics[]>>({})
  const [qualityAlerts, setQualityAlerts] = useState<QualityAlert[]>([])
  const [collectionStats, setCollectionStats] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)

  const exchangeAPI = ExchangeAPI.getInstance()

  // Carregar dados de qualidade
  const loadQualityData = async () => {
    setIsLoading(true)
    
    try {
      // Obter métricas de qualidade para cada exchange
      const exchanges = ['gateio', 'mexc', 'bitget']
      const metrics: Record<string, DataQualityMetrics[]> = {}
      
      exchanges.forEach(exchange => {
        metrics[exchange] = exchangeAPI.getDataQualityMetrics(exchange) as DataQualityMetrics[]
      })
      
      // Obter alertas e estatísticas
      const alerts = exchangeAPI.getQualityAlerts() as QualityAlert[]
      const stats = exchangeAPI.getCollectionStats()
      
      setQualityMetrics(metrics)
      setQualityAlerts(alerts)
      setCollectionStats(stats)
      setLastUpdate(new Date())
      
    } catch (error) {
      console.error('Error loading quality data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Carregar dados na montagem e configurar atualização automática
  useEffect(() => {
    loadQualityData()
    
    const interval = setInterval(loadQualityData, 30000) // Atualizar a cada 30s
    return () => clearInterval(interval)
  }, [])

  // Obter métricas mais recentes de uma exchange
  const getLatestMetrics = (exchange: string): DataQualityMetrics | null => {
    const metrics = qualityMetrics[exchange]
    return metrics && metrics.length > 0 ? metrics[metrics.length - 1] : null
  }

  // Calcular estatísticas gerais
  const overallStats = {
    totalRecords: Object.values(qualityMetrics).reduce((sum, metrics) => {
      const latest = metrics[metrics.length - 1]
      return sum + (latest?.totalRecords || 0)
    }, 0),
    avgValidationRate: Object.values(qualityMetrics).reduce((sum, metrics, index, array) => {
      const latest = metrics[metrics.length - 1]
      const rate = latest?.validationRate || 0
      return index === array.length - 1 ? (sum + rate) / array.length : sum + rate
    }, 0),
    totalIssues: qualityAlerts.length,
    highSeverityAlerts: qualityAlerts.filter(a => a.severity === 'high').length
  }

  // Ícone de severidade
  const getSeverityIcon = (severity: 'low' | 'medium' | 'high') => {
    switch (severity) {
      case 'high': return <AlertTriangle className="h-4 w-4 text-red-600" />
      case 'medium': return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'low': return <AlertTriangle className="h-4 w-4 text-blue-600" />
    }
  }

  // Badge de severidade
  const getSeverityBadge = (severity: 'low' | 'medium' | 'high') => {
    const variants = {
      high: 'destructive' as const,
      medium: 'warning' as const,
      low: 'info' as const
    }
    return <Badge variant={variants[severity]}>{severity.toUpperCase()}</Badge>
  }

  // Cor da taxa de validação
  const getValidationRateColor = (rate: number) => {
    if (rate >= 95) return 'text-green-600'
    if (rate >= 85) return 'text-yellow-600'
    return 'text-red-600'
  }

  // Formatar timestamp
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('pt-BR')
  }

  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <Card className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="space-y-3">
              {[1, 2, 3].map(i => (
                <div key={i} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header com estatísticas gerais */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Database className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold">Qualidade dos Dados - 6,800+ Pares</h3>
          </div>
          <Button
            onClick={loadQualityData}
            size="sm"
            variant="outline"
            disabled={isLoading}
          >
            {isLoading ? 'Atualizando...' : 'Atualizar'}
          </Button>
        </div>

        {/* Estatísticas gerais */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{overallStats.totalRecords.toLocaleString()}</div>
            <div className="text-sm text-gray-500">Total de Registros</div>
          </div>
          <div className="text-center">
            <div className={`text-2xl font-bold ${getValidationRateColor(overallStats.avgValidationRate)}`}>
              {overallStats.avgValidationRate.toFixed(1)}%
            </div>
            <div className="text-sm text-gray-500">Taxa de Validação</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{overallStats.totalIssues}</div>
            <div className="text-sm text-gray-500">Issues Detectados</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{overallStats.highSeverityAlerts}</div>
            <div className="text-sm text-gray-500">Alertas Críticos</div>
          </div>
        </div>

        {lastUpdate && (
          <div className="text-xs text-gray-500 text-center">
            Última atualização: {lastUpdate.toLocaleString('pt-BR')}
          </div>
        )}
      </Card>

      {/* Métricas por exchange */}
      <div className="grid gap-6">
        {['gateio', 'mexc', 'bitget'].map(exchange => {
          const latest = getLatestMetrics(exchange)
          const exchangeNames = {
            gateio: 'Gate.io',
            mexc: 'MEXC',
            bitget: 'Bitget'
          }

          return (
            <Card key={exchange} className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <Wifi className="h-5 w-5 text-green-600" />
                  <h4 className="text-lg font-semibold">{exchangeNames[exchange as keyof typeof exchangeNames]}</h4>
                </div>
                {latest && (
                  <Badge variant={latest.validationRate >= 95 ? 'success' : latest.validationRate >= 85 ? 'warning' : 'destructive'}>
                    {latest.validationRate.toFixed(1)}% Válidos
                  </Badge>
                )}
              </div>

              {latest ? (
                <>
                  {/* Métricas principais */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-lg font-semibold text-gray-900">{latest.totalRecords.toLocaleString()}</div>
                      <div className="text-xs text-gray-500">Total</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-green-600">{latest.validRecords.toLocaleString()}</div>
                      <div className="text-xs text-gray-500">Válidos</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-red-600">{latest.invalidRecords.toLocaleString()}</div>
                      <div className="text-xs text-gray-500">Inválidos</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-purple-600">${latest.avgPrice.toFixed(4)}</div>
                      <div className="text-xs text-gray-500">Preço Médio</div>
                    </div>
                  </div>

                  {/* Issues detectados */}
                  {latest.issues.length > 0 && (
                    <div className="space-y-2">
                      <h5 className="font-medium text-gray-900 flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-yellow-600" />
                        Issues Detectados ({latest.issues.length})
                      </h5>
                      <div className="space-y-1">
                        {latest.issues.map((issue, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm">
                            <div className="flex items-center gap-2">
                              {getSeverityIcon(issue.severity)}
                              <span>{issue.description}</span>
                            </div>
                            {getSeverityBadge(issue.severity)}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Timestamp */}
                  <div className="text-xs text-gray-500 mt-4">
                    Última coleta: {formatTime(latest.timestamp)}
                  </div>
                </>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Database className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>Nenhum dado de qualidade disponível</p>
                  <p className="text-xs">Execute uma coleta de dados para ver as métricas</p>
                </div>
              )}
            </Card>
          )
        })}
      </div>

      {/* Alertas recentes */}
      {qualityAlerts.length > 0 && (
        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <h4 className="text-lg font-semibold">Alertas de Qualidade ({qualityAlerts.length})</h4>
          </div>
          
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {qualityAlerts.slice(0, 10).map((alert, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-3">
                  {getSeverityIcon(alert.severity)}
                  <div>
                    <div className="font-medium text-sm">{alert.exchange.toUpperCase()}</div>
                    <div className="text-sm text-gray-600">{alert.message}</div>
                  </div>
                </div>
                <div className="text-right">
                  {getSeverityBadge(alert.severity)}
                  <div className="text-xs text-gray-500 mt-1">
                    {formatTime(alert.timestamp)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Estatísticas de coleta */}
      {collectionStats && (
        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <TrendingUp className="h-5 w-5 text-green-600" />
            <h4 className="text-lg font-semibold">Estatísticas de Coleta</h4>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-xl font-bold text-blue-600">{collectionStats.totalExchanges}</div>
              <div className="text-sm text-gray-500">Exchanges Ativas</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-green-600">{collectionStats.totalRecords?.toLocaleString()}</div>
              <div className="text-sm text-gray-500">Registros Coletados</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-red-600">{collectionStats.activeAlerts}</div>
              <div className="text-sm text-gray-500">Alertas Ativos</div>
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}

export default DataQualityDashboard