// ProductionDeploymentDashboard - Dashboard para Deploy em Produção

import React, { useState, useEffect } from 'react'
import { Card } from '../ui/Card'
import { Button } from '../ui/Button'
import { Badge } from '../ui/Badge'
import { 
  Rocket, 
  Play, 
  Square, 
  RotateCcw,
  CheckCircle, 
  XCircle, 
  Clock,
  AlertTriangle,
  RefreshCw,
  Download,
  Eye,
  EyeOff,
  Server,
  Database,
  Shield,
  Activity,
  TrendingUp,
  Settings,
  GitBranch,
  Package,
  Monitor
} from 'lucide-react'

interface DeploymentConfig {
  environment: 'staging' | 'production'
  version: string
  buildNumber: string
  deploymentStrategy: 'blue-green' | 'rolling' | 'canary'
  healthChecks: HealthCheckConfig[]
  rollbackConfig: RollbackConfig
  monitoring: MonitoringConfig
}

interface HealthCheckConfig {
  name: string
  endpoint: string
  timeout: number
  retries: number
  interval: number
  expectedStatus: number
}

interface RollbackConfig {
  enabled: boolean
  autoRollback: boolean
  rollbackThreshold: {
    errorRate: number
    responseTime: number
    availability: number
  }
  maxRollbackTime: number
}

interface MonitoringConfig {
  metrics: string[]
  alerts: AlertConfig[]
  dashboards: string[]
  logLevel: 'error' | 'warn' | 'info' | 'debug'
}

interface AlertConfig {
  name: string
  condition: string
  threshold: number
  severity: 'low' | 'medium' | 'high' | 'critical'
  channels: string[]
}

interface DeploymentResult {
  timestamp: number
  config: DeploymentConfig
  status: 'success' | 'failed' | 'rolled_back' | 'in_progress'
  duration: number
  steps: DeploymentStep[]
  healthChecks: HealthCheckResult[]
  metrics: DeploymentMetrics
  rollbackInfo?: RollbackInfo
  logs: string[]
}

interface DeploymentStep {
  name: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  startTime: number
  endTime?: number
  duration?: number
  logs: string[]
  error?: string
}

interface HealthCheckResult {
  name: string
  status: 'healthy' | 'unhealthy' | 'unknown'
  responseTime: number
  lastCheck: number
  error?: string
}

interface DeploymentMetrics {
  buildTime: number
  deployTime: number
  totalTime: number
  artifactSize: number
  testCoverage: number
  codeQuality: number
  securityScore: number
}

interface RollbackInfo {
  triggered: boolean
  reason: string
  previousVersion: string
  rollbackTime: number
  status: 'success' | 'failed'
}

const ProductionDeploymentDashboard: React.FC = () => {
  const [deployment, setDeployment] = useState<DeploymentResult | null>(null)
  const [deploymentHistory, setDeploymentHistory] = useState<DeploymentResult[]>([])
  const [isDeploying, setIsDeploying] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['overview']))
  const [selectedEnvironment, setSelectedEnvironment] = useState<'staging' | 'production'>('staging')

  // Configuração padrão de deployment
  const defaultConfig: DeploymentConfig = {
    environment: selectedEnvironment,
    version: '1.0.0',
    buildNumber: `${Date.now()}`,
    deploymentStrategy: selectedEnvironment === 'production' ? 'blue-green' : 'rolling',
    healthChecks: [
      {
        name: 'API Health Check',
        endpoint: '/health',
        timeout: 5000,
        retries: 3,
        interval: 30000,
        expectedStatus: 200
      },
      {
        name: 'Database Connection',
        endpoint: '/health/db',
        timeout: 10000,
        retries: 2,
        interval: 60000,
        expectedStatus: 200
      },
      {
        name: 'Exchange APIs Critical',
        endpoint: '/health/exchanges',
        timeout: 15000,
        retries: 3,
        interval: 30000,
        expectedStatus: 200
      }
    ],
    rollbackConfig: {
      enabled: true,
      autoRollback: selectedEnvironment === 'production',
      rollbackThreshold: {
        errorRate: 5,
        responseTime: 5000,
        availability: 95
      },
      maxRollbackTime: 300000
    },
    monitoring: {
      metrics: ['response_time', 'error_rate', 'throughput', 'memory_usage', 'cpu_usage'],
      alerts: [
        {
          name: 'High Error Rate',
          condition: 'error_rate > 5%',
          threshold: 5,
          severity: 'high',
          channels: ['email', 'slack']
        },
        {
          name: 'Slow Response Time',
          condition: 'response_time > 3s',
          threshold: 3000,
          severity: 'medium',
          channels: ['slack']
        }
      ],
      dashboards: ['system-overview', 'api-performance', 'business-metrics'],
      logLevel: selectedEnvironment === 'production' ? 'warn' : 'info'
    }
  }

  // Executar deployment
  const startDeployment = async () => {
    setIsDeploying(true)
    setLoading(true)
    setError(null)
    
    try {
      // Simular deployment
      const mockDeployment: DeploymentResult = {
        timestamp: Date.now(),
        config: defaultConfig,
        status: 'in_progress',
        duration: 0,
        steps: [],
        healthChecks: [],
        metrics: {
          buildTime: 0,
          deployTime: 0,
          totalTime: 0,
          artifactSize: 0,
          testCoverage: 0,
          codeQuality: 0,
          securityScore: 0
        },
        logs: []
      }
      
      setDeployment(mockDeployment)
      
      // Simular steps do deployment
      const steps = [
        'Pre-deployment Checks',
        'Build Application',
        'Run Tests',
        'Security Scan',
        'Build Docker Image',
        'Deploy to Environment',
        'Database Migration',
        'Configure Load Balancer',
        'Update DNS',
        'Post-deployment Verification'
      ]
      
      for (let i = 0; i < steps.length; i++) {
        const step: DeploymentStep = {
          name: steps[i],
          status: 'running',
          startTime: Date.now(),
          logs: [`Starting ${steps[i]}...`, `Processing ${steps[i]}...`, `Completing ${steps[i]}...`]
        }
        
        mockDeployment.steps.push(step)
        setDeployment({ ...mockDeployment })
        
        // Simular duração do step
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        step.status = 'completed'
        step.endTime = Date.now()
        step.duration = step.endTime - step.startTime
        
        setDeployment({ ...mockDeployment })
      }
      
      // Simular health checks
      for (const healthCheckConfig of defaultConfig.healthChecks) {
        const healthCheck: HealthCheckResult = {
          name: healthCheckConfig.name,
          status: Math.random() > 0.1 ? 'healthy' : 'unhealthy',
          responseTime: Math.random() * 1000 + 200,
          lastCheck: Date.now()
        }
        
        if (healthCheck.status === 'unhealthy') {
          healthCheck.error = 'Connection timeout'
        }
        
        mockDeployment.healthChecks.push(healthCheck)
      }
      
      // Finalizar deployment
      mockDeployment.status = mockDeployment.healthChecks.every(hc => hc.status === 'healthy') ? 'success' : 'failed'
      mockDeployment.duration = Date.now() - mockDeployment.timestamp
      mockDeployment.metrics = {
        buildTime: 45000,
        deployTime: 120000,
        totalTime: mockDeployment.duration,
        artifactSize: Math.random() * 50 + 20,
        testCoverage: Math.random() * 15 + 85,
        codeQuality: Math.random() * 10 + 90,
        securityScore: Math.random() * 10 + 90
      }
      
      // Se falhou, simular rollback
      if (mockDeployment.status === 'failed' && defaultConfig.rollbackConfig.autoRollback) {
        mockDeployment.rollbackInfo = {
          triggered: true,
          reason: 'Health checks failed',
          previousVersion: 'v0.9.9',
          rollbackTime: 30000,
          status: 'success'
        }
        mockDeployment.status = 'rolled_back'
      }
      
      setDeployment(mockDeployment)
      
      // Adicionar ao histórico
      setDeploymentHistory(prev => [mockDeployment, ...prev.slice(0, 9)])
      
    } catch (err) {
      setError('Failed to start deployment')
    } finally {
      setIsDeploying(false)
      setLoading(false)
    }
  }

  // Cancelar deployment
  const cancelDeployment = () => {
    if (deployment && deployment.status === 'in_progress') {
      setDeployment({
        ...deployment,
        status: 'failed',
        logs: [...deployment.logs, 'Deployment cancelled by user']
      })
      setIsDeploying(false)
    }
  }

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(section)) {
      newExpanded.delete(section)
    } else {
      newExpanded.add(section)
    }
    setExpandedSections(newExpanded)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600'
      case 'failed': return 'text-red-600'
      case 'rolled_back': return 'text-yellow-600'
      case 'in_progress': return 'text-blue-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'failed': return <XCircle className="w-5 h-5 text-red-600" />
      case 'rolled_back': return <RotateCcw className="w-5 h-5 text-yellow-600" />
      case 'in_progress': return <RefreshCw className="w-5 h-5 text-blue-600 animate-spin" />
      default: return <Clock className="w-5 h-5 text-gray-600" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      success: 'success' as const,
      failed: 'destructive' as const,
      rolled_back: 'warning' as const,
      in_progress: 'info' as const
    }
    return <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
      {status.replace('_', ' ').toUpperCase()}
    </Badge>
  }

  const exportDeploymentReport = () => {
    if (!deployment) return
    
    const report = {
      timestamp: new Date(deployment.timestamp).toISOString(),
      environment: deployment.config.environment,
      version: deployment.config.version,
      status: deployment.status,
      duration: deployment.duration,
      metrics: deployment.metrics,
      steps: deployment.steps.map(step => ({
        name: step.name,
        status: step.status,
        duration: step.duration,
        error: step.error
      })),
      healthChecks: deployment.healthChecks,
      rollbackInfo: deployment.rollbackInfo
    }
    
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `deployment-report-${deployment.config.environment}-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Production Deployment</h2>
          <p className="text-sm text-gray-600">
            Deploy and manage production releases
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {/* Environment Selector */}
          <select
            value={selectedEnvironment}
            onChange={(e) => setSelectedEnvironment(e.target.value as 'staging' | 'production')}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            disabled={isDeploying}
          >
            <option value="staging">Staging</option>
            <option value="production">Production</option>
          </select>
          
          {deployment && (
            <Button onClick={exportDeploymentReport} variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export Report
            </Button>
          )}
          
          {isDeploying ? (
            <Button onClick={cancelDeployment} variant="destructive">
              <Square className="w-4 h-4 mr-2" />
              Cancel Deploy
            </Button>
          ) : (
            <Button onClick={startDeployment} className="bg-blue-600 hover:bg-blue-700">
              <Rocket className="w-4 h-4 mr-2" />
              Deploy to {selectedEnvironment}
            </Button>
          )}
        </div>
      </div>

      {/* Deployment Configuration */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Deployment Configuration</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-bold text-blue-600">{defaultConfig.version}</div>
            <div className="text-sm text-gray-600">Version</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-bold text-green-600">{defaultConfig.deploymentStrategy}</div>
            <div className="text-sm text-gray-600">Strategy</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-bold text-purple-600">{defaultConfig.healthChecks.length}</div>
            <div className="text-sm text-gray-600">Health Checks</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-bold text-orange-600">
              {defaultConfig.rollbackConfig.enabled ? 'Enabled' : 'Disabled'}
            </div>
            <div className="text-sm text-gray-600">Auto Rollback</div>
          </div>
        </div>
      </Card>

      {/* Current Deployment */}
      {deployment && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              {getStatusIcon(deployment.status)}
              <div>
                <h3 className="text-lg font-semibold">
                  Current Deployment - {deployment.config.environment}
                </h3>
                <p className="text-sm text-gray-600">
                  Version {deployment.config.version} • 
                  Started: {new Date(deployment.timestamp).toLocaleString()}
                  {deployment.duration > 0 && ` • Duration: ${(deployment.duration / 1000).toFixed(1)}s`}
                </p>
              </div>
            </div>
            {getStatusBadge(deployment.status)}
          </div>

          {/* Deployment Steps */}
          <div className="space-y-2 mb-6">
            <h4 className="font-medium">Deployment Steps</h4>
            <div className="space-y-1">
              {deployment.steps.map((step, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div className="flex items-center space-x-2">
                    {step.status === 'completed' && <CheckCircle className="w-4 h-4 text-green-600" />}
                    {step.status === 'running' && <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />}
                    {step.status === 'failed' && <XCircle className="w-4 h-4 text-red-600" />}
                    {step.status === 'pending' && <Clock className="w-4 h-4 text-gray-400" />}
                    <span className="text-sm">{step.name}</span>
                  </div>
                  <div className="text-xs text-gray-500">
                    {step.duration && `${step.duration}ms`}
                    {step.error && <span className="text-red-600 ml-2">{step.error}</span>}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Deployment Metrics */}
          {deployment.metrics.totalTime > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-lg font-bold text-blue-600">
                  {(deployment.metrics.buildTime / 1000).toFixed(1)}s
                </div>
                <div className="text-sm text-blue-700">Build Time</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-lg font-bold text-green-600">
                  {deployment.metrics.testCoverage.toFixed(1)}%
                </div>
                <div className="text-sm text-green-700">Test Coverage</div>
              </div>
              <div className="text-center p-3 bg-purple-50 rounded-lg">
                <div className="text-lg font-bold text-purple-600">
                  {deployment.metrics.artifactSize.toFixed(1)}MB
                </div>
                <div className="text-sm text-purple-700">Artifact Size</div>
              </div>
              <div className="text-center p-3 bg-orange-50 rounded-lg">
                <div className="text-lg font-bold text-orange-600">
                  {deployment.metrics.securityScore.toFixed(1)}%
                </div>
                <div className="text-sm text-orange-700">Security Score</div>
              </div>
            </div>
          )}

          {/* Health Checks */}
          {deployment.healthChecks.length > 0 && (
            <div className="mb-6">
              <h4 className="font-medium mb-3">Health Checks</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {deployment.healthChecks.map((healthCheck, index) => (
                  <div key={index} className={`p-3 rounded-lg border ${
                    healthCheck.status === 'healthy' ? 'bg-green-50 border-green-200' :
                    healthCheck.status === 'unhealthy' ? 'bg-red-50 border-red-200' :
                    'bg-gray-50 border-gray-200'
                  }`}>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium">{healthCheck.name}</span>
                      {healthCheck.status === 'healthy' && <CheckCircle className="w-4 h-4 text-green-600" />}
                      {healthCheck.status === 'unhealthy' && <XCircle className="w-4 h-4 text-red-600" />}
                      {healthCheck.status === 'unknown' && <Clock className="w-4 h-4 text-gray-400" />}
                    </div>
                    <div className="text-xs text-gray-600">
                      Response: {healthCheck.responseTime.toFixed(0)}ms
                    </div>
                    {healthCheck.error && (
                      <div className="text-xs text-red-600 mt-1">{healthCheck.error}</div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Rollback Info */}
          {deployment.rollbackInfo && (
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <RotateCcw className="w-5 h-5 text-yellow-600" />
                <h4 className="font-medium text-yellow-800">Rollback Executed</h4>
              </div>
              <div className="text-sm text-yellow-700">
                <p><strong>Reason:</strong> {deployment.rollbackInfo.reason}</p>
                <p><strong>Previous Version:</strong> {deployment.rollbackInfo.previousVersion}</p>
                <p><strong>Rollback Time:</strong> {(deployment.rollbackInfo.rollbackTime / 1000).toFixed(1)}s</p>
                <p><strong>Status:</strong> {deployment.rollbackInfo.status.toUpperCase()}</p>
              </div>
            </div>
          )}
        </Card>
      )}

      {/* Deployment History */}
      {deploymentHistory.length > 0 && (
        <Card className="overflow-hidden">
          <div 
            className="p-4 bg-gray-50 cursor-pointer flex items-center justify-between hover:bg-gray-100 transition-colors"
            onClick={() => toggleSection('history')}
          >
            <div className="flex items-center space-x-3">
              <GitBranch className="w-5 h-5 text-gray-600" />
              <div>
                <h3 className="font-semibold">Deployment History</h3>
                <p className="text-sm text-gray-600">
                  {deploymentHistory.length} recent deployments
                </p>
              </div>
            </div>
            {expandedSections.has('history') ? 
              <EyeOff className="w-4 h-4" /> : 
              <Eye className="w-4 h-4" />
            }
          </div>
          
          {expandedSections.has('history') && (
            <div className="p-4 border-t">
              <div className="space-y-3">
                {deploymentHistory.map((deploy, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(deploy.status)}
                      <div>
                        <div className="font-medium">
                          {deploy.config.environment} v{deploy.config.version}
                        </div>
                        <div className="text-sm text-gray-600">
                          {new Date(deploy.timestamp).toLocaleString()} • 
                          {(deploy.duration / 1000).toFixed(1)}s
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(deploy.status)}
                      <span className="text-xs text-gray-500">
                        {deploy.config.deploymentStrategy}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </Card>
      )}
    </div>
  )
}

export default ProductionDeploymentDashboard