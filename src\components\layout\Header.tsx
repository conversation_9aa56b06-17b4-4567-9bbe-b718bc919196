import React, { useState } from 'react'
import { Search, Menu, ChevronLeft, ChevronRight, Bell, Settings } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { ThemeToggle, ThemeStatus } from '@/components/ui/ThemeProvider'
import { Badge } from '@/components/ui/Card'

interface HeaderProps {
  onMenuClick: () => void
  sidebarCollapsed: boolean
  onSidebarToggle: () => void
}

const Header: React.FC<HeaderProps> = ({ 
  onMenuClick, 
  sidebarCollapsed, 
  onSidebarToggle 
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [notificationCount] = useState(3) // Mock notification count

  return (
    <header className="sticky top-0 z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-16 items-center gap-4 px-4 lg:px-6">
        {/* Mobile Menu Button */}
        <Button
          variant="ghost"
          size="icon"
          className="lg:hidden"
          onClick={onMenuClick}
        >
          <Menu className="h-5 w-5" />
        </Button>

        {/* Desktop Sidebar Toggle */}
        <Button
          variant="ghost"
          size="icon"
          className="hidden lg:flex"
          onClick={onSidebarToggle}
        >
          {sidebarCollapsed ? (
            <ChevronRight className="h-5 w-5" />
          ) : (
            <ChevronLeft className="h-5 w-5" />
          )}
        </Button>

        {/* Title */}
        <div className="flex items-center gap-2">
          <h1 className="text-lg font-semibold">
            Crypto Arbitrage
          </h1>
          <Badge variant="outline" size="sm">
            Live
          </Badge>
        </div>

        {/* Search */}
        <div className="flex-1 max-w-md">
          <SearchInput 
            value={searchTerm}
            onChange={setSearchTerm}
            placeholder="Buscar símbolos, exchanges..."
          />
        </div>

        {/* Right Side Controls */}
        <div className="flex items-center gap-2">
          {/* Production Mode Indicator */}
          <ProductionModeIndicator />

          {/* Connection Status */}
          <ConnectionStatus />

          {/* Theme Status (hidden on mobile) */}
          <div className="hidden md:block">
            <ThemeStatus />
          </div>

          {/* Theme Toggle */}
          <ThemeToggle />

          {/* Notifications */}
          <NotificationButton count={notificationCount} />

          {/* Settings */}
          <Button variant="ghost" size="icon">
            <Settings className="h-5 w-5" />
          </Button>
        </div>
      </div>
    </header>
  )
}

// Search Input Component
interface SearchInputProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
}

const SearchInput: React.FC<SearchInputProps> = ({ 
  value, 
  onChange, 
  placeholder = "Buscar..." 
}) => {
  const [focused, setFocused] = useState(false)

  return (
    <div className="relative">
      <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
      <Input
        type="text"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onFocus={() => setFocused(true)}
        onBlur={() => setFocused(false)}
        className="pl-10 pr-4"
        variant="search"
      />
      {focused && value && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-popover border rounded-md shadow-md p-2 z-50">
          <div className="text-sm text-muted-foreground">
            Buscar por "{value}"...
          </div>
        </div>
      )}
    </div>
  )
}

// Connection Status Component
const ConnectionStatus: React.FC = () => {
  // Mock connection status - would be connected to real data
  const exchanges = [
    { name: 'Gate.io', status: 'connected' as const, color: 'bg-exchange-gateio' },
    { name: 'MEXC', status: 'connected' as const, color: 'bg-exchange-mexc' },
    { name: 'Bitget', status: 'connecting' as const, color: 'bg-exchange-bitget' },
  ]

  const allConnected = exchanges.every(ex => ex.status === 'connected')
  const anyConnecting = exchanges.some(ex => ex.status === 'connecting')

  return (
    <div className="flex items-center gap-2">
      <div className={`w-2 h-2 rounded-full ${
        allConnected ? 'bg-green-500' : 
        anyConnecting ? 'bg-yellow-500 animate-pulse' : 
        'bg-red-500'
      }`} />
      <span className="hidden sm:inline text-sm text-muted-foreground">
        {allConnected ? 'Conectado' : anyConnecting ? 'Conectando' : 'Desconectado'}
      </span>
    </div>
  )
}

// Notification Button Component
interface NotificationButtonProps {
  count: number
}

const NotificationButton: React.FC<NotificationButtonProps> = ({ count }) => {
  return (
    <Button variant="ghost" size="icon" className="relative">
      <Bell className="h-5 w-5" />
      {count > 0 && (
        <Badge 
          variant="destructive" 
          size="sm"
          className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
        >
          {count > 9 ? '9+' : count}
        </Badge>
      )}
    </Button>
  )
}

// Production Mode Indicator Component
const ProductionModeIndicator: React.FC = () => {
  const isProduction = import.meta.env.VITE_ENABLE_REAL_APIS === 'true'
  
  return (
    <div className="hidden sm:block">
      {isProduction ? (
        <Badge variant="success" className="text-xs">
          🚀 PRODUÇÃO REAL
        </Badge>
      ) : (
        <Badge variant="warning" className="text-xs">
          🔄 SIMULADO
        </Badge>
      )}
    </div>
  )
}

export default Header