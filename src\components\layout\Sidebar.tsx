import React from 'react'
import { 
  Bar<PERSON>hart3, 
  TrendingUp, 
  Settings, 
  Activity, 
  Wallet, 
  Bell,
  X,
  Home,
  Filter,
  PieChart
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Card'

interface SidebarProps {
  open: boolean
  collapsed: boolean
  onOpenChange: (open: boolean) => void
  onCollapsedChange: (collapsed: boolean) => void
}

const Sidebar: React.FC<SidebarProps> = ({ 
  open, 
  collapsed, 
  onOpenChange, 
  onCollapsedChange 
}) => {
  const navigationItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: Home,
      active: true,
      badge: undefined
    },
    {
      id: 'opportunities',
      label: 'Oportunidades',
      icon: TrendingUp,
      active: false,
      badge: 42
    },
    {
      id: 'charts',
      label: 'Gráficos',
      icon: BarChart3,
      active: false,
      badge: undefined
    },
    {
      id: 'positions',
      label: 'Posições',
      icon: Wallet,
      active: false,
      badge: 3
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: Pie<PERSON>hart,
      active: false,
      badge: undefined
    },
    {
      id: 'monitoring',
      label: 'Monitoramento',
      icon: Activity,
      active: false,
      badge: undefined
    },
    {
      id: 'filters',
      label: 'Filtros',
      icon: Filter,
      active: false,
      badge: undefined
    },
    {
      id: 'alerts',
      label: 'Alertas',
      icon: Bell,
      active: false,
      badge: 2
    },
    {
      id: 'settings',
      label: 'Configurações',
      icon: Settings,
      active: false,
      badge: undefined
    }
  ]

  const exchangeStatus = [
    { name: 'Gate.io', status: 'online' as const, pairs: '3,272', color: 'bg-exchange-gateio' },
    { name: 'MEXC', status: 'online' as const, pairs: '3,216', color: 'bg-exchange-mexc' },
    { name: 'Bitget', status: 'connecting' as const, pairs: '1,312', color: 'bg-exchange-bitget' },
  ]

  return (
    <>
      {/* Desktop Sidebar */}
      <aside className={cn(
        "fixed inset-y-0 left-0 z-50 hidden lg:flex flex-col bg-card border-r transition-all duration-200",
        collapsed ? "w-16" : "w-64"
      )}>
        {/* Logo/Brand */}
        <div className="flex h-16 items-center border-b px-4">
          {collapsed ? (
            <div className="flex h-8 w-8 items-center justify-center rounded bg-primary text-primary-foreground font-bold">
              CA
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <div className="flex h-8 w-8 items-center justify-center rounded bg-primary text-primary-foreground font-bold">
                CA
              </div>
              <span className="font-semibold">Crypto Arbitrage</span>
            </div>
          )}
        </div>

        {/* Navigation */}
        <nav className="flex-1 space-y-1 p-2">
          {navigationItems.map((item) => (
            <SidebarItem
              key={item.id}
              item={item}
              collapsed={collapsed}
            />
          ))}
        </nav>

        {/* Exchange Status */}
        {!collapsed && (
          <div className="border-t p-4">
            <h4 className="text-sm font-medium text-muted-foreground mb-3">
              Status das Exchanges
            </h4>
            <div className="space-y-2">
              {exchangeStatus.map((exchange) => (
                <ExchangeStatusItem
                  key={exchange.name}
                  exchange={exchange}
                />
              ))}
            </div>
          </div>
        )}
      </aside>

      {/* Mobile Sidebar */}
      <aside className={cn(
        "fixed inset-y-0 left-0 z-50 w-64 bg-card border-r transition-transform duration-200 lg:hidden",
        open ? "translate-x-0" : "-translate-x-full"
      )}>
        {/* Mobile Header */}
        <div className="flex h-16 items-center justify-between border-b px-4">
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded bg-primary text-primary-foreground font-bold">
              CA
            </div>
            <span className="font-semibold">Crypto Arbitrage</span>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onOpenChange(false)}
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Mobile Navigation */}
        <nav className="flex-1 space-y-1 p-2">
          {navigationItems.map((item) => (
            <SidebarItem
              key={item.id}
              item={item}
              collapsed={false}
              onClick={() => onOpenChange(false)}
            />
          ))}
        </nav>

        {/* Mobile Exchange Status */}
        <div className="border-t p-4">
          <h4 className="text-sm font-medium text-muted-foreground mb-3">
            Status das Exchanges
          </h4>
          <div className="space-y-2">
            {exchangeStatus.map((exchange) => (
              <ExchangeStatusItem
                key={exchange.name}
                exchange={exchange}
              />
            ))}
          </div>
        </div>
      </aside>
    </>
  )
}

// Sidebar Item Component
interface SidebarItemProps {
  item: {
    id: string
    label: string
    icon: React.ComponentType<{ className?: string }>
    active: boolean
    badge?: number
  }
  collapsed: boolean
  onClick?: () => void
}

const SidebarItem: React.FC<SidebarItemProps> = ({ item, collapsed, onClick }) => {
  const { icon: Icon, label, active, badge } = item

  return (
    <Button
      variant={active ? "secondary" : "ghost"}
      className={cn(
        "w-full justify-start gap-3 h-10",
        collapsed && "px-2 justify-center"
      )}
      onClick={onClick}
    >
      <Icon className="h-5 w-5 shrink-0" />
      {!collapsed && (
        <>
          <span className="flex-1 text-left">{label}</span>
          {badge && badge > 0 && (
            <Badge variant="secondary" size="sm">
              {badge > 99 ? '99+' : badge}
            </Badge>
          )}
        </>
      )}
    </Button>
  )
}

// Exchange Status Item Component
interface ExchangeStatusItemProps {
  exchange: {
    name: string
    status: 'online' | 'offline' | 'connecting'
    pairs: string
    color: string
  }
}

const ExchangeStatusItem: React.FC<ExchangeStatusItemProps> = ({ exchange }) => {
  const statusColors = {
    online: 'bg-green-500',
    offline: 'bg-red-500',
    connecting: 'bg-yellow-500 animate-pulse'
  }

  return (
    <div className="flex items-center justify-between text-sm">
      <div className="flex items-center gap-2">
        <div className={`w-2 h-2 rounded-full ${exchange.color}`} />
        <span className="font-medium">{exchange.name}</span>
      </div>
      <div className="flex items-center gap-2">
        <span className="text-muted-foreground">{exchange.pairs}</span>
        <div className={`w-2 h-2 rounded-full ${statusColors[exchange.status]}`} />
      </div>
    </div>
  )
}

export default Sidebar