// APIMonitoringDashboard - Dashboard de Monitoramento das APIs

import { useState, useEffect } from 'react'
import { Activity, AlertTriangle, CheckCircle, Clock, Zap, TrendingUp, TrendingDown, Wifi, WifiOff } from 'lucide-react'
import { Card } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'
import { ExchangeAPI } from '@/services/ExchangeAPI'
import type { APIHealthStatus, APIAlert } from '@/services/monitoring/APIMonitor'

interface APIMonitoringDashboardProps {
  className?: string
}

export function APIMonitoringDashboard({ className = '' }: APIMonitoringDashboardProps) {
  const [healthStatuses, setHealthStatuses] = useState<APIHealthStatus[]>([])
  const [apiAlerts, setApiAlerts] = useState<APIAlert[]>([])
  const [overallStats, setOverallStats] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)

  const exchangeAPI = ExchangeAPI.getInstance()

  // Carregar dados de monitoramento
  const loadMonitoringData = async () => {
    setIsLoading(true)
    
    try {
      // Obter status de saúde de todas as exchanges
      const healthData = exchangeAPI.getAPIHealthStatus() as APIHealthStatus[]
      
      // Obter alertas ativos
      const alerts = exchangeAPI.getAPIAlerts() as APIAlert[]
      
      // Obter estatísticas gerais
      const stats = exchangeAPI.getAPIMetrics()
      
      setHealthStatuses(healthData)
      setApiAlerts(alerts)
      setOverallStats(stats)
      setLastUpdate(new Date())
      
    } catch (error) {
      console.error('Error loading monitoring data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Carregar dados na montagem e configurar atualização automática
  useEffect(() => {
    loadMonitoringData()
    
    const interval = setInterval(loadMonitoringData, 15000) // Atualizar a cada 15s
    return () => clearInterval(interval)
  }, [])

  // Resolver alerta
  const resolveAlert = async (alertId: string) => {
    try {
      const resolved = exchangeAPI.resolveAPIAlert(alertId)
      if (resolved) {
        await loadMonitoringData() // Recarregar dados
      }
    } catch (error) {
      console.error('Error resolving alert:', error)
    }
  }

  // Obter cor do status de saúde
  const getHealthColor = (isHealthy: boolean) => {
    return isHealthy ? 'text-green-600' : 'text-red-600'
  }

  // Obter ícone do status de saúde
  const getHealthIcon = (isHealthy: boolean) => {
    return isHealthy ? <CheckCircle className="h-5 w-5 text-green-600" /> : <AlertTriangle className="h-5 w-5 text-red-600" />
  }

  // Obter badge de severidade
  const getSeverityBadge = (severity: 'low' | 'medium' | 'high' | 'critical') => {
    const variants = {
      low: 'info' as const,
      medium: 'warning' as const,
      high: 'destructive' as const,
      critical: 'destructive' as const
    }
    return <Badge variant={variants[severity]}>{severity.toUpperCase()}</Badge>
  }

  // Formatar tempo de resposta
  const formatResponseTime = (ms: number) => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`
    return `${(ms / 1000).toFixed(1)}s`
  }

  // Formatar porcentagem
  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`
  }

  // Formatar timestamp
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('pt-BR')
  }

  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <Card className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="space-y-3">
              {[1, 2, 3].map(i => (
                <div key={i} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header com estatísticas gerais */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold">Monitoramento de APIs - Tempo Real</h3>
          </div>
          <Button
            onClick={loadMonitoringData}
            size="sm"
            variant="outline"
            disabled={isLoading}
          >
            {isLoading ? 'Atualizando...' : 'Atualizar'}
          </Button>
        </div>

        {/* Estatísticas gerais */}
        {overallStats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{overallStats.totalRequests?.toLocaleString() || 0}</div>
              <div className="text-sm text-gray-500">Total Requests</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${overallStats.successRate >= 0.95 ? 'text-green-600' : 'text-red-600'}`}>
                {formatPercentage(overallStats.successRate || 0)}
              </div>
              <div className="text-sm text-gray-500">Success Rate</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${overallStats.avgResponseTime <= 2000 ? 'text-green-600' : 'text-yellow-600'}`}>
                {formatResponseTime(overallStats.avgResponseTime || 0)}
              </div>
              <div className="text-sm text-gray-500">Avg Response</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{overallStats.activeAlerts || 0}</div>
              <div className="text-sm text-gray-500">Active Alerts</div>
            </div>
          </div>
        )}

        {lastUpdate && (
          <div className="text-xs text-gray-500 text-center">
            Última atualização: {lastUpdate.toLocaleString('pt-BR')}
          </div>
        )}
      </Card>

      {/* Status de saúde por exchange */}
      <div className="grid gap-6">
        {healthStatuses.map(status => {
          const exchangeNames = {
            gateio: 'Gate.io',
            mexc: 'MEXC',
            bitget: 'Bitget'
          }

          return (
            <Card key={status.exchange} className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  {getHealthIcon(status.isHealthy)}
                  <h4 className="text-lg font-semibold">
                    {exchangeNames[status.exchange as keyof typeof exchangeNames] || status.exchange.toUpperCase()}
                  </h4>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={status.isHealthy ? 'success' : 'destructive'}>
                    {status.isHealthy ? 'Healthy' : 'Unhealthy'}
                  </Badge>
                  <div className={`flex items-center gap-1 ${status.isHealthy ? 'text-green-600' : 'text-red-600'}`}>
                    {status.isHealthy ? <Wifi className="h-4 w-4" /> : <WifiOff className="h-4 w-4" />}
                    <span className="text-sm">{formatResponseTime(status.responseTime)}</span>
                  </div>
                </div>
              </div>

              {/* Métricas principais */}
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-lg font-semibold text-gray-900">{status.totalRequests.toLocaleString()}</div>
                  <div className="text-xs text-gray-500">Total Requests</div>
                </div>
                <div className="text-center">
                  <div className={`text-lg font-semibold ${status.successRate >= 0.95 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatPercentage(status.successRate)}
                  </div>
                  <div className="text-xs text-gray-500">Success Rate</div>
                </div>
                <div className="text-center">
                  <div className={`text-lg font-semibold ${status.avgResponseTime <= 2000 ? 'text-green-600' : 'text-yellow-600'}`}>
                    {formatResponseTime(status.avgResponseTime)}
                  </div>
                  <div className="text-xs text-gray-500">Avg Response</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-red-600">{status.failedRequests}</div>
                  <div className="text-xs text-gray-500">Failed</div>
                </div>
                <div className="text-center">
                  <div className={`text-lg font-semibold ${status.uptime >= 99 ? 'text-green-600' : 'text-yellow-600'}`}>
                    {status.uptime.toFixed(1)}%
                  </div>
                  <div className="text-xs text-gray-500">Uptime</div>
                </div>
              </div>

              {/* Issues detectados */}
              {status.issues.length > 0 && (
                <div className="space-y-2">
                  <h5 className="font-medium text-gray-900 flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    Issues Detectados ({status.issues.length})
                  </h5>
                  <div className="space-y-1">
                    {status.issues.map((issue, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm">
                        <div className="flex items-center gap-2">
                          <AlertTriangle className={`h-4 w-4 ${
                            issue.severity === 'critical' ? 'text-red-600' :
                            issue.severity === 'high' ? 'text-red-500' :
                            issue.severity === 'medium' ? 'text-yellow-600' : 'text-blue-600'
                          }`} />
                          <span>{issue.message}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-gray-500">({issue.count})</span>
                          {getSeverityBadge(issue.severity)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Timestamp */}
              <div className="text-xs text-gray-500 mt-4">
                Última verificação: {formatTime(status.lastCheck)}
              </div>
            </Card>
          )
        })}
      </div>

      {/* Alertas ativos */}
      {apiAlerts.length > 0 && (
        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <h4 className="text-lg font-semibold">Alertas Ativos ({apiAlerts.length})</h4>
          </div>
          
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {apiAlerts.map((alert) => (
              <div key={alert.id} className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-3">
                  <AlertTriangle className={`h-4 w-4 ${
                    alert.severity === 'critical' ? 'text-red-600' :
                    alert.severity === 'high' ? 'text-red-500' :
                    alert.severity === 'medium' ? 'text-yellow-600' : 'text-blue-600'
                  }`} />
                  <div>
                    <div className="font-medium text-sm">{alert.exchange.toUpperCase()}</div>
                    <div className="text-sm text-gray-600">{alert.message}</div>
                    <div className="text-xs text-gray-500">{formatTime(alert.timestamp)}</div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getSeverityBadge(alert.severity)}
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => resolveAlert(alert.id)}
                    className="text-xs"
                  >
                    Resolver
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Resumo de performance */}
      {overallStats && (
        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <TrendingUp className="h-5 w-5 text-green-600" />
            <h4 className="text-lg font-semibold">Resumo de Performance</h4>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded">
              <div className="text-2xl font-bold text-green-600">{overallStats.healthyExchanges}/{overallStats.totalExchanges}</div>
              <div className="text-sm text-green-700">Exchanges Saudáveis</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded">
              <div className="text-2xl font-bold text-blue-600">{formatResponseTime(overallStats.avgResponseTime || 0)}</div>
              <div className="text-sm text-blue-700">Tempo Médio de Resposta</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded">
              <div className="text-2xl font-bold text-purple-600">{formatPercentage(overallStats.successRate || 0)}</div>
              <div className="text-sm text-purple-700">Taxa de Sucesso Geral</div>
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}

export default APIMonitoringDashboard