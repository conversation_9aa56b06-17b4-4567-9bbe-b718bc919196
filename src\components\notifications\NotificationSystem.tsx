// NotificationSystem - Sistema Completo de Notificações

import { useState, useEffect, useCallback, useRef } from 'react'
import { X, Bell, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { Switch } from '@/components/ui/Switch'
import { <PERSON>lider } from '@/components/ui/Slider'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select'
import { AlertSystem } from '@/services/AlertSystem'
import type { ArbitrageOpportunity } from '@/types/arbitrage'

export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  timestamp: number
  duration?: number
  persistent?: boolean
  data?: any
  actions?: NotificationAction[]
}

export interface NotificationAction {
  label: string
  action: () => void
  variant?: 'primary' | 'secondary' | 'destructive'
}

export interface NotificationSettings {
  enabled: boolean
  sound: boolean
  volume: number
  position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
  duration: number
  maxNotifications: number
  highProfitThreshold: number
  positionAlerts: boolean
  exchangeStatus: boolean
  browserNotifications: boolean
}

const DEFAULT_SETTINGS: NotificationSettings = {
  enabled: true,
  sound: true,
  volume: 0.5,
  position: 'top-right',
  duration: 5000,
  maxNotifications: 5,
  highProfitThreshold: 1.0,
  positionAlerts: true,
  exchangeStatus: true,
  browserNotifications: false
}

interface NotificationSystemProps {
  className?: string
}

function NotificationSystem({ className = '' }: NotificationSystemProps) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [settings, setSettings] = useState<NotificationSettings>(() => {
    const saved = localStorage.getItem('notification-settings')
    return saved ? { ...DEFAULT_SETTINGS, ...JSON.parse(saved) } : DEFAULT_SETTINGS
  })
  const [showSettings, setShowSettings] = useState(false)
  const [permissionStatus, setPermissionStatus] = useState<NotificationPermission>('default')
  
  const alertSystem = AlertSystem.getInstance()
  const audioContextRef = useRef<AudioContext | null>(null)
  const notificationSoundsRef = useRef<{ [key: string]: AudioBuffer }>({})
  
  // Salvar configurações
  useEffect(() => {
    localStorage.setItem('notification-settings', JSON.stringify(settings))
  }, [settings])
  
  // Verificar permissão de notificações do navegador
  useEffect(() => {
    if ('Notification' in window) {
      setPermissionStatus(Notification.permission)
    }
  }, [])
  
  // Tocar som
  const playSound = useCallback((type: Notification['type']) => {
    if (!settings.sound) return
    
    // Som simples usando Web Audio API
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()
      
      const frequencies = {
        success: 800,
        error: 400,
        warning: 600,
        info: 500
      }
      
      oscillator.frequency.setValueAtTime(frequencies[type], audioContext.currentTime)
      oscillator.type = 'sine'
      
      gainNode.gain.setValueAtTime(settings.volume * 0.1, audioContext.currentTime)
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3)
      
      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)
      
      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.3)
    } catch (error) {
      console.warn('Failed to play notification sound:', error)
    }
  }, [settings.sound, settings.volume])
  
  // Mostrar notificação do navegador
  const showBrowserNotification = useCallback((notification: Notification) => {
    if (!settings.browserNotifications || permissionStatus !== 'granted') {
      return
    }
    
    try {
      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: notification.id,
        requireInteraction: notification.persistent
      })
      
      browserNotification.onclick = () => {
        window.focus()
        browserNotification.close()
      }
      
      if (!notification.persistent) {
        setTimeout(() => {
          browserNotification.close()
        }, notification.duration || settings.duration)
      }
    } catch (error) {
      console.warn('Failed to show browser notification:', error)
    }
  }, [settings.browserNotifications, settings.duration, permissionStatus])
  
  // Adicionar notificação
  const addNotification = useCallback((notification: Omit<Notification, 'id' | 'timestamp'>) => {
    if (!settings.enabled) return
    
    const newNotification: Notification = {
      ...notification,
      id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      duration: notification.duration || settings.duration
    }
    
    setNotifications(prev => {
      const updated = [newNotification, ...prev].slice(0, settings.maxNotifications)
      return updated
    })
    
    // Tocar som
    playSound(newNotification.type)
    
    // Mostrar notificação do navegador
    showBrowserNotification(newNotification)
    
    // Auto-remover se não for persistente
    if (!newNotification.persistent) {
      setTimeout(() => {
        removeNotification(newNotification.id)
      }, newNotification.duration)
    }
  }, [settings, playSound, showBrowserNotification])
  
  // Remover notificação
  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }, [])
  
  // Limpar todas as notificações
  const clearAll = useCallback(() => {
    setNotifications([])
  }, [])
  
  // Solicitar permissão para notificações do navegador
  const requestBrowserPermission = useCallback(async () => {
    if ('Notification' in window && Notification.permission === 'default') {
      const permission = await Notification.requestPermission()
      setPermissionStatus(permission)
      
      if (permission === 'granted') {
        addNotification({
          type: 'success',
          title: 'Notificações Habilitadas',
          message: 'Você receberá notificações do navegador para oportunidades importantes'
        })
      }
    }
  }, [addNotification])
  
  // Integração com AlertSystem
  useEffect(() => {
    const handleAlert = (level: string, message: string, data?: any) => {
      if (!settings.enabled) return
      
      let type: Notification['type'] = 'info'
      let title = 'Alerta'
      
      switch (level) {
        case 'critical':
          type = 'error'
          title = 'Alerta Crítico'
          break
        case 'high':
          type = 'warning'
          title = 'Alerta Importante'
          break
        case 'medium':
          type = 'info'
          title = 'Informação'
          break
        case 'low':
          type = 'success'
          title = 'Notificação'
          break
      }
      
      const actions: NotificationAction[] = []
      
      // Adicionar ações específicas baseadas no tipo de dados
      if (data?.symbol) {
        actions.push({
          label: 'Ver Detalhes',
          action: () => {
            console.log('Ver detalhes de:', data.symbol)
          }
        })
      }
      
      addNotification({
        type,
        title,
        message,
        data,
        actions: actions.length > 0 ? actions : undefined,
        persistent: level === 'critical'
      })
    }
    
    // Registrar listener no AlertSystem
    alertSystem.onAlert = handleAlert
    
    return () => {
      alertSystem.onAlert = undefined
    }
  }, [alertSystem, addNotification, settings.enabled])
  
  // Ícones para tipos de notificação
  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success': return CheckCircle
      case 'error': return AlertCircle
      case 'warning': return AlertTriangle
      case 'info': return Info
      default: return Info
    }
  }
  
  // Cores para tipos de notificação
  const getNotificationColors = (type: Notification['type']) => {
    switch (type) {
      case 'success': return 'border-green-200 bg-green-50 text-green-800'
      case 'error': return 'border-red-200 bg-red-50 text-red-800'
      case 'warning': return 'border-yellow-200 bg-yellow-50 text-yellow-800'
      case 'info': return 'border-blue-200 bg-blue-50 text-blue-800'
      default: return 'border-gray-200 bg-gray-50 text-gray-800'
    }
  }
  
  // Posicionamento das notificações
  const getPositionClasses = () => {
    switch (settings.position) {
      case 'top-left': return 'top-4 left-4'
      case 'top-right': return 'top-4 right-4'
      case 'bottom-left': return 'bottom-4 left-4'
      case 'bottom-right': return 'bottom-4 right-4'
      default: return 'top-4 right-4'
    }
  }
  
  return (
    <>
      {/* Container de Notificações */}
      <div className={`fixed ${getPositionClasses()} z-50 space-y-2 max-w-sm w-full ${className}`}>
        {notifications.map((notification) => {
          const Icon = getNotificationIcon(notification.type)
          const colors = getNotificationColors(notification.type)
          
          return (
            <Card
              key={notification.id}
              className={`p-4 border ${colors} animate-in slide-in-from-right duration-300`}
            >
              <div className="flex items-start gap-3">
                <Icon className="h-5 w-5 flex-shrink-0 mt-0.5" />
                
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-sm">{notification.title}</h4>
                  <p className="text-sm opacity-90 mt-1">{notification.message}</p>
                  
                  {notification.actions && notification.actions.length > 0 && (
                    <div className="flex gap-2 mt-3">
                      {notification.actions.map((action, index) => (
                        <Button
                          key={index}
                          size="sm"
                          variant={action.variant || 'secondary'}
                          onClick={() => {
                            action.action()
                            if (!notification.persistent) {
                              removeNotification(notification.id)
                            }
                          }}
                          className="text-xs"
                        >
                          {action.label}
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
                
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => removeNotification(notification.id)}
                  className="p-1 h-auto text-current opacity-70 hover:opacity-100"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </Card>
          )
        })}
      </div>
      
      {/* Modal de Configurações */}
      {showSettings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-md max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold">Configurações de Notificação</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowSettings(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="space-y-6">
                {/* Configurações Gerais */}
                <div>
                  <h4 className="font-medium mb-3">Geral</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <label className="text-sm font-medium">Notificações Habilitadas</label>
                      <Switch
                        checked={settings.enabled}
                        onCheckedChange={(enabled) => setSettings(prev => ({ ...prev, enabled }))}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <label className="text-sm font-medium">Som</label>
                      <Switch
                        checked={settings.sound}
                        onCheckedChange={(sound) => setSettings(prev => ({ ...prev, sound }))}
                      />
                    </div>
                    
                    {settings.sound && (
                      <div>
                        <label className="text-sm font-medium block mb-2">Volume: {Math.round(settings.volume * 100)}%</label>
                        <Slider
                          value={[settings.volume]}
                          onValueChange={([volume]) => setSettings(prev => ({ ...prev, volume }))}
                          max={1}
                          step={0.1}
                          className="w-full"
                        />
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Posição */}
                <div>
                  <label className="text-sm font-medium block mb-2">Posição</label>
                  <Select
                    value={settings.position}
                    onValueChange={(position: NotificationSettings['position']) => 
                      setSettings(prev => ({ ...prev, position }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="top-right">Superior Direita</SelectItem>
                      <SelectItem value="top-left">Superior Esquerda</SelectItem>
                      <SelectItem value="bottom-right">Inferior Direita</SelectItem>
                      <SelectItem value="bottom-left">Inferior Esquerda</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Duração */}
                <div>
                  <label className="text-sm font-medium block mb-2">Duração: {settings.duration / 1000}s</label>
                  <Slider
                    value={[settings.duration]}
                    onValueChange={([duration]) => setSettings(prev => ({ ...prev, duration }))}
                    min={1000}
                    max={10000}
                    step={500}
                    className="w-full"
                  />
                </div>
                
                {/* Threshold de Alta Rentabilidade */}
                <div>
                  <label className="text-sm font-medium block mb-2">Threshold Alta Rentabilidade: {settings.highProfitThreshold}%</label>
                  <Slider
                    value={[settings.highProfitThreshold]}
                    onValueChange={([highProfitThreshold]) => setSettings(prev => ({ ...prev, highProfitThreshold }))}
                    min={0.1}
                    max={5.0}
                    step={0.1}
                    className="w-full"
                  />
                </div>
                
                {/* Notificações do Navegador */}
                <div>
                  <h4 className="font-medium mb-3">Notificações do Navegador</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <label className="text-sm font-medium">Habilitadas</label>
                      <div className="flex items-center gap-2">
                        {permissionStatus !== 'granted' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={requestBrowserPermission}
                            className="text-xs"
                          >
                            Permitir
                          </Button>
                        )}
                        <Switch
                          checked={settings.browserNotifications && permissionStatus === 'granted'}
                          onCheckedChange={(browserNotifications) => {
                            if (browserNotifications && permissionStatus !== 'granted') {
                              requestBrowserPermission()
                            } else {
                              setSettings(prev => ({ ...prev, browserNotifications }))
                            }
                          }}
                          disabled={permissionStatus !== 'granted'}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Ações */}
                <div className="flex gap-3 pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => {
                      addNotification({
                        type: 'info',
                        title: 'Teste de Notificação',
                        message: 'Esta é uma notificação de teste para verificar suas configurações'
                      })
                    }}
                    className="flex-1"
                  >
                    Testar
                  </Button>
                  <Button
                    variant="outline"
                    onClick={clearAll}
                    className="flex-1"
                  >
                    Limpar Todas
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </div>
      )}
    </>
  )
}

// Hook para usar o sistema de notificações
export function useNotifications() {
  const [system, setSystem] = useState<NotificationSystem | null>(null)
  
  useEffect(() => {
    // Aqui você poderia implementar um contexto global para o sistema de notificações
    // Por simplicidade, vamos usar uma instância local
  }, [])
  
  return {
    // Funções que seriam expostas pelo sistema
    notify: (notification: Omit<Notification, 'id' | 'timestamp'>) => {
      console.log('Notification:', notification)
    }
  }
}

// Componente para botão de notificações no header
export function NotificationButton({ 
  onClick, 
  notificationCount = 0 
}: { 
  onClick: () => void
  notificationCount?: number 
}) {
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={onClick}
      className="relative"
    >
      <Bell className="h-4 w-4" />
      {notificationCount > 0 && (
        <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
          {notificationCount > 9 ? '9+' : notificationCount}
        </span>
      )}
    </Button>
  )
}

export { NotificationSystem }
export default NotificationSystem