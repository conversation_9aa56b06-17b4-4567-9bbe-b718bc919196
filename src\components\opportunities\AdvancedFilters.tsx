// AdvancedFilters - Sistema de Filtros Avançados para Oportunidades

import { useState } from 'react'
import { ChevronDown, ChevronUp, Filter, X, RotateCcw } from 'lucide-react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select'
import { Switch } from '@/components/ui/Switch'
import { Slider } from '@/components/ui/Slider'

export interface FilterState {
  // Filtros básicos
  searchTerm: string
  spotExchange: string
  futuresExchange: string
  type: string
  profitability: string
  
  // Filtros avançados
  spreadRange: [number, number]
  volumeRange: [number, number]
  priceRange: [number, number]
  
  // Configurações de tempo
  timeframe: string
  autoRefresh: boolean
  realTimeUpdates: boolean
  notifications: boolean
  
  // Configurações de exibição
  showOnlyHighVolume: boolean
  showOnlyRecentData: boolean
  hideStaleData: boolean
}

interface AdvancedFiltersProps {
  filters: FilterState
  onFiltersChange: (filters: FilterState) => void
  exchanges: {
    spot: string[]
    futures: string[]
  }
  isCollapsed?: boolean
  onToggleCollapse?: () => void
  className?: string
}

const DEFAULT_FILTERS: FilterState = {
  searchTerm: '',
  spotExchange: 'all',
  futuresExchange: 'all',
  type: 'all',
  profitability: 'all',
  spreadRange: [0, 10],
  volumeRange: [0, 1000000],
  priceRange: [0, 100000],
  timeframe: '1h',
  autoRefresh: true,
  realTimeUpdates: true,
  notifications: false,
  showOnlyHighVolume: false,
  showOnlyRecentData: true,
  hideStaleData: true
}

export function AdvancedFilters({
  filters,
  onFiltersChange,
  exchanges,
  isCollapsed = false,
  onToggleCollapse,
  className = ''
}: AdvancedFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)

  const updateFilter = (key: keyof FilterState, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const resetFilters = () => {
    onFiltersChange(DEFAULT_FILTERS)
  }

  const getActiveFiltersCount = () => {
    let count = 0
    
    if (filters.searchTerm) count++
    if (filters.spotExchange !== 'all') count++
    if (filters.futuresExchange !== 'all') count++
    if (filters.type !== 'all') count++
    if (filters.profitability !== 'all') count++
    if (filters.spreadRange[0] > 0 || filters.spreadRange[1] < 10) count++
    if (filters.volumeRange[0] > 0 || filters.volumeRange[1] < 1000000) count++
    if (filters.priceRange[0] > 0 || filters.priceRange[1] < 100000) count++
    if (filters.showOnlyHighVolume) count++
    if (!filters.showOnlyRecentData) count++
    if (!filters.hideStaleData) count++
    
    return count
  }

  const activeFiltersCount = getActiveFiltersCount()

  if (isCollapsed) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Button
          variant="outline"
          onClick={onToggleCollapse}
          className="flex items-center gap-2"
        >
          <Filter className="h-4 w-4" />
          Filtros
          {activeFiltersCount > 0 && (
            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
              {activeFiltersCount}
            </span>
          )}
        </Button>
      </div>
    )
  }

  return (
    <Card className={`p-6 space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            Filtros Avançados
          </h3>
          {activeFiltersCount > 0 && (
            <span className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">
              {activeFiltersCount} ativo{activeFiltersCount !== 1 ? 's' : ''}
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={resetFilters}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Limpar
          </Button>
          
          {onToggleCollapse && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleCollapse}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Filtros Básicos */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-gray-700 border-b pb-2">
          Filtros Básicos
        </h4>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Busca */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Buscar Símbolo
            </label>
            <Input
              placeholder="BTC, ETH, ADA..."
              value={filters.searchTerm}
              onChange={(e) => updateFilter('searchTerm', e.target.value)}
            />
          </div>

          {/* Spot Exchange */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Spot Exchange
            </label>
            <Select 
              value={filters.spotExchange} 
              onValueChange={(value) => updateFilter('spotExchange', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas</SelectItem>
                {exchanges.spot.map(exchange => (
                  <SelectItem key={exchange} value={exchange}>
                    {exchange.toUpperCase()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Futures Exchange */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Futures Exchange
            </label>
            <Select 
              value={filters.futuresExchange} 
              onValueChange={(value) => updateFilter('futuresExchange', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas</SelectItem>
                {exchanges.futures.map(exchange => (
                  <SelectItem key={exchange} value={exchange}>
                    {exchange.toUpperCase()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Tipo */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Tipo de Arbitragem
            </label>
            <Select 
              value={filters.type} 
              onValueChange={(value) => updateFilter('type', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                <SelectItem value="spot-futures-cross">Spot-Futures</SelectItem>
                <SelectItem value="futures-futures-cross">Futures-Futures</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Rentabilidade */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Rentabilidade
            </label>
            <Select 
              value={filters.profitability} 
              onValueChange={(value) => updateFilter('profitability', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas</SelectItem>
                <SelectItem value="high">Alta</SelectItem>
                <SelectItem value="medium">Média</SelectItem>
                <SelectItem value="low">Baixa</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Timeframe */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Timeframe
            </label>
            <Select 
              value={filters.timeframe} 
              onValueChange={(value) => updateFilter('timeframe', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1m">1 Minuto</SelectItem>
                <SelectItem value="5m">5 Minutos</SelectItem>
                <SelectItem value="15m">15 Minutos</SelectItem>
                <SelectItem value="1h">1 Hora</SelectItem>
                <SelectItem value="4h">4 Horas</SelectItem>
                <SelectItem value="1d">1 Dia</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Toggle Filtros Avançados */}
      <div className="border-t pt-4">
        <Button
          variant="ghost"
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="flex items-center gap-2 w-full justify-center"
        >
          {showAdvanced ? (
            <>
              <ChevronUp className="h-4 w-4" />
              Ocultar Filtros Avançados
            </>
          ) : (
            <>
              <ChevronDown className="h-4 w-4" />
              Mostrar Filtros Avançados
            </>
          )}
        </Button>
      </div>

      {/* Filtros Avançados */}
      {showAdvanced && (
        <div className="space-y-6 border-t pt-6">
          <h4 className="text-sm font-medium text-gray-700">
            Filtros Avançados
          </h4>

          {/* Ranges com Sliders */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Spread Range */}
            <div>
              <label className="text-sm font-medium text-gray-700 mb-3 block">
                Spread Range: {filters.spreadRange[0]}% - {filters.spreadRange[1]}%
              </label>
              <Slider
                value={filters.spreadRange}
                onValueChange={(value) => updateFilter('spreadRange', value)}
                min={0}
                max={10}
                step={0.1}
                className="w-full"
              />
            </div>

            {/* Volume Range */}
            <div>
              <label className="text-sm font-medium text-gray-700 mb-3 block">
                Volume Range: ${filters.volumeRange[0].toLocaleString()} - ${filters.volumeRange[1].toLocaleString()}
              </label>
              <Slider
                value={filters.volumeRange}
                onValueChange={(value) => updateFilter('volumeRange', value)}
                min={0}
                max={1000000}
                step={10000}
                className="w-full"
              />
            </div>

            {/* Price Range */}
            <div>
              <label className="text-sm font-medium text-gray-700 mb-3 block">
                Price Range: ${filters.priceRange[0].toLocaleString()} - ${filters.priceRange[1].toLocaleString()}
              </label>
              <Slider
                value={filters.priceRange}
                onValueChange={(value) => updateFilter('priceRange', value)}
                min={0}
                max={100000}
                step={1000}
                className="w-full"
              />
            </div>
          </div>

          {/* Switches de Configuração */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Auto Refresh */}
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Auto Refresh
                </label>
                <p className="text-xs text-gray-500">
                  Atualização automática
                </p>
              </div>
              <Switch
                checked={filters.autoRefresh}
                onCheckedChange={(checked) => updateFilter('autoRefresh', checked)}
              />
            </div>

            {/* Real Time Updates */}
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Tempo Real
                </label>
                <p className="text-xs text-gray-500">
                  Atualizações em tempo real
                </p>
              </div>
              <Switch
                checked={filters.realTimeUpdates}
                onCheckedChange={(checked) => updateFilter('realTimeUpdates', checked)}
              />
            </div>

            {/* Notifications */}
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Notificações
                </label>
                <p className="text-xs text-gray-500">
                  Alertas de oportunidades
                </p>
              </div>
              <Switch
                checked={filters.notifications}
                onCheckedChange={(checked) => updateFilter('notifications', checked)}
              />
            </div>

            {/* High Volume Only */}
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Alto Volume
                </label>
                <p className="text-xs text-gray-500">
                  Apenas alta liquidez
                </p>
              </div>
              <Switch
                checked={filters.showOnlyHighVolume}
                onCheckedChange={(checked) => updateFilter('showOnlyHighVolume', checked)}
              />
            </div>
          </div>

          {/* Configurações de Dados */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {/* Recent Data Only */}
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Apenas Dados Recentes
                </label>
                <p className="text-xs text-gray-500">
                  Filtrar dados antigos (&gt; 1 min)
                </p>
              </div>
              <Switch
                checked={filters.showOnlyRecentData}
                onCheckedChange={(checked) => updateFilter('showOnlyRecentData', checked)}
              />
            </div>

            {/* Hide Stale Data */}
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Ocultar Dados Obsoletos
                </label>
                <p className="text-xs text-gray-500">
                  Remover dados com problemas
                </p>
              </div>
              <Switch
                checked={filters.hideStaleData}
                onCheckedChange={(checked) => updateFilter('hideStaleData', checked)}
              />
            </div>
          </div>
        </div>
      )}
    </Card>
  )
}

export default AdvancedFilters