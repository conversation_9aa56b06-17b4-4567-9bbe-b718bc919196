// OpportunityCard - Card Individual para Oportunidades de Arbitragem

import { useState, memo, useCallback } from 'react'
import { ExternalLink, Copy, TrendingUp, Activity, Clock, AlertCircle, BarChart3 } from 'lucide-react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { ChartModal } from '../charts/ChartModal'
import type { ArbitrageOpportunity } from '@/types/arbitrage'

interface OpportunityCardProps {
  opportunity: ArbitrageOpportunity
  onSelect?: (opportunity: ArbitrageOpportunity) => void
  isSelected?: boolean
}

export function OpportunityCard({ 
  opportunity, 
  onSelect, 
  isSelected = false 
}: OpportunityCardProps) {
  const [copied, setCopied] = useState(false)
  const [showChart, setShowChart] = useState(false)

  // Só exibir oportunidades com spread positivo
  if (opportunity.spreadPercentage <= 0 || opportunity.profitability === 'NONE') {
    return null
  }

  const displaySpreadPercentage = opportunity.spreadPercentage
  const displayNetSpread = opportunity.netSpread
  
  // Determinar cor baseada na rentabilidade
  const getProfitabilityColor = (profitability: string) => {
    switch (profitability) {
      case 'HIGH':
        return {
          bg: 'bg-green-50 border-green-200',
          text: 'text-green-800',
          badge: 'bg-green-100 text-green-800'
        }
      case 'MEDIUM':
        return {
          bg: 'bg-yellow-50 border-yellow-200',
          text: 'text-yellow-800',
          badge: 'bg-yellow-100 text-yellow-800'
        }
      case 'LOW':
        return {
          bg: 'bg-blue-50 border-blue-200',
          text: 'text-blue-800',
          badge: 'bg-blue-100 text-blue-800'
        }
      default:
        return {
          bg: 'bg-gray-50 border-gray-200',
          text: 'text-gray-800',
          badge: 'bg-gray-100 text-gray-800'
        }
    }
  }

  const colors = getProfitabilityColor(opportunity.profitability)

  const copyToClipboard = useCallback(async () => {
    const text = `${opportunity.symbol} - ${opportunity.spotExchange} → ${opportunity.futuresExchange}
Spread: ${opportunity.spreadPercentage.toFixed(3)}%
Tipo: ${opportunity.type}
Spot: ${opportunity.spotPrice.toFixed(4)} (${opportunity.spotExchange})
Futures: ${opportunity.futuresPrice.toFixed(4)} (${opportunity.futuresExchange})
Volume: ${Math.min(opportunity.spotVolume, opportunity.futuresVolume).toLocaleString()}`

    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Erro ao copiar:', err)
    }
  }, [opportunity])

  const openSpotExchange = useCallback(() => {
    if (opportunity.urls?.spot) {
      window.open(opportunity.urls.spot, '_blank')
    }
  }, [opportunity.urls?.spot])

  const openFuturesExchange = useCallback(() => {
    if (opportunity.urls?.futures) {
      window.open(opportunity.urls.futures, '_blank')
    }
  }, [opportunity.urls?.futures])

  const formatVolume = (volume: number) => {
    if (volume >= 1000000) {
      return `${(volume / 1000000).toFixed(1)}M`
    } else if (volume >= 1000) {
      return `${(volume / 1000).toFixed(1)}K`
    }
    return volume.toFixed(0)
  }

  const getDataAge = () => {
    const ageInSeconds = opportunity.dataAge / 1000
    if (ageInSeconds < 60) {
      return `${ageInSeconds.toFixed(0)}s`
    } else if (ageInSeconds < 3600) {
      return `${(ageInSeconds / 60).toFixed(0)}m`
    }
    return `${(ageInSeconds / 3600).toFixed(0)}h`
  }

  return (
    <Card
        className={`p-6 transition-all duration-200 hover:shadow-lg cursor-pointer ${
          colors.bg
        } ${
          isSelected ? 'ring-2 ring-blue-500' : ''
        }`}
      onClick={() => onSelect?.(opportunity)}
    >
      {/* Header com símbolo e spread */}
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="text-lg font-bold text-gray-900 mb-1">
            {opportunity.symbol}
          </h3>
          <p className="text-sm text-gray-600">
            {opportunity.spotExchange.toUpperCase()} → {opportunity.futuresExchange.toUpperCase()}
          </p>
        </div>
        
        <div className="text-right">
          <div className="text-xl font-bold flex items-center gap-1 text-green-600">
            <TrendingUp className="h-5 w-5" />
            +{displaySpreadPercentage.toFixed(3)}%
          </div>
        </div>
      </div>

      {/* Informações de preço */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center p-3 bg-white rounded-lg border">
          <p className="text-xs text-gray-500 mb-1">SPOT</p>
          <p className="font-semibold text-gray-900">
            ${opportunity.spotPrice.toFixed(4)}
          </p>
          <p className="text-xs text-gray-600">
            {opportunity.spotExchange.toUpperCase()}
          </p>
        </div>
        
        <div className="text-center p-3 bg-white rounded-lg border">
          <p className="text-xs text-gray-500 mb-1">FUTURES</p>
          <p className="font-semibold text-gray-900">
            ${opportunity.futuresPrice.toFixed(4)}
          </p>
          <p className="text-xs text-gray-600">
            {opportunity.futuresExchange.toUpperCase()}
          </p>
        </div>
      </div>

      {/* Métricas adicionais */}
      <div className="grid grid-cols-3 gap-2 mb-4 text-xs">
        <div className="text-center">
          <div className="flex items-center justify-center gap-1 text-gray-500 mb-1">
            <Activity className="h-3 w-3" />
            Volume
          </div>
          <div className="font-medium text-gray-900">
            ${formatVolume(Math.min(opportunity.spotVolume, opportunity.futuresVolume))}
          </div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center gap-1 text-gray-500 mb-1">
            <TrendingUp className="h-3 w-3" />
            Net Spread
          </div>
          <div className="font-medium text-gray-900">
            {displayNetSpread.toFixed(3)}%
          </div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center gap-1 text-gray-500 mb-1">
            <Clock className="h-3 w-3" />
            Idade
          </div>
          <div className="font-medium text-gray-900">
            {getDataAge()}
          </div>
        </div>
      </div>

      {/* Tipo de arbitragem */}
      <div className="mb-4">
        <div className="flex items-center gap-2 text-sm">
          <span className="text-gray-500">Tipo:</span>
          <span className={`px-2 py-1 rounded text-xs font-medium ${
            opportunity.type === 'spot-futures-cross' 
              ? 'bg-green-100 text-green-800' 
              : 'bg-purple-100 text-purple-800'
          }`}>
            {opportunity.type === 'spot-futures-cross' ? 'Spot-Futures' : 'Futures-Futures'}
          </span>
        </div>
      </div>

      {/* Risk Analysis */}
      {opportunity.riskAnalysis && (
        <div className="mb-4 p-3 bg-white rounded-lg border">
          <div className="flex items-center gap-2 mb-2">
            <AlertCircle className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Análise de Risco</span>
          </div>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="text-gray-500">Nível:</span>
              <span className={`ml-1 px-2 py-1 rounded ${
                opportunity.riskAnalysis.riskLevel === 'low' ? 'bg-green-100 text-green-800' :
                opportunity.riskAnalysis.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {opportunity.riskAnalysis.riskLevel.toUpperCase()}
              </span>
            </div>
            <div>
              <span className="text-gray-500">Liquidez:</span>
              <span className="ml-1 font-medium">
                {(opportunity.riskAnalysis.liquidityScore * 100).toFixed(0)}%
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Ações */}
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={(e) => {
            e.stopPropagation()
            copyToClipboard()
          }}
          className="flex-1"
        >
          <Copy className="h-4 w-4 mr-2" />
          {copied ? 'Copiado!' : 'Copiar'}
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={(e) => {
            e.stopPropagation()
            setShowChart(true)
          }}
          title="Ver Gráfico Histórico"
        >
          <BarChart3 className="h-4 w-4" />
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={(e) => {
            e.stopPropagation()
            openSpotExchange()
          }}
          title={`Abrir ${opportunity.spotExchange.toUpperCase()} (Spot)`}
        >
          <ExternalLink className="h-4 w-4" />
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={(e) => {
            e.stopPropagation()
            openFuturesExchange()
          }}
          title={`Abrir ${opportunity.futuresExchange.toUpperCase()} (Futures)`}
        >
          <ExternalLink className="h-4 w-4" />
        </Button>
      </div>

      {/* Chart Modal */}
      <ChartModal
        isOpen={showChart}
        onClose={() => setShowChart(false)}
        opportunity={opportunity}
      />


    </Card>
  )
}

// Memoizar o componente para evitar re-renders desnecessários
export default memo(OpportunityCard)