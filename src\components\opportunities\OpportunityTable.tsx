// OpportunityTable - Tabela de Oportunidades em Grid de Cards com Filtros Avançados

import { useState, useMemo, useCallback, memo } from 'react'
import { Search, Filter, ArrowUpDown, ExternalLink, Settings } from 'lucide-react'
import { Card } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { Button } from '@/components/ui/Button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select'
import { OpportunityCard } from './OpportunityCard'
import { AdvancedFilters } from './AdvancedFilters'
import { useFilters } from '@/hooks/useFilters'
import type { ArbitrageOpportunity } from '@/types/arbitrage'

interface OpportunityTableProps {
  opportunities: ArbitrageOpportunity[]
  isLoading?: boolean
  className?: string
}

type SortField = 'spread' | 'volume' | 'symbol' | 'profitability'
type SortDirection = 'asc' | 'desc'

export function OpportunityTable({
  opportunities,
  isLoading = false,
  className = ''
}: OpportunityTableProps) {
  const [sortField, setSortField] = useState<SortField>('spread')
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 50 // Limitar a 50 itens por página para performance

  // Hook de filtros
  const {
    filters,
    setFilters,
    updateFilter,
    resetFilters,
    filteredOpportunities,
    activeFiltersCount,
    exchanges,
    isFiltering
  } = useFilters(opportunities)

  // Ordenar oportunidades filtradas
  const sortedOpportunities = useMemo(() => {
    const sorted = [...filteredOpportunities]
    
    sorted.sort((a, b) => {
      let aValue: any
      let bValue: any

      switch (sortField) {
        case 'spread':
          aValue = Math.abs(a.spreadPercentage)
          bValue = Math.abs(b.spreadPercentage)
          break
        case 'volume':
          aValue = Math.min(a.spotVolume, a.futuresVolume)
          bValue = Math.min(b.spotVolume, b.futuresVolume)
          break
        case 'symbol':
          aValue = a.symbol
          bValue = b.symbol
          break
        case 'profitability':
          const profitOrder = { high: 3, medium: 2, low: 1 }
          aValue = profitOrder[a.profitability]
          bValue = profitOrder[b.profitability]
          break
        default:
          return 0
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    return sorted
  }, [filteredOpportunities, sortField, sortDirection])

  // Paginação para performance
  const paginatedOpportunities = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    return sortedOpportunities.slice(startIndex, endIndex)
  }, [sortedOpportunities, currentPage, itemsPerPage])

  const totalPages = Math.ceil(sortedOpportunities.length / itemsPerPage)

  const handleSort = useCallback((field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }, [sortField, sortDirection])

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        {/* Loading Header */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="h-10 bg-gray-200 rounded animate-pulse flex-1"></div>
          <div className="h-10 bg-gray-200 rounded animate-pulse w-32"></div>
        </div>

        {/* Loading Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map(i => (
            <Card key={i} className="p-6">
              <div className="animate-pulse space-y-4">
                <div className="flex justify-between">
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                  <div className="h-6 bg-gray-200 rounded w-16"></div>
                </div>
                <div className="h-3 bg-gray-200 rounded w-32"></div>
                <div className="h-3 bg-gray-200 rounded w-24"></div>
                <div className="flex gap-2">
                  <div className="h-8 bg-gray-200 rounded w-20"></div>
                  <div className="h-8 bg-gray-200 rounded w-20"></div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header com busca rápida e controles */}
      <div className="flex flex-col gap-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Buscar por símbolo (ex: BTC, ETH)..."
              value={filters.searchTerm}
              onChange={(e) => updateFilter('searchTerm', e.target.value)}
              className="pl-10"
            />
            {isFiltering && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
              </div>
            )}
          </div>
          
          <div className="flex gap-2">
            <Button
              variant={showAdvancedFilters ? "default" : "outline"}
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              className="flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              Filtros Avançados
              {activeFiltersCount > 0 && (
                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                  {activeFiltersCount}
                </span>
              )}
            </Button>
            
            <Button
              variant="outline"
              onClick={() => handleSort('spread')}
              className="flex items-center gap-2"
            >
              <ArrowUpDown className="h-4 w-4" />
              Spread {sortField === 'spread' && (sortDirection === 'desc' ? '↓' : '↑')}
            </Button>
          </div>
        </div>

        {/* Filtros básicos rápidos */}
        <div className="flex flex-wrap gap-2">
          <Select value={filters.spotExchange} onValueChange={(value) => updateFilter('spotExchange', value)}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Spot Exchange" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todas Spot</SelectItem>
              {exchanges.spot.map(exchange => (
                <SelectItem key={exchange} value={exchange}>
                  {exchange.toUpperCase()}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filters.futuresExchange} onValueChange={(value) => updateFilter('futuresExchange', value)}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Futures Exchange" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todas Futures</SelectItem>
              {exchanges.futures.map(exchange => (
                <SelectItem key={exchange} value={exchange}>
                  {exchange.toUpperCase()}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filters.profitability} onValueChange={(value) => updateFilter('profitability', value)}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Rentabilidade" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todas</SelectItem>
              <SelectItem value="high">Alta</SelectItem>
              <SelectItem value="medium">Média</SelectItem>
              <SelectItem value="low">Baixa</SelectItem>
            </SelectContent>
          </Select>

          {activeFiltersCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={resetFilters}
              className="text-gray-500 hover:text-gray-700"
            >
              Limpar ({activeFiltersCount})
            </Button>
          )}
        </div>
      </div>

      {/* Filtros Avançados */}
      {showAdvancedFilters && (
        <AdvancedFilters
          filters={filters}
          onFiltersChange={setFilters}
          exchanges={exchanges}
          onToggleCollapse={() => setShowAdvancedFilters(false)}
        />
      )}

      {/* Resultados */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-600">
          {sortedOpportunities.length} de {opportunities.length} oportunidades
          {isFiltering && <span className="text-blue-600 ml-2">• Filtrando...</span>}
        </p>
        
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500">Ordenar por:</span>
          <Select value={sortField} onValueChange={(value) => handleSort(value as SortField)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="spread">Spread</SelectItem>
              <SelectItem value="volume">Volume</SelectItem>
              <SelectItem value="symbol">Símbolo</SelectItem>
              <SelectItem value="profitability">Rentabilidade</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Grid de oportunidades */}
      {sortedOpportunities.length === 0 ? (
        <Card className="p-12 text-center">
          <div className="text-gray-400 mb-4">
            <ExternalLink className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Nenhuma oportunidade encontrada
          </h3>
          <p className="text-gray-500 mb-4">
            {opportunities.length === 0 
              ? 'O sistema está coletando dados das exchanges...'
              : 'Tente ajustar os filtros para ver mais resultados'
            }
          </p>
          {activeFiltersCount > 0 && (
            <Button variant="outline" onClick={resetFilters}>
              Limpar Filtros
            </Button>
          )}
        </Card>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {paginatedOpportunities.map((opportunity) => (
              <OpportunityCard
                key={opportunity.id}
                opportunity={opportunity}
              />
            ))}
          </div>
          
          {/* Paginação */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-8">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                Anterior
              </Button>
              
              <span className="text-sm text-gray-600 px-4">
                Página {currentPage} de {totalPages} ({sortedOpportunities.length} oportunidades)
              </span>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                Próxima
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  )
}

// Memoizar o componente para evitar re-renders desnecessários
export default memo(OpportunityTable)