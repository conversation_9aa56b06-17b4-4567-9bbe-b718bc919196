// OTIMIZAÇÃO: Tabela virtualizada para alta performance com milhares de oportunidades

import React, { memo, useMemo, useCallback } from 'react'
import { FixedSizeList as List } from 'react-window'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ArrowUpDown, TrendingUp, ExternalLink } from 'lucide-react'
import type { ArbitrageOpportunity } from '@/types/arbitrage'

interface VirtualizedOpportunityTableProps {
  opportunities: ArbitrageOpportunity[]
  height?: number
  itemHeight?: number
  onOpportunityClick?: (opportunity: ArbitrageOpportunity) => void
  className?: string
}

// OTIMIZAÇÃO: Componente de linha memoizado para evitar re-renders desnecessários
const OpportunityRow = memo<{
  index: number
  style: React.CSSProperties
  data: {
    opportunities: ArbitrageOpportunity[]
    onOpportunityClick?: (opportunity: ArbitrageOpportunity) => void
  }
}>(({ index, style, data }) => {
  const opportunity = data.opportunities[index]
  
  if (!opportunity) {
    return <div style={style} />
  }

  const handleClick = useCallback(() => {
    data.onOpportunityClick?.(opportunity)
  }, [data.onOpportunityClick, opportunity])

  const handleSpotClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()
    if (opportunity.urls?.spot) {
      window.open(opportunity.urls.spot, '_blank')
    }
  }, [opportunity.urls?.spot])

  const handleFuturesClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()
    if (opportunity.urls?.futures) {
      window.open(opportunity.urls.futures, '_blank')
    }
  }, [opportunity.urls?.futures])

  // OTIMIZAÇÃO: Determinar variante do card baseada na rentabilidade
  const cardVariant = useMemo(() => {
    const absSpread = Math.abs(opportunity.spreadPercentage)
    if (absSpread >= 1.0) return 'high-profit'
    if (absSpread >= 0.5) return 'medium-profit'
    return 'low-profit'
  }, [opportunity.spreadPercentage])

  // OTIMIZAÇÃO: Formatação de números memoizada
  const formattedSpread = useMemo(() => 
    opportunity.spreadPercentage.toFixed(3)
  , [opportunity.spreadPercentage])

  const formattedVolume = useMemo(() => {
    const volume = opportunity.spotVolume || 0
    if (volume >= 1000000) return `$${(volume / 1000000).toFixed(1)}M`
    if (volume >= 1000) return `$${(volume / 1000).toFixed(1)}K`
    return `$${volume.toFixed(0)}`
  }, [opportunity.spotVolume])

  const dataAge = useMemo(() => {
    const age = opportunity.dataAge || 0
    if (age < 1000) return `${age}ms`
    return `${(age / 1000).toFixed(1)}s`
  }, [opportunity.dataAge])

  return (
    <div style={style} className="px-2 py-1">
      <Card 
        className={`
          p-3 cursor-pointer transition-all duration-200 hover:shadow-md
          ${cardVariant === 'high-profit' ? 'border-green-500 bg-green-50 dark:bg-green-950' : ''}
          ${cardVariant === 'medium-profit' ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-950' : ''}
          ${cardVariant === 'low-profit' ? 'border-blue-500 bg-blue-50 dark:bg-blue-950' : ''}
        `}
        onClick={handleClick}
      >
        <div className="flex items-center justify-between">
          {/* Símbolo e Exchanges */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span className="font-semibold text-sm truncate">
                {opportunity.symbol}
              </span>
              <Badge variant="outline" size="sm">
                Cross-Exchange
              </Badge>
            </div>
            
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span className="truncate">
                {opportunity.spotExchange?.toUpperCase()} → {opportunity.futuresExchange?.toUpperCase()}
              </span>
              <Badge variant="secondary" size="sm">
                {opportunity.type === 'spot-futures-cross' ? 'Spot-Futures' : 'Futures-Futures'}
              </Badge>
            </div>
          </div>

          {/* Spread e Métricas */}
          <div className="flex items-center gap-3">
            <div className="text-right">
              <div className={`
                font-bold text-sm
                ${opportunity.spreadPercentage > 0 ? 'text-green-600' : 'text-red-600'}
              `}>
                {opportunity.spreadPercentage > 0 ? '+' : ''}{formattedSpread}%
              </div>
              <div className="text-xs text-muted-foreground">
                {formattedVolume}
              </div>
            </div>

            <div className="text-right">
              <div className="text-xs text-muted-foreground">
                {dataAge}
              </div>
              <div className="text-xs">
                <Badge 
                  variant={opportunity.profitability === 'high' ? 'default' : 'secondary'}
                  size="sm"
                >
                  {opportunity.profitability?.toUpperCase()}
                </Badge>
              </div>
            </div>

            {/* Botões de Ação */}
            <div className="flex gap-1">
              <Button
                size="sm"
                variant="outline"
                onClick={handleSpotClick}
                className="h-6 px-2 text-xs"
              >
                Spot
                <ExternalLink className="w-3 h-3 ml-1" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={handleFuturesClick}
                className="h-6 px-2 text-xs"
              >
                Futures
                <ExternalLink className="w-3 h-3 ml-1" />
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
})

OpportunityRow.displayName = 'OpportunityRow'

// OTIMIZAÇÃO: Componente principal da tabela virtualizada
export const VirtualizedOpportunityTable = memo<VirtualizedOpportunityTableProps>(({
  opportunities,
  height = 600,
  itemHeight = 80,
  onOpportunityClick,
  className = ''
}) => {
  // OTIMIZAÇÃO: Dados memoizados para evitar re-renders
  const listData = useMemo(() => ({
    opportunities,
    onOpportunityClick
  }), [opportunities, onOpportunityClick])

  // OTIMIZAÇÃO: Renderização condicional para listas vazias
  if (opportunities.length === 0) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="text-center">
          <TrendingUp className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">Nenhuma oportunidade encontrada</h3>
          <p className="text-muted-foreground">
            Aguarde enquanto coletamos dados das exchanges...
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className={`border rounded-lg ${className}`}>
      {/* Header */}
      <div className="p-4 border-b bg-muted/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ArrowUpDown className="w-4 h-4" />
            <span className="font-semibold">
              Oportunidades de Arbitragem ({opportunities.length.toLocaleString()})
            </span>
          </div>
          <Badge variant="outline">
            Virtualizado para Performance
          </Badge>
        </div>
      </div>

      {/* Lista Virtualizada */}
      <List
        height={height}
        itemCount={opportunities.length}
        itemSize={itemHeight}
        itemData={listData}
        overscanCount={5} // OTIMIZAÇÃO: Pre-render 5 itens extras para scroll suave
        className="scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent"
      >
        {OpportunityRow}
      </List>

      {/* Footer com estatísticas */}
      <div className="p-2 border-t bg-muted/30 text-xs text-muted-foreground">
        <div className="flex justify-between items-center">
          <span>
            Exibindo {opportunities.length.toLocaleString()} oportunidades
          </span>
          <span>
            Virtualização ativa para performance otimizada
          </span>
        </div>
      </div>
    </div>
  )
})

VirtualizedOpportunityTable.displayName = 'VirtualizedOpportunityTable'

export default VirtualizedOpportunityTable
