// OptimizationDashboard - Dashboard Unificado para Otimização e Produção

import React, { useState } from 'react'
import { Card } from '../ui/Card'
import { Tabs } from '../ui/Tabs'
import { 
  Zap, 
  Activity, 
  Server, 
  Shield,
  TrendingUp,
  Settings,
  Play,
  Gauge
} from 'lucide-react'
import PerformanceOptimizationDashboard from './PerformanceOptimizationDashboard'
import ProductionDeploymentDashboard from '../deployment/ProductionDeploymentDashboard'

interface OptimizationDashboardProps {
  className?: string
}

export function OptimizationDashboard({ className = '' }: OptimizationDashboardProps) {
  const [activeTab, setActiveTab] = useState('performance')

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold">Sistema de Otimização e Produção</h3>
          </div>
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Sistema Operacional</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>Otimizações Ativas</span>
            </div>
          </div>
        </div>

        <div className="text-xs text-gray-500 text-center">
          FASE 6 - Otimização de Performance e Preparação para Produção
        </div>
      </Card>

      {/* Tabs de Otimização */}
      <Tabs defaultValue="performance" className="w-full">
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6">
          <button
            className={`flex-1 flex items-center justify-center gap-1 px-3 py-2 rounded-md text-xs font-medium transition-colors ${
              activeTab === 'performance' 
                ? 'bg-white text-blue-600 shadow-sm' 
                : 'hover:bg-gray-200'
            }`}
            onClick={() => setActiveTab('performance')}
          >
            <TrendingUp className="w-3 h-3" />
            Performance
          </button>
          <button
            className={`flex-1 flex items-center justify-center gap-1 px-3 py-2 rounded-md text-xs font-medium transition-colors ${
              activeTab === 'stress' 
                ? 'bg-white text-blue-600 shadow-sm' 
                : 'hover:bg-gray-200'
            }`}
            onClick={() => setActiveTab('stress')}
          >
            <Activity className="w-3 h-3" />
            Stress Testing
          </button>
          <button
            className={`flex-1 flex items-center justify-center gap-1 px-3 py-2 rounded-md text-xs font-medium transition-colors ${
              activeTab === 'deployment' 
                ? 'bg-white text-blue-600 shadow-sm' 
                : 'hover:bg-gray-200'
            }`}
            onClick={() => setActiveTab('deployment')}
          >
            <Play className="w-3 h-3" />
            Deployment
          </button>
          <button
            className={`flex-1 flex items-center justify-center gap-1 px-3 py-2 rounded-md text-xs font-medium transition-colors ${
              activeTab === 'production' 
                ? 'bg-white text-blue-600 shadow-sm' 
                : 'hover:bg-gray-200'
            }`}
            onClick={() => setActiveTab('production')}
          >
            <Server className="w-3 h-3" />
            Production
          </button>
          <button
            className={`flex-1 flex items-center justify-center gap-1 px-3 py-2 rounded-md text-xs font-medium transition-colors ${
              activeTab === 'security' 
                ? 'bg-white text-blue-600 shadow-sm' 
                : 'hover:bg-gray-200'
            }`}
            onClick={() => setActiveTab('security')}
          >
            <Shield className="w-3 h-3" />
            Security
          </button>
        </div>

        {/* Performance Optimization Content */}
        <div className={activeTab === 'performance' ? '' : 'hidden'}>
          <PerformanceOptimizationDashboard />
        </div>

        {/* Stress Testing Content */}
        <div className={activeTab === 'stress' ? '' : 'hidden'}>
          <Card className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <Activity className="w-6 h-6 text-orange-600" />
              <h3 className="text-lg font-semibold">Stress Testing</h3>
            </div>
            <p className="text-gray-600 mb-4">
              Sistema de testes de estresse para validar performance sob carga alta.
            </p>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <p className="text-sm text-yellow-800">
                Componente de stress testing será implementado na próxima fase de desenvolvimento.
              </p>
            </div>
          </Card>
        </div>

        {/* Deployment Content */}
        <div className={activeTab === 'deployment' ? '' : 'hidden'}>
          <ProductionDeploymentDashboard />
        </div>

        {/* Production Ready Content */}
        <div className={activeTab === 'production' ? '' : 'hidden'}>
          <div className="space-y-6">
            <Card className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <Server className="w-6 h-6 text-green-600" />
                <h3 className="text-lg font-semibold">Production Readiness</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="text-2xl font-bold text-green-600">98.5%</div>
                  <div className="text-sm text-green-700">System Uptime</div>
                  <div className="text-xs text-green-600 mt-1">Last 30 days</div>
                </div>
                
                <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="text-2xl font-bold text-blue-600">1.2s</div>
                  <div className="text-sm text-blue-700">Avg Response Time</div>
                  <div className="text-xs text-blue-600 mt-1">Under 2s target</div>
                </div>
                
                <div className="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
                  <div className="text-2xl font-bold text-purple-600">0.8%</div>
                  <div className="text-sm text-purple-700">Error Rate</div>
                  <div className="text-xs text-purple-600 mt-1">Under 1% target</div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">Production Checklist</h4>
                <div className="space-y-2">
                  {[
                    { item: 'Environment Variables Configured', status: 'completed' },
                    { item: 'Database Connections Optimized', status: 'completed' },
                    { item: 'API Rate Limiting Implemented', status: 'completed' },
                    { item: 'Error Monitoring Setup', status: 'completed' },
                    { item: 'Backup Strategy Implemented', status: 'in-progress' },
                    { item: 'Load Balancer Configuration', status: 'pending' }
                  ].map((check, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="text-sm">{check.item}</span>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        check.status === 'completed' ? 'bg-green-100 text-green-800' :
                        check.status === 'in-progress' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {check.status.replace('-', ' ').toUpperCase()}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h4 className="font-medium mb-4">Deployment Configuration</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <h5 className="text-sm font-medium text-gray-700">Environment Settings</h5>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Node Environment:</span>
                      <span className="font-mono bg-gray-100 px-2 py-1 rounded">production</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Port:</span>
                      <span className="font-mono bg-gray-100 px-2 py-1 rounded">3000</span>
                    </div>
                    <div className="flex justify-between">
                      <span>SSL Enabled:</span>
                      <span className="font-mono bg-green-100 text-green-800 px-2 py-1 rounded">true</span>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <h5 className="text-sm font-medium text-gray-700">Resource Limits</h5>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Memory Limit:</span>
                      <span className="font-mono bg-gray-100 px-2 py-1 rounded">512MB</span>
                    </div>
                    <div className="flex justify-between">
                      <span>CPU Limit:</span>
                      <span className="font-mono bg-gray-100 px-2 py-1 rounded">2 cores</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Max Connections:</span>
                      <span className="font-mono bg-gray-100 px-2 py-1 rounded">1000</span>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>

        {/* Security Hardening Content */}
        <div className={activeTab === 'security' ? '' : 'hidden'}>
          <div className="space-y-6">
            <Card className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <Shield className="w-6 h-6 text-red-600" />
                <h3 className="text-lg font-semibold">Security Hardening</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="text-2xl font-bold text-green-600">A+</div>
                  <div className="text-sm text-green-700">Security Grade</div>
                  <div className="text-xs text-green-600 mt-1">SSL Labs Rating</div>
                </div>
                
                <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="text-2xl font-bold text-blue-600">100%</div>
                  <div className="text-sm text-blue-700">HTTPS Coverage</div>
                  <div className="text-xs text-blue-600 mt-1">All endpoints secured</div>
                </div>
                
                <div className="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
                  <div className="text-2xl font-bold text-purple-600">0</div>
                  <div className="text-sm text-purple-700">Security Vulnerabilities</div>
                  <div className="text-xs text-purple-600 mt-1">Last scan: Today</div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">Security Measures</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h5 className="text-sm font-medium text-gray-700">Authentication & Authorization</h5>
                    <div className="space-y-1 text-sm">
                      {[
                        'HMAC Authentication Implemented',
                        'API Key Rotation Enabled',
                        'Rate Limiting Active',
                        'Request Validation Enabled'
                      ].map((item, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <span>{item}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h5 className="text-sm font-medium text-gray-700">Data Protection</h5>
                    <div className="space-y-1 text-sm">
                      {[
                        'Data Encryption at Rest',
                        'TLS 1.3 for Data in Transit',
                        'Sensitive Data Masking',
                        'Audit Logging Enabled'
                      ].map((item, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <span>{item}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h4 className="font-medium mb-4">Security Monitoring</h4>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-lg font-bold">24/7</div>
                    <div className="text-sm text-gray-600">Monitoring</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-lg font-bold">&lt; 1min</div>
                    <div className="text-sm text-gray-600">Alert Response</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-lg font-bold">99.9%</div>
                    <div className="text-sm text-gray-600">Uptime SLA</div>
                  </div>
                </div>
                
                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h5 className="font-medium text-blue-800 mb-2">Recent Security Events</h5>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Last Security Scan:</span>
                      <span className="text-blue-700">2 hours ago - No issues found</span>
                    </div>
                    <div className="flex justify-between">
                      <span>SSL Certificate:</span>
                      <span className="text-blue-700">Valid until Dec 2025</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Dependency Audit:</span>
                      <span className="text-blue-700">All packages up to date</span>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </Tabs>
    </div>
  )
}

export default OptimizationDashboard