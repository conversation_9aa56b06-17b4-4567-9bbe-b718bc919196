// PositionManager - Gerenciador Avançado de Posições Cross-Exchange

import { useState, useEffect, useMemo } from 'react'
import { Plus, ExternalLink, AlertCircle, TrendingUp, TrendingDown, X, Edit, Save, Calculator } from 'lucide-react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select'
import { Switch } from '@/components/ui/Switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs'
import { useArbitrageData } from '@/hooks/useArbitrageData'
import { AlertSystem } from '@/services/AlertSystem'
import type { ArbitrageOpportunity, Position } from '@/types/arbitrage'

interface PositionManagerProps {
  className?: string
}

interface NewPositionForm {
  symbol: string
  spotExchange: string
  futuresExchange: string
  entrySpotPrice: number
  entryFuturesPrice: number
  quantity: number
  alertThreshold: number
  alertEnabled: boolean
  notes: string
}

const DEFAULT_FORM: NewPositionForm = {
  symbol: '',
  spotExchange: '',
  futuresExchange: '',
  entrySpotPrice: 0,
  entryFuturesPrice: 0,
  quantity: 0,
  alertThreshold: 0.1,
  alertEnabled: true,
  notes: ''
}

export function PositionManager({ className = '' }: PositionManagerProps) {
  const [positions, setPositions] = useState<Position[]>([])
  const [showAddForm, setShowAddForm] = useState(false)
  const [newPosition, setNewPosition] = useState<NewPositionForm>(DEFAULT_FORM)
  const [editingPosition, setEditingPosition] = useState<string | null>(null)
  const [selectedOpportunity, setSelectedOpportunity] = useState<ArbitrageOpportunity | null>(null)

  // Hook para dados de arbitragem
  const { opportunities } = useArbitrageData({
    enabled: true,
    enableAlerts: false // Evitar conflito com alertas de posições
  })

  // Instância do sistema de alertas
  const alertSystem = AlertSystem.getInstance()

  // Carregar posições do localStorage
  useEffect(() => {
    const savedPositions = localStorage.getItem('crypto-arbitrage-positions')
    if (savedPositions) {
      try {
        const parsed = JSON.parse(savedPositions)
        setPositions(parsed.map((p: any) => ({
          ...p,
          entryTime: new Date(p.entryTime),
          lastUpdate: new Date(p.lastUpdate),
          lastAlertTime: p.lastAlertTime ? new Date(p.lastAlertTime) : undefined
        })))
      } catch (error) {
        console.error('Erro ao carregar posições:', error)
      }
    }
  }, [])

  // Salvar posições no localStorage
  const savePositions = (newPositions: Position[]) => {
    setPositions(newPositions)
    localStorage.setItem('crypto-arbitrage-positions', JSON.stringify(newPositions))
  }

  // Atualizar posições com dados atuais
  useEffect(() => {
    if (opportunities.length === 0 || positions.length === 0) return

    const updatedPositions = positions.map(position => {
      // Encontrar oportunidade correspondente
      const currentOpportunity = opportunities.find(opp => 
        opp.symbol === position.symbol &&
        opp.spotExchange === position.spotExchange &&
        opp.futuresExchange === position.futuresExchange
      )

      if (!currentOpportunity) {
        return { ...position, status: 'stale' as const }
      }

      // Calcular P&L atual
      const currentSpread = currentOpportunity.spreadPercentage
      const entrySpread = position.entrySpread
      const spreadDiff = currentSpread - entrySpread
      
      // P&L baseado na diferença de spread e quantidade
      const pnlPercentage = spreadDiff
      const pnlAbsolute = (spreadDiff / 100) * position.quantity * position.entrySpotPrice

      // Determinar status
      let status: Position['status'] = 'active'
      if (Math.abs(currentSpread) < position.alertThreshold) {
        status = 'alert'
      }

      const updatedPosition: Position = {
        ...position,
        currentSpread,
        currentSpotPrice: currentOpportunity.spotPrice,
        currentFuturesPrice: currentOpportunity.futuresPrice,
        pnlPercentage,
        pnlAbsolute,
        status,
        lastUpdate: new Date()
      }

      // Verificar alertas
      if (position.alertEnabled && status === 'alert') {
        alertSystem.checkCrossExchangeCloseAlert(updatedPosition)
      }

      return updatedPosition
    })

    if (JSON.stringify(updatedPositions) !== JSON.stringify(positions)) {
      savePositions(updatedPositions)
    }
  }, [opportunities, positions, alertSystem])

  // Preencher formulário com oportunidade selecionada
  const fillFormFromOpportunity = (opportunity: ArbitrageOpportunity) => {
    setNewPosition({
      symbol: opportunity.symbol,
      spotExchange: opportunity.spotExchange,
      futuresExchange: opportunity.futuresExchange,
      entrySpotPrice: opportunity.spotPrice,
      entryFuturesPrice: opportunity.futuresPrice,
      quantity: 1000, // Valor padrão
      alertThreshold: Math.abs(opportunity.spreadPercentage) * 0.2, // 20% do spread atual
      alertEnabled: true,
      notes: `Posição criada a partir de oportunidade com spread de ${opportunity.spreadPercentage.toFixed(3)}%`
    })
    setSelectedOpportunity(opportunity)
    setShowAddForm(true)
  }

  // Adicionar nova posição
  const addPosition = () => {
    if (!newPosition.symbol || !newPosition.spotExchange || !newPosition.futuresExchange) {
      return
    }

    const entrySpread = ((newPosition.entryFuturesPrice - newPosition.entrySpotPrice) / newPosition.entrySpotPrice) * 100

    const position: Position = {
      id: `pos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      symbol: newPosition.symbol,
      spotExchange: newPosition.spotExchange as any,
      futuresExchange: newPosition.futuresExchange as any,
      entrySpotPrice: newPosition.entrySpotPrice,
      entryFuturesPrice: newPosition.entryFuturesPrice,
      entrySpread,
      currentSpread: entrySpread,
      currentSpotPrice: newPosition.entrySpotPrice,
      currentFuturesPrice: newPosition.entryFuturesPrice,
      quantity: newPosition.quantity,
      pnlPercentage: 0,
      pnlAbsolute: 0,
      alertThreshold: newPosition.alertThreshold,
      alertEnabled: newPosition.alertEnabled,
      status: 'active',
      entryTime: new Date(),
      lastUpdate: new Date(),
      notes: newPosition.notes
    }

    savePositions([...positions, position])
    setNewPosition(DEFAULT_FORM)
    setSelectedOpportunity(null)
    setShowAddForm(false)
  }

  // Remover posição
  const removePosition = (positionId: string) => {
    savePositions(positions.filter(p => p.id !== positionId))
  }

  // Atualizar threshold de alerta
  const updateAlertThreshold = (positionId: string, threshold: number) => {
    const updatedPositions = positions.map(p => 
      p.id === positionId ? { ...p, alertThreshold: threshold } : p
    )
    savePositions(updatedPositions)
  }

  // Estatísticas das posições
  const positionStats = useMemo(() => {
    const totalPnL = positions.reduce((sum, p) => sum + p.pnlAbsolute, 0)
    const totalPositions = positions.length
    const activePositions = positions.filter(p => p.status === 'active').length
    const alertPositions = positions.filter(p => p.status === 'alert').length
    const profitablePositions = positions.filter(p => p.pnlAbsolute > 0).length

    return {
      totalPnL,
      totalPositions,
      activePositions,
      alertPositions,
      profitablePositions,
      winRate: totalPositions > 0 ? (profitablePositions / totalPositions) * 100 : 0
    }
  }, [positions])

  // Renderizar formulário de nova posição
  const renderAddPositionForm = () => (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Nova Posição Cross-Exchange</h3>
        <Button variant="ghost" size="sm" onClick={() => setShowAddForm(false)}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      {selectedOpportunity && (
        <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <p className="text-sm text-blue-800">
            <strong>Oportunidade selecionada:</strong> {selectedOpportunity.symbol} 
            ({selectedOpportunity.spotExchange} → {selectedOpportunity.futuresExchange})
            - Spread: {selectedOpportunity.spreadPercentage.toFixed(3)}%
          </p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">
            Símbolo
          </label>
          <Input
            value={newPosition.symbol}
            onChange={(e) => setNewPosition({...newPosition, symbol: e.target.value})}
            placeholder="BTC/USDT"
          />
        </div>

        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">
            Quantidade
          </label>
          <Input
            type="number"
            value={newPosition.quantity}
            onChange={(e) => setNewPosition({...newPosition, quantity: parseFloat(e.target.value) || 0})}
            placeholder="1000"
          />
        </div>

        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">
            Spot Exchange
          </label>
          <Select value={newPosition.spotExchange} onValueChange={(value) => setNewPosition({...newPosition, spotExchange: value})}>
            <SelectTrigger>
              <SelectValue placeholder="Selecionar exchange" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="gateio">Gate.io</SelectItem>
              <SelectItem value="mexc">MEXC</SelectItem>
              <SelectItem value="bitget">Bitget</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">
            Futures Exchange
          </label>
          <Select value={newPosition.futuresExchange} onValueChange={(value) => setNewPosition({...newPosition, futuresExchange: value})}>
            <SelectTrigger>
              <SelectValue placeholder="Selecionar exchange" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="gateio">Gate.io</SelectItem>
              <SelectItem value="mexc">MEXC</SelectItem>
              <SelectItem value="bitget">Bitget</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">
            Preço Spot de Entrada
          </label>
          <Input
            type="number"
            step="0.0001"
            value={newPosition.entrySpotPrice}
            onChange={(e) => setNewPosition({...newPosition, entrySpotPrice: parseFloat(e.target.value) || 0})}
            placeholder="45000.00"
          />
        </div>

        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">
            Preço Futures de Entrada
          </label>
          <Input
            type="number"
            step="0.0001"
            value={newPosition.entryFuturesPrice}
            onChange={(e) => setNewPosition({...newPosition, entryFuturesPrice: parseFloat(e.target.value) || 0})}
            placeholder="45150.00"
          />
        </div>

        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">
            Threshold de Alerta (%)
          </label>
          <Input
            type="number"
            step="0.01"
            value={newPosition.alertThreshold}
            onChange={(e) => setNewPosition({...newPosition, alertThreshold: parseFloat(e.target.value) || 0})}
            placeholder="0.10"
          />
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            checked={newPosition.alertEnabled}
            onCheckedChange={(checked) => setNewPosition({...newPosition, alertEnabled: checked})}
          />
          <label className="text-sm font-medium text-gray-700">
            Alertas Habilitados
          </label>
        </div>
      </div>

      <div className="mt-4">
        <label className="text-sm font-medium text-gray-700 mb-2 block">
          Notas
        </label>
        <Input
          value={newPosition.notes}
          onChange={(e) => setNewPosition({...newPosition, notes: e.target.value})}
          placeholder="Notas sobre a posição..."
        />
      </div>

      {/* Preview do spread */}
      {newPosition.entrySpotPrice > 0 && newPosition.entryFuturesPrice > 0 && (
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Calculator className="h-4 w-4 text-gray-600" />
            <span className="text-sm font-medium text-gray-700">Preview do Spread</span>
          </div>
          <div className="text-sm text-gray-600">
            Spread de entrada: {(((newPosition.entryFuturesPrice - newPosition.entrySpotPrice) / newPosition.entrySpotPrice) * 100).toFixed(3)}%
          </div>
        </div>
      )}

      <div className="flex gap-2 mt-6">
        <Button onClick={addPosition} className="flex-1">
          <Plus className="h-4 w-4 mr-2" />
          Adicionar Posição
        </Button>
        <Button variant="outline" onClick={() => setShowAddForm(false)}>
          Cancelar
        </Button>
      </div>
    </Card>
  )

  // Renderizar tabela de posições
  const renderPositionsTable = () => (
    <div className="space-y-4">
      {positions.length === 0 ? (
        <Card className="p-8 text-center">
          <div className="text-gray-400 mb-4">
            <TrendingUp className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Nenhuma posição ativa
          </h3>
          <p className="text-gray-500 mb-4">
            Adicione uma nova posição ou selecione uma oportunidade da tabela
          </p>
          <Button onClick={() => setShowAddForm(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Nova Posição
          </Button>
        </Card>
      ) : (
        positions.map(position => (
          <Card key={position.id} className={`p-4 ${
            position.status === 'alert' ? 'border-yellow-300 bg-yellow-50' :
            position.status === 'stale' ? 'border-gray-300 bg-gray-50' :
            'border-gray-200'
          }`}>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h4 className="font-semibold text-lg">{position.symbol}</h4>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    position.status === 'active' ? 'bg-green-100 text-green-800' :
                    position.status === 'alert' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {position.status.toUpperCase()}
                  </span>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500">Exchanges</p>
                    <p className="font-medium">
                      {position.spotExchange.toUpperCase()} → {position.futuresExchange.toUpperCase()}
                    </p>
                  </div>
                  
                  <div>
                    <p className="text-gray-500">Spread Entrada</p>
                    <p className="font-medium">{position.entrySpread.toFixed(3)}%</p>
                  </div>
                  
                  <div>
                    <p className="text-gray-500">Spread Atual</p>
                    <p className={`font-medium ${
                      position.currentSpread > position.entrySpread ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {position.currentSpread.toFixed(3)}%
                    </p>
                  </div>
                  
                  <div>
                    <p className="text-gray-500">P&L</p>
                    <div className={`font-bold ${
                      position.pnlAbsolute > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      <div>{position.pnlPercentage > 0 ? '+' : ''}{position.pnlPercentage.toFixed(2)}%</div>
                      <div className="text-xs">
                        ${position.pnlAbsolute > 0 ? '+' : ''}{position.pnlAbsolute.toFixed(2)}
                      </div>
                    </div>
                  </div>
                </div>

                {position.notes && (
                  <p className="text-sm text-gray-600 mt-2 italic">
                    {position.notes}
                  </p>
                )}
              </div>

              <div className="flex items-center gap-2 ml-4">
                {/* Threshold de alerta editável */}
                <div className="text-right">
                  <p className="text-xs text-gray-500">Threshold</p>
                  {editingPosition === position.id ? (
                    <div className="flex items-center gap-1">
                      <Input
                        type="number"
                        step="0.01"
                        value={position.alertThreshold}
                        onChange={(e) => updateAlertThreshold(position.id, parseFloat(e.target.value) || 0)}
                        className="w-16 h-6 text-xs"
                      />
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setEditingPosition(null)}
                      >
                        <Save className="h-3 w-3" />
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center gap-1">
                      <span className="text-sm font-medium">{position.alertThreshold.toFixed(2)}%</span>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setEditingPosition(position.id)}
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                    </div>
                  )}
                </div>

                {/* Botões de ação */}
                <div className="flex flex-col gap-1">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => window.open(`https://${position.spotExchange}.com`, '_blank')}
                    title={`Abrir ${position.spotExchange.toUpperCase()} (Spot)`}
                  >
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => window.open(`https://${position.futuresExchange}.com`, '_blank')}
                    title={`Abrir ${position.futuresExchange.toUpperCase()} (Futures)`}
                  >
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                </div>

                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => removePosition(position.id)}
                  className="text-red-600 hover:text-red-700"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {position.status === 'alert' && (
              <div className="mt-3 p-2 bg-yellow-100 rounded-lg flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-yellow-600" />
                <span className="text-sm text-yellow-800">
                  Spread próximo do threshold ({position.alertThreshold.toFixed(2)}%) - considere fechar a posição
                </span>
              </div>
            )}
          </Card>
        ))
      )}
    </div>
  )

  // Renderizar seletor de oportunidades
  const renderOpportunitySelector = () => (
    <Card className="p-4">
      <h3 className="font-semibold mb-4">Selecionar Oportunidade</h3>
      <div className="space-y-2 max-h-60 overflow-y-auto">
        {opportunities.slice(0, 10).map(opportunity => (
          <div
            key={opportunity.id}
            className="flex items-center justify-between p-2 hover:bg-gray-50 rounded cursor-pointer"
            onClick={() => fillFormFromOpportunity(opportunity)}
          >
            <div>
              <span className="font-medium">{opportunity.symbol}</span>
              <span className="text-sm text-gray-500 ml-2">
                {opportunity.spotExchange} → {opportunity.futuresExchange}
              </span>
            </div>
            <div className={`text-sm font-medium ${
              opportunity.spreadPercentage > 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {opportunity.spreadPercentage > 0 ? '+' : ''}{opportunity.spreadPercentage.toFixed(3)}%
            </div>
          </div>
        ))}
      </div>
    </Card>
  )

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header com estatísticas */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Gerenciador de Posições Cross-Exchange
          </h2>
          <p className="text-gray-600">
            Gerencie suas posições de arbitragem com P&L em tempo real
          </p>
        </div>
        
        {!showAddForm && (
          <Button onClick={() => setShowAddForm(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Nova Posição
          </Button>
        )}
      </div>

      {/* Cards de estatísticas */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-gray-900">
            {positionStats.totalPositions}
          </div>
          <div className="text-sm text-gray-500">Total</div>
        </Card>
        
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-green-600">
            {positionStats.activePositions}
          </div>
          <div className="text-sm text-gray-500">Ativas</div>
        </Card>
        
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-yellow-600">
            {positionStats.alertPositions}
          </div>
          <div className="text-sm text-gray-500">Alertas</div>
        </Card>
        
        <Card className="p-4 text-center">
          <div className={`text-2xl font-bold ${
            positionStats.totalPnL > 0 ? 'text-green-600' : 'text-red-600'
          }`}>
            ${positionStats.totalPnL > 0 ? '+' : ''}{positionStats.totalPnL.toFixed(0)}
          </div>
          <div className="text-sm text-gray-500">P&L Total</div>
        </Card>
        
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">
            {positionStats.profitablePositions}
          </div>
          <div className="text-sm text-gray-500">Lucrativas</div>
        </Card>
        
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-purple-600">
            {positionStats.winRate.toFixed(0)}%
          </div>
          <div className="text-sm text-gray-500">Win Rate</div>
        </Card>
      </div>

      {/* Conteúdo principal */}
      {showAddForm ? (
        <Tabs defaultValue="form" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="form">Formulário</TabsTrigger>
            <TabsTrigger value="opportunities">Oportunidades</TabsTrigger>
          </TabsList>

          <TabsContent value="form">
            {renderAddPositionForm()}
          </TabsContent>

          <TabsContent value="opportunities">
            {renderOpportunitySelector()}
          </TabsContent>
        </Tabs>
      ) : (
        renderPositionsTable()
      )}
    </div>
  )
}

export default PositionManager