// RealTimeUpdates - Componente para Atualizações em Tempo Real

import { useState, useEffect, useRef, useCallback } from 'react'
import { Wifi, WifiOff, Activity, Clock, Zap, AlertCircle, CheckCircle } from 'lucide-react'
import { Badge } from '@/components/ui/Badge'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { useArbitrageWebSocket } from '@/hooks/useWebSocket'
import type { ArbitrageOpportunity } from '@/types/arbitrage'

interface RealTimeUpdatesProps {
  className?: string
  onDataUpdate?: (data: ArbitrageOpportunity[]) => void
  showConnectionStatus?: boolean
  showUpdateIndicators?: boolean
  showLatencyInfo?: boolean
}

interface UpdateIndicator {
  id: string
  type: 'new' | 'updated' | 'removed'
  symbol: string
  timestamp: number
  data?: any
}

interface ConnectionMetrics {
  totalUpdates: number
  updatesPerMinute: number
  averageLatency: number
  lastUpdateTime: number
  connectionUptime: number
}

export function RealTimeUpdates({
  className = '',
  onDataUpdate,
  showConnectionStatus = true,
  showUpdateIndicators = true,
  showLatencyInfo = true
}: RealTimeUpdatesProps) {
  const [updates, setUpdates] = useState<UpdateIndicator[]>([])
  const [metrics, setMetrics] = useState<ConnectionMetrics>({
    totalUpdates: 0,
    updatesPerMinute: 0,
    averageLatency: 0,
    lastUpdateTime: 0,
    connectionUptime: 0
  })
  
  const updateCountRef = useRef(0)
  const latencyHistoryRef = useRef<number[]>([])
  const connectionStartRef = useRef<number>(0)
  const updateTimesRef = useRef<number[]>([])
  
  // Callback para atualizações de oportunidades
  const handleOpportunityUpdate = useCallback((opportunities: ArbitrageOpportunity[]) => {
    const now = Date.now()
    updateCountRef.current++
    updateTimesRef.current.push(now)
    
    // Manter apenas últimos 60 segundos para cálculo de RPM
    updateTimesRef.current = updateTimesRef.current.filter(time => now - time < 60000)
    
    // Criar indicadores de atualização
    const newUpdates: UpdateIndicator[] = opportunities.slice(0, 5).map((opp, index) => ({
      id: `update_${now}_${index}`,
      type: Math.random() > 0.7 ? 'new' : 'updated',
      symbol: opp.symbol,
      timestamp: now,
      data: opp
    }))
    
    setUpdates(prev => [...newUpdates, ...prev].slice(0, 20)) // Manter apenas 20 mais recentes
    
    // Atualizar métricas
    setMetrics(prev => ({
      ...prev,
      totalUpdates: updateCountRef.current,
      updatesPerMinute: updateTimesRef.current.length,
      lastUpdateTime: now
    }))
    
    // Callback externo
    onDataUpdate?.(opportunities)
  }, [onDataUpdate])
  
  // Callback para alertas de posição
  const handlePositionAlert = useCallback((alert: any) => {
    const now = Date.now()
    
    const alertUpdate: UpdateIndicator = {
      id: `alert_${now}`,
      type: 'new',
      symbol: alert.symbol || 'ALERT',
      timestamp: now,
      data: alert
    }
    
    setUpdates(prev => [alertUpdate, ...prev].slice(0, 20))
  }, [])
  
  // Callback para status das exchanges
  const handleExchangeStatus = useCallback((status: any) => {
    console.log('Exchange status update:', status)
  }, [])
  
  // Hook WebSocket
  const {
    isConnected,
    isConnecting,
    latency,
    lastError,
    connectionAttempts,
    reconnect,
    requestSnapshot
  } = useArbitrageWebSocket(
    handleOpportunityUpdate,
    handlePositionAlert,
    handleExchangeStatus
  )
  
  // Atualizar latência média
  useEffect(() => {
    if (latency > 0) {
      latencyHistoryRef.current.push(latency)
      if (latencyHistoryRef.current.length > 10) {
        latencyHistoryRef.current = latencyHistoryRef.current.slice(-10)
      }
      
      const avgLatency = latencyHistoryRef.current.reduce((a, b) => a + b, 0) / latencyHistoryRef.current.length
      
      setMetrics(prev => ({
        ...prev,
        averageLatency: Math.round(avgLatency)
      }))
    }
  }, [latency])
  
  // Calcular uptime da conexão
  useEffect(() => {
    if (isConnected && connectionStartRef.current === 0) {
      connectionStartRef.current = Date.now()
    } else if (!isConnected) {
      connectionStartRef.current = 0
    }
    
    const interval = setInterval(() => {
      if (isConnected && connectionStartRef.current > 0) {
        const uptime = Date.now() - connectionStartRef.current
        setMetrics(prev => ({
          ...prev,
          connectionUptime: uptime
        }))
      }
    }, 1000)
    
    return () => clearInterval(interval)
  }, [isConnected])
  
  // Limpar updates antigos
  useEffect(() => {
    const interval = setInterval(() => {
      const cutoff = Date.now() - 30000 // 30 segundos
      setUpdates(prev => prev.filter(update => update.timestamp > cutoff))
    }, 5000)
    
    return () => clearInterval(interval)
  }, [])
  
  // Formatar tempo
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }
  
  // Formatar duração
  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`
    } else {
      return `${seconds}s`
    }
  }
  
  // Status da conexão
  const getConnectionStatus = () => {
    if (isConnecting) {
      return {
        icon: Activity,
        text: 'Conectando...',
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-100',
        pulse: true
      }
    } else if (isConnected) {
      return {
        icon: Wifi,
        text: 'Conectado',
        color: 'text-green-600',
        bgColor: 'bg-green-100',
        pulse: false
      }
    } else if (lastError) {
      return {
        icon: AlertCircle,
        text: 'Erro',
        color: 'text-red-600',
        bgColor: 'bg-red-100',
        pulse: false
      }
    } else {
      return {
        icon: WifiOff,
        text: 'Desconectado',
        color: 'text-gray-600',
        bgColor: 'bg-gray-100',
        pulse: false
      }
    }
  }
  
  const connectionStatus = getConnectionStatus()
  const ConnectionIcon = connectionStatus.icon
  
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Status da Conexão */}
      {showConnectionStatus && (
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-full ${connectionStatus.bgColor} ${connectionStatus.pulse ? 'animate-pulse' : ''}`}>
                <ConnectionIcon className={`h-4 w-4 ${connectionStatus.color}`} />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">Status da Conexão</h3>
                <p className={`text-sm ${connectionStatus.color}`}>
                  {connectionStatus.text}
                  {connectionAttempts > 0 && ` (Tentativa ${connectionAttempts})`}
                </p>
              </div>
            </div>
            
            {!isConnected && !isConnecting && (
              <Button
                onClick={reconnect}
                size="sm"
                className="bg-blue-600 text-white hover:bg-blue-700"
              >
                Reconectar
              </Button>
            )}
          </div>
          
          {lastError && (
            <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
              {lastError}
            </div>
          )}
        </Card>
      )}
      
      {/* Métricas de Performance */}
      {showLatencyInfo && isConnected && (
        <Card className="p-4">
          <h3 className="font-medium text-gray-900 mb-3">Métricas em Tempo Real</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <p className="text-gray-500">Latência</p>
              <p className={`font-medium ${metrics.averageLatency < 100 ? 'text-green-600' : metrics.averageLatency < 300 ? 'text-yellow-600' : 'text-red-600'}`}>
                {metrics.averageLatency}ms
              </p>
            </div>
            <div>
              <p className="text-gray-500">Updates/min</p>
              <p className="font-medium text-blue-600">{metrics.updatesPerMinute}</p>
            </div>
            <div>
              <p className="text-gray-500">Total Updates</p>
              <p className="font-medium text-gray-900">{metrics.totalUpdates.toLocaleString()}</p>
            </div>
            <div>
              <p className="text-gray-500">Uptime</p>
              <p className="font-medium text-green-600">{formatDuration(metrics.connectionUptime)}</p>
            </div>
          </div>
        </Card>
      )}
      
      {/* Indicadores de Atualização */}
      {showUpdateIndicators && updates.length > 0 && (
        <Card className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medium text-gray-900">Atualizações Recentes</h3>
            <Button
              onClick={requestSnapshot}
              variant="ghost"
              size="sm"
              className="text-blue-600 hover:text-blue-700"
            >
              <Zap className="h-3 w-3 mr-1" />
              Atualizar
            </Button>
          </div>
          
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {updates.map((update) => (
              <div
                key={update.id}
                className={`flex items-center justify-between p-2 rounded transition-all duration-500 ${
                  Date.now() - update.timestamp < 3000 
                    ? 'bg-blue-50 border border-blue-200 animate-pulse' 
                    : 'bg-gray-50'
                }`}
              >
                <div className="flex items-center gap-2">
                  <Badge 
                    variant={update.type === 'new' ? 'default' : update.type === 'updated' ? 'secondary' : 'destructive'}
                    className="text-xs"
                  >
                    {update.type === 'new' ? 'NOVO' : update.type === 'updated' ? 'ATUALIZADO' : 'REMOVIDO'}
                  </Badge>
                  <span className="font-medium text-sm">{update.symbol}</span>
                  {update.data?.spreadPercentage && (
                    <span className={`text-xs ${update.data.spreadPercentage > 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {update.data.spreadPercentage > 0 ? '+' : ''}{update.data.spreadPercentage.toFixed(3)}%
                    </span>
                  )}
                </div>
                <div className="flex items-center gap-2 text-xs text-gray-500">
                  <Clock className="h-3 w-3" />
                  {formatTime(update.timestamp)}
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
      
      {/* Última Atualização */}
      {metrics.lastUpdateTime > 0 && (
        <div className="text-center text-xs text-gray-500">
          Última atualização: {formatTime(metrics.lastUpdateTime)}
        </div>
      )}
    </div>
  )
}

// Componente auxiliar para indicador de status simples
export function ConnectionIndicator({ className = '' }: { className?: string }) {
  const { isConnected, isConnecting, latency } = useArbitrageWebSocket()
  
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className={`w-2 h-2 rounded-full ${
        isConnecting 
          ? 'bg-yellow-500 animate-pulse' 
          : isConnected 
            ? 'bg-green-500' 
            : 'bg-red-500'
      }`} />
      <span className="text-xs text-gray-600">
        {isConnecting ? 'Conectando...' : isConnected ? `${latency}ms` : 'Offline'}
      </span>
    </div>
  )
}

// Componente para pulse de atualização
export function UpdatePulse({ 
  isUpdating, 
  className = '' 
}: { 
  isUpdating: boolean
  className?: string 
}) {
  return (
    <div className={`relative ${className}`}>
      {isUpdating && (
        <div className="absolute inset-0 bg-blue-500 opacity-20 rounded animate-ping" />
      )}
      <CheckCircle className={`h-4 w-4 ${isUpdating ? 'text-blue-600' : 'text-green-600'} transition-colors`} />
    </div>
  )
}

export default RealTimeUpdates