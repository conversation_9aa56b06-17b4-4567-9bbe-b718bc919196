// ReportGenerator - Gerador de relatórios completos

import React, { useState } from 'react'
import { Download, FileText, Calendar, Filter, BarChart3 } from 'lucide-react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select'
import { Switch } from '@/components/ui/Switch'
import type { ArbitrageOpportunity, DashboardMetrics } from '@/types/arbitrage'

interface ReportGeneratorProps {
  opportunities: ArbitrageOpportunity[]
  metrics: DashboardMetrics | null
  className?: string
}

interface ReportConfig {
  format: 'json' | 'csv' | 'pdf'
  timeframe: '1h' | '4h' | '1d' | '7d' | '30d'
  includeCharts: boolean
  includeMetrics: boolean
  includeOpportunities: boolean
  includeAnalysis: boolean
  filterByProfitability: string[]
  filterByExchange: string[]
}

export function ReportGenerator({ opportunities, metrics, className = '' }: ReportGeneratorProps) {
  const [config, setConfig] = useState<ReportConfig>({
    format: 'json',
    timeframe: '1d',
    includeCharts: false,
    includeMetrics: true,
    includeOpportunities: true,
    includeAnalysis: true,
    filterByProfitability: ['high', 'medium', 'low'],
    filterByExchange: ['gateio', 'mexc', 'bitget']
  })

  const [isGenerating, setIsGenerating] = useState(false)

  const updateConfig = (key: keyof ReportConfig, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }))
  }

  const generateReport = async () => {
    setIsGenerating(true)
    
    try {
      // Filter opportunities based on config
      const filteredOpportunities = opportunities.filter(opp => {
        const profitabilityMatch = config.filterByProfitability.includes(opp.profitability)
        const exchangeMatch = config.filterByExchange.includes(opp.spotExchange) || 
                             config.filterByExchange.includes(opp.futuresExchange)
        return profitabilityMatch && exchangeMatch
      })

      const reportData = {
        metadata: {
          generatedAt: new Date().toISOString(),
          timeframe: config.timeframe,
          totalOpportunities: filteredOpportunities.length,
          reportConfig: config
        },
        ...(config.includeMetrics && { metrics }),
        ...(config.includeOpportunities && { opportunities: filteredOpportunities }),
        ...(config.includeAnalysis && {
          analysis: {
            profitabilityDistribution: calculateProfitabilityDistribution(filteredOpportunities),
            exchangePerformance: calculateExchangePerformance(filteredOpportunities),
            topSymbols: calculateTopSymbols(filteredOpportunities),
            riskAnalysis: calculateRiskAnalysis(filteredOpportunities)
          }
        })
      }

      // Generate file based on format
      switch (config.format) {
        case 'json':
          downloadJSON(reportData, `arbitrage-report-${config.timeframe}`)
          break
        case 'csv':
          downloadCSV(filteredOpportunities, `arbitrage-opportunities-${config.timeframe}`)
          break
        case 'pdf':
          // PDF generation would require additional library
          alert('PDF generation coming soon!')
          break
      }
    } catch (error) {
      console.error('Error generating report:', error)
      alert('Erro ao gerar relatório')
    } finally {
      setIsGenerating(false)
    }
  }

  const downloadJSON = (data: any, filename: string) => {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${filename}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const downloadCSV = (opportunities: ArbitrageOpportunity[], filename: string) => {
    const headers = [
      'Symbol', 'Spot Exchange', 'Futures Exchange', 'Spot Price', 'Futures Price',
      'Spread %', 'Profitability', 'Volume', 'Type', 'Last Update'
    ]

    const rows = opportunities.map(opp => [
      opp.symbol,
      opp.spotExchange,
      opp.futuresExchange,
      opp.spotPrice.toFixed(4),
      opp.futuresPrice.toFixed(4),
      opp.spreadPercentage.toFixed(3),
      opp.profitability,
      Math.min(opp.spotVolume, opp.futuresVolume).toFixed(0),
      opp.type,
      opp.lastUpdate.toISOString()
    ])

    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${filename}.csv`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const calculateProfitabilityDistribution = (opportunities: ArbitrageOpportunity[]) => {
    return opportunities.reduce((acc, opp) => {
      acc[opp.profitability] = (acc[opp.profitability] || 0) + 1
      return acc
    }, {} as Record<string, number>)
  }

  const calculateExchangePerformance = (opportunities: ArbitrageOpportunity[]) => {
    const performance = opportunities.reduce((acc, opp) => {
      const key = `${opp.spotExchange}-${opp.futuresExchange}`
      if (!acc[key]) acc[key] = []
      acc[key].push(Math.abs(opp.spreadPercentage))
      return acc
    }, {} as Record<string, number[]>)

    return Object.entries(performance).reduce((acc, [key, spreads]) => {
      acc[key] = {
        count: spreads.length,
        avgSpread: spreads.reduce((sum, spread) => sum + spread, 0) / spreads.length,
        maxSpread: Math.max(...spreads),
        minSpread: Math.min(...spreads)
      }
      return acc
    }, {} as Record<string, any>)
  }

  const calculateTopSymbols = (opportunities: ArbitrageOpportunity[]) => {
    const symbolStats = opportunities.reduce((acc, opp) => {
      if (!acc[opp.symbol]) {
        acc[opp.symbol] = { count: 0, totalSpread: 0, totalVolume: 0 }
      }
      acc[opp.symbol].count++
      acc[opp.symbol].totalSpread += Math.abs(opp.spreadPercentage)
      acc[opp.symbol].totalVolume += Math.min(opp.spotVolume, opp.futuresVolume)
      return acc
    }, {} as Record<string, any>)

    return Object.entries(symbolStats)
      .map(([symbol, stats]) => ({
        symbol,
        count: stats.count,
        avgSpread: stats.totalSpread / stats.count,
        totalVolume: stats.totalVolume
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)
  }

  const calculateRiskAnalysis = (opportunities: ArbitrageOpportunity[]) => {
    const spreads = opportunities.map(opp => Math.abs(opp.spreadPercentage))
    const avgSpread = spreads.reduce((sum, spread) => sum + spread, 0) / spreads.length
    const variance = spreads.reduce((sum, spread) => sum + Math.pow(spread - avgSpread, 2), 0) / spreads.length
    const volatility = Math.sqrt(variance)

    return {
      averageSpread: avgSpread,
      volatility: volatility,
      riskScore: Math.min(100, volatility * 20 + (avgSpread * 10)),
      highRiskOpportunities: opportunities.filter(opp => Math.abs(opp.spreadPercentage) > avgSpread + volatility).length
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Gerador de Relatórios</h2>
          <p className="text-gray-600">Exporte dados e análises personalizadas</p>
        </div>
        
        <Button
          onClick={generateReport}
          disabled={isGenerating}
          className="flex items-center gap-2"
        >
          <Download className="h-4 w-4" />
          {isGenerating ? 'Gerando...' : 'Gerar Relatório'}
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Configuration */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Configurações do Relatório
          </h3>

          <div className="space-y-4">
            {/* Format */}
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Formato
              </label>
              <Select value={config.format} onValueChange={(value) => updateConfig('format', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="json">JSON</SelectItem>
                  <SelectItem value="csv">CSV</SelectItem>
                  <SelectItem value="pdf">PDF (Em breve)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Timeframe */}
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Período
              </label>
              <Select value={config.timeframe} onValueChange={(value) => updateConfig('timeframe', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1h">Última Hora</SelectItem>
                  <SelectItem value="4h">Últimas 4 Horas</SelectItem>
                  <SelectItem value="1d">Último Dia</SelectItem>
                  <SelectItem value="7d">Última Semana</SelectItem>
                  <SelectItem value="30d">Último Mês</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Content Options */}
            <div className="space-y-3">
              <label className="text-sm font-medium text-gray-700">Incluir no Relatório</label>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Métricas do Dashboard</span>
                <Switch
                  checked={config.includeMetrics}
                  onCheckedChange={(checked) => updateConfig('includeMetrics', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm">Lista de Oportunidades</span>
                <Switch
                  checked={config.includeOpportunities}
                  onCheckedChange={(checked) => updateConfig('includeOpportunities', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm">Análises Avançadas</span>
                <Switch
                  checked={config.includeAnalysis}
                  onCheckedChange={(checked) => updateConfig('includeAnalysis', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm">Gráficos (PDF apenas)</span>
                <Switch
                  checked={config.includeCharts}
                  onCheckedChange={(checked) => updateConfig('includeCharts', checked)}
                  disabled={config.format !== 'pdf'}
                />
              </div>
            </div>
          </div>
        </Card>

        {/* Preview */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Preview do Relatório
          </h3>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {opportunities.filter(opp => 
                    config.filterByProfitability.includes(opp.profitability)
                  ).length}
                </div>
                <div className="text-sm text-blue-600">Oportunidades</div>
              </div>

              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {config.format.toUpperCase()}
                </div>
                <div className="text-sm text-green-600">Formato</div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium text-sm">Conteúdo Incluído:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                {config.includeMetrics && <li>✓ Métricas do dashboard</li>}
                {config.includeOpportunities && <li>✓ Lista de oportunidades</li>}
                {config.includeAnalysis && <li>✓ Análises avançadas</li>}
                {config.includeCharts && <li>✓ Gráficos e visualizações</li>}
              </ul>
            </div>

            <div className="pt-4 border-t">
              <div className="text-xs text-gray-500">
                Período: {config.timeframe} • Formato: {config.format.toUpperCase()}
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Relatórios Rápidos</span>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                updateConfig('format', 'csv')
                updateConfig('includeOpportunities', true)
                updateConfig('includeMetrics', false)
                updateConfig('includeAnalysis', false)
                generateReport()
              }}
            >
              Exportar Oportunidades (CSV)
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                updateConfig('format', 'json')
                updateConfig('includeMetrics', true)
                updateConfig('includeAnalysis', true)
                generateReport()
              }}
            >
              Relatório Completo (JSON)
            </Button>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default ReportGenerator