// SystemSettings - Configurações avançadas do sistema

import React, { useState, useEffect } from 'react'
import { Settings, Save, RotateCcw, Bell, Shield, Zap, Database } from 'lucide-react'
import { Card } from '@/components/ui/Card'
import { But<PERSON> } from '@/components/ui/Button'
import { Switch } from '@/components/ui/Switch'
import { Slider } from '@/components/ui/Slider'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select'
import { Input } from '@/components/ui/Input'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs'

interface SystemConfig {
  // Performance Settings
  cacheEnabled: boolean
  cacheDuration: number
  maxOpportunities: number
  updateInterval: number
  
  // Alert Settings
  alertsEnabled: boolean
  soundEnabled: boolean
  vibrationEnabled: boolean
  pushEnabled: boolean
  minSpreadForAlert: number
  
  // Display Settings
  theme: 'light' | 'dark' | 'system'
  compactMode: boolean
  showAdvancedMetrics: boolean
  defaultTimeframe: string
  
  // API Settings
  enableRealAPIs: boolean
  rateLimitBuffer: number
  retryAttempts: number
  timeoutDuration: number
  
  // Security Settings
  enableHMAC: boolean
  logLevel: 'error' | 'warn' | 'info' | 'debug'
  dataRetention: number
}

const DEFAULT_CONFIG: SystemConfig = {
  cacheEnabled: true,
  cacheDuration: 5000,
  maxOpportunities: 2000,
  updateInterval: 15000,
  alertsEnabled: true,
  soundEnabled: true,
  vibrationEnabled: true,
  pushEnabled: false,
  minSpreadForAlert: 0.5,
  theme: 'system',
  compactMode: false,
  showAdvancedMetrics: true,
  defaultTimeframe: '4h',
  enableRealAPIs: false,
  rateLimitBuffer: 20,
  retryAttempts: 3,
  timeoutDuration: 10000,
  enableHMAC: true,
  logLevel: 'info',
  dataRetention: 24
}

export function SystemSettings() {
  const [config, setConfig] = useState<SystemConfig>(DEFAULT_CONFIG)
  const [hasChanges, setHasChanges] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  // Load config from localStorage on mount
  useEffect(() => {
    const savedConfig = localStorage.getItem('system-config')
    if (savedConfig) {
      try {
        const parsed = JSON.parse(savedConfig)
        setConfig({ ...DEFAULT_CONFIG, ...parsed })
      } catch (error) {
        console.error('Error loading config:', error)
      }
    }
  }, [])

  const updateConfig = (key: keyof SystemConfig, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }))
    setHasChanges(true)
  }

  const saveConfig = async () => {
    setIsSaving(true)
    try {
      localStorage.setItem('system-config', JSON.stringify(config))
      setHasChanges(false)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Show success message
      console.log('✅ Configurações salvas com sucesso')
    } catch (error) {
      console.error('❌ Erro ao salvar configurações:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const resetConfig = () => {
    setConfig(DEFAULT_CONFIG)
    setHasChanges(true)
  }

  const exportConfig = () => {
    const dataStr = JSON.stringify(config, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'system-config.json'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const importConfig = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const imported = JSON.parse(e.target?.result as string)
        setConfig({ ...DEFAULT_CONFIG, ...imported })
        setHasChanges(true)
      } catch (error) {
        console.error('Error importing config:', error)
        alert('Erro ao importar configuração')
      }
    }
    reader.readAsText(file)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Settings className="h-6 w-6" />
            Configurações do Sistema
          </h2>
          <p className="text-gray-600">Configure o comportamento e performance do sistema</p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={resetConfig}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Resetar
          </Button>
          
          <Button
            onClick={saveConfig}
            disabled={!hasChanges || isSaving}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            {isSaving ? 'Salvando...' : 'Salvar'}
          </Button>
        </div>
      </div>

      {hasChanges && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-yellow-800">
            <Bell className="h-4 w-4" />
            <span className="text-sm font-medium">
              Você tem alterações não salvas. Clique em "Salvar" para aplicar.
            </span>
          </div>
        </div>
      )}

      <Tabs defaultValue="performance" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="alerts">Alertas</TabsTrigger>
          <TabsTrigger value="display">Interface</TabsTrigger>
          <TabsTrigger value="advanced">Avançado</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Configurações de Performance
            </h3>

            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Cache Habilitado</label>
                  <p className="text-xs text-gray-500">Melhora performance mas pode usar mais memória</p>
                </div>
                <Switch
                  checked={config.cacheEnabled}
                  onCheckedChange={(checked) => updateConfig('cacheEnabled', checked)}
                />
              </div>

              <div>
                <label className="text-sm font-medium block mb-2">
                  Duração do Cache: {config.cacheDuration}ms
                </label>
                <Slider
                  value={[config.cacheDuration]}
                  onValueChange={([value]) => updateConfig('cacheDuration', value)}
                  min={1000}
                  max={30000}
                  step={1000}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>1s</span>
                  <span>30s</span>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium block mb-2">
                  Máximo de Oportunidades: {config.maxOpportunities}
                </label>
                <Slider
                  value={[config.maxOpportunities]}
                  onValueChange={([value]) => updateConfig('maxOpportunities', value)}
                  min={100}
                  max={5000}
                  step={100}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>100</span>
                  <span>5000</span>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium block mb-2">
                  Intervalo de Atualização: {config.updateInterval / 1000}s
                </label>
                <Slider
                  value={[config.updateInterval]}
                  onValueChange={([value]) => updateConfig('updateInterval', value)}
                  min={5000}
                  max={60000}
                  step={5000}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>5s</span>
                  <span>60s</span>
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Configurações de Alertas
            </h3>

            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Alertas Habilitados</label>
                  <p className="text-xs text-gray-500">Ativar/desativar todos os alertas</p>
                </div>
                <Switch
                  checked={config.alertsEnabled}
                  onCheckedChange={(checked) => updateConfig('alertsEnabled', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Alertas Sonoros</label>
                  <p className="text-xs text-gray-500">Sons para oportunidades importantes</p>
                </div>
                <Switch
                  checked={config.soundEnabled}
                  onCheckedChange={(checked) => updateConfig('soundEnabled', checked)}
                  disabled={!config.alertsEnabled}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Vibração (Mobile)</label>
                  <p className="text-xs text-gray-500">Vibração em dispositivos móveis</p>
                </div>
                <Switch
                  checked={config.vibrationEnabled}
                  onCheckedChange={(checked) => updateConfig('vibrationEnabled', checked)}
                  disabled={!config.alertsEnabled}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Notificações Push</label>
                  <p className="text-xs text-gray-500">Notificações do navegador</p>
                </div>
                <Switch
                  checked={config.pushEnabled}
                  onCheckedChange={(checked) => updateConfig('pushEnabled', checked)}
                  disabled={!config.alertsEnabled}
                />
              </div>

              <div>
                <label className="text-sm font-medium block mb-2">
                  Spread Mínimo para Alerta: {config.minSpreadForAlert}%
                </label>
                <Slider
                  value={[config.minSpreadForAlert]}
                  onValueChange={([value]) => updateConfig('minSpreadForAlert', value)}
                  min={0.1}
                  max={2.0}
                  step={0.1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>0.1%</span>
                  <span>2.0%</span>
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="display" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Configurações de Interface</h3>

            <div className="space-y-6">
              <div>
                <label className="text-sm font-medium block mb-2">Tema</label>
                <Select value={config.theme} onValueChange={(value) => updateConfig('theme', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">Claro</SelectItem>
                    <SelectItem value="dark">Escuro</SelectItem>
                    <SelectItem value="system">Sistema</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium block mb-2">Timeframe Padrão</label>
                <Select value={config.defaultTimeframe} onValueChange={(value) => updateConfig('defaultTimeframe', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1m">1 Minuto</SelectItem>
                    <SelectItem value="5m">5 Minutos</SelectItem>
                    <SelectItem value="15m">15 Minutos</SelectItem>
                    <SelectItem value="1h">1 Hora</SelectItem>
                    <SelectItem value="4h">4 Horas</SelectItem>
                    <SelectItem value="1d">1 Dia</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Modo Compacto</label>
                  <p className="text-xs text-gray-500">Interface mais densa</p>
                </div>
                <Switch
                  checked={config.compactMode}
                  onCheckedChange={(checked) => updateConfig('compactMode', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Métricas Avançadas</label>
                  <p className="text-xs text-gray-500">Mostrar métricas técnicas detalhadas</p>
                </div>
                <Switch
                  checked={config.showAdvancedMetrics}
                  onCheckedChange={(checked) => updateConfig('showAdvancedMetrics', checked)}
                />
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Configurações Avançadas
            </h3>

            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">APIs Reais</label>
                  <p className="text-xs text-gray-500">Usar APIs reais das exchanges (requer chaves)</p>
                </div>
                <Switch
                  checked={config.enableRealAPIs}
                  onCheckedChange={(checked) => updateConfig('enableRealAPIs', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Autenticação HMAC</label>
                  <p className="text-xs text-gray-500">Usar autenticação HMAC para APIs</p>
                </div>
                <Switch
                  checked={config.enableHMAC}
                  onCheckedChange={(checked) => updateConfig('enableHMAC', checked)}
                />
              </div>

              <div>
                <label className="text-sm font-medium block mb-2">Nível de Log</label>
                <Select value={config.logLevel} onValueChange={(value) => updateConfig('logLevel', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="error">Error</SelectItem>
                    <SelectItem value="warn">Warning</SelectItem>
                    <SelectItem value="info">Info</SelectItem>
                    <SelectItem value="debug">Debug</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium block mb-2">
                  Buffer Rate Limit: {config.rateLimitBuffer}%
                </label>
                <Slider
                  value={[config.rateLimitBuffer]}
                  onValueChange={([value]) => updateConfig('rateLimitBuffer', value)}
                  min={10}
                  max={50}
                  step={5}
                  className="w-full"
                />
              </div>

              <div>
                <label className="text-sm font-medium block mb-2">
                  Tentativas de Retry: {config.retryAttempts}
                </label>
                <Slider
                  value={[config.retryAttempts]}
                  onValueChange={([value]) => updateConfig('retryAttempts', value)}
                  min={1}
                  max={10}
                  step={1}
                  className="w-full"
                />
              </div>

              <div>
                <label className="text-sm font-medium block mb-2">
                  Retenção de Dados: {config.dataRetention}h
                </label>
                <Slider
                  value={[config.dataRetention]}
                  onValueChange={([value]) => updateConfig('dataRetention', value)}
                  min={1}
                  max={168}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>1h</span>
                  <span>7d</span>
                </div>
              </div>
            </div>
          </Card>

          {/* Import/Export */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Database className="h-5 w-5" />
              Backup e Restauração
            </h3>

            <div className="flex gap-4">
              <Button
                variant="outline"
                onClick={exportConfig}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Exportar Configuração
              </Button>

              <div>
                <input
                  type="file"
                  accept=".json"
                  onChange={importConfig}
                  className="hidden"
                  id="import-config"
                />
                <Button
                  variant="outline"
                  onClick={() => document.getElementById('import-config')?.click()}
                  className="flex items-center gap-2"
                >
                  <Database className="h-4 w-4" />
                  Importar Configuração
                </Button>
              </div>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default SystemSettings