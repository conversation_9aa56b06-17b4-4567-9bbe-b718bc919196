import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const cardVariants = cva(
  "rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200",
  {
    variants: {
      variant: {
        default: "border-border",
        // Variantes específicas para arbitragem
        opportunity: "hover:shadow-md cursor-pointer",
        "high-profit": "border-profit-high bg-green-50 dark:bg-green-950 hover:shadow-lg",
        "medium-profit": "border-profit-medium bg-yellow-50 dark:bg-yellow-950 hover:shadow-lg",
        "low-profit": "border-profit-low bg-blue-50 dark:bg-blue-950 hover:shadow-lg",
        stats: "border-l-4 hover:shadow-md",
        alert: "border-red-500 bg-red-50 dark:bg-red-950",
        success: "border-green-500 bg-green-50 dark:bg-green-950",
      },
      size: {
        default: "p-6",
        sm: "p-4",
        lg: "p-8",
        compact: "p-3",
      },
      pulse: {
        none: "",
        slow: "animate-pulse-slow",
        normal: "animate-pulse",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      pulse: "none",
    },
  }
)

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  crossExchange?: boolean
  spread?: number
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, size, pulse, crossExchange, spread, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(cardVariants({ variant, size, pulse, className }))}
        {...props}
      >
        {crossExchange && (
          <div className="mb-2 flex items-center gap-2">
            <Badge variant="outline" size="sm">Cross-Exchange</Badge>
            {spread && (
              <Badge 
                variant={spread > 1 ? "success" : spread > 0.5 ? "warning" : "secondary"} 
                size="sm"
              >
                {spread.toFixed(2)}%
              </Badge>
            )}
          </div>
        )}
        {children}
      </div>
    )
  }
)
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-2xl font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
))
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
))
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center p-6 pt-0", className)}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

// Badge component for cross-exchange indicators
const badgeVariants = cva(
  "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/80",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        success: "bg-green-500 text-white",
        warning: "bg-yellow-500 text-white",
        // Exchange specific badges
        gateio: "bg-exchange-gateio text-white",
        mexc: "bg-exchange-mexc text-white",
        bitget: "bg-exchange-bitget text-white",
      },
      size: {
        default: "px-2.5 py-0.5",
        sm: "px-2 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, size, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant, size }), className)} {...props} />
  )
}

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent, Badge, cardVariants }