import React, { createContext, useContext, useEffect, useState } from 'react'

type Theme = 'dark' | 'light' | 'system'

type ThemeProviderProps = {
  children: React.ReactNode
  defaultTheme?: Theme
  storageKey?: string
}

type ThemeProviderState = {
  theme: Theme
  setTheme: (theme: Theme) => void
  actualTheme: 'dark' | 'light' // The actual theme being applied
  toggleTheme: () => void
  isSystemTheme: boolean
}

const initialState: ThemeProviderState = {
  theme: 'system',
  setTheme: () => null,
  actualTheme: 'light',
  toggleTheme: () => null,
  isSystemTheme: true,
}

const ThemeProviderContext = createContext<ThemeProviderState>(initialState)

export function ThemeProvider({
  children,
  defaultTheme = 'system',
  storageKey = 'crypto-arbitrage-theme',
  ...props
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(
    () => (localStorage.getItem(storageKey) as Theme) || defaultTheme
  )
  const [actualTheme, setActualTheme] = useState<'dark' | 'light'>('light')

  useEffect(() => {
    const root = window.document.documentElement
    root.classList.remove('light', 'dark')

    let resolvedTheme: 'dark' | 'light'

    if (theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)')
        .matches
        ? 'dark'
        : 'light'
      resolvedTheme = systemTheme
    } else {
      resolvedTheme = theme
    }

    root.classList.add(resolvedTheme)
    setActualTheme(resolvedTheme)

    // Add arbitrage-specific CSS variables based on theme
    if (resolvedTheme === 'dark') {
      root.style.setProperty('--profit-high', '142 76% 45%')
      root.style.setProperty('--profit-medium', '45 93% 55%')
      root.style.setProperty('--profit-low', '217 91% 70%')
    } else {
      root.style.setProperty('--profit-high', '142 76% 36%')
      root.style.setProperty('--profit-medium', '45 93% 47%')
      root.style.setProperty('--profit-low', '217 91% 60%')
    }
  }, [theme])

  // Listen for system theme changes
  useEffect(() => {
    if (theme !== 'system') return

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleChange = () => {
      const root = window.document.documentElement
      root.classList.remove('light', 'dark')
      
      const systemTheme = mediaQuery.matches ? 'dark' : 'light'
      root.classList.add(systemTheme)
      setActualTheme(systemTheme)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [theme])

  const handleSetTheme = (newTheme: Theme) => {
    localStorage.setItem(storageKey, newTheme)
    setTheme(newTheme)
  }

  const toggleTheme = () => {
    if (theme === 'system') {
      handleSetTheme('light')
    } else if (theme === 'light') {
      handleSetTheme('dark')
    } else {
      handleSetTheme('system')
    }
  }

  const value = {
    theme,
    setTheme: handleSetTheme,
    actualTheme,
    toggleTheme,
    isSystemTheme: theme === 'system',
  }

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  )
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext)

  if (context === undefined)
    throw new Error('useTheme must be used within a ThemeProvider')

  return context
}

// Theme Toggle Component
import { Moon, Sun, Monitor } from 'lucide-react'
import { Button } from './Button'

export const ThemeToggle: React.FC = () => {
  const { theme, setTheme, actualTheme } = useTheme()

  const themes = [
    { value: 'light', icon: Sun, label: 'Claro' },
    { value: 'dark', icon: Moon, label: 'Escuro' },
    { value: 'system', icon: Monitor, label: 'Sistema' },
  ] as const

  return (
    <div className="flex items-center gap-1 rounded-md border p-1">
      {themes.map(({ value, icon: Icon, label }) => (
        <Button
          key={value}
          variant={theme === value ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setTheme(value)}
          className="h-8 w-8 p-0"
          title={label}
        >
          <Icon className="h-4 w-4" />
        </Button>
      ))}
    </div>
  )
}

// Theme Status Indicator
export const ThemeStatus: React.FC = () => {
  const { theme, actualTheme, isSystemTheme } = useTheme()

  return (
    <div className="flex items-center gap-2 text-sm text-muted-foreground">
      <div className={`w-2 h-2 rounded-full ${
        actualTheme === 'dark' ? 'bg-slate-800' : 'bg-yellow-400'
      }`} />
      <span>
        {isSystemTheme ? `Sistema (${actualTheme})` : theme}
      </span>
    </div>
  )
}