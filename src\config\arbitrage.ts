// Configurações centralizadas do sistema de arbitragem

import type { Exchange, Profitability, ArbitrageType } from '@/types/arbitrage'

// Intervalos de atualização e cache multi-camadas
export const UPDATE_INTERVALS = {
  REAL_TIME: 5000, // 5 segundos - atualizações em tempo real
  CHART_DATA: 30000, // 30 segundos - dados de gráficos
  POSITION_UPDATE: 1000, // 1 segundo - atualização de posições
  HEALTH_CHECK: 10000, // 10 segundos - verificação de saúde
  WEBSOCKET_HEARTBEAT: 30000, // 30 segundos - heartbeat WebSocket
} as const

// Thresholds de validação cross-exchange
export const VALIDATION_THRESHOLDS = {
  MIN_SPREAD_PERCENTAGE: 0.05, // 0.05%
  MIN_VOLUME_USD: 1000, // $1,000
  MAX_DATA_AGE_MS: 15000, // 15 segundos
  MIN_LIQUIDITY_SCORE: 0.3,
  MAX_OPPORTUNITIES: 2000,
} as const

// Configurações de cache multi-camadas inteligente
export const CACHE_CONFIG = {
  L1: {
    TTL: 2000, // 2 segundos - dados críticos (preços, spreads)
    MAX_ENTRIES: 1000,
    DESCRIPTION: 'Cache L1 - Dados críticos em tempo real'
  },
  L2: {
    TTL: 5000, // 5 segundos - dados frequentes (volumes, métricas)
    MAX_ENTRIES: 5000,
    DESCRIPTION: 'Cache L2 - Dados frequentes'
  },
  L3: {
    TTL: 10000, // 10 segundos - dados menos críticos (histórico, estatísticas)
    MAX_ENTRIES: 10000,
    DESCRIPTION: 'Cache L3 - Dados menos críticos'
  },
  CLEANUP_INTERVAL: 60000, // 1 minuto - limpeza automática
  MAX_MEMORY_MB: 100, // Limite de memória para cache
} as const

// Configurações completas das exchanges com autenticação HMAC
export const EXCHANGE_CONFIG = {
  gateio: {
    name: 'Gate.io',
    spotPairs: 2670,
    futuresPairs: 602,
    fee: 0.1, // 0.1%
    rateLimit: {
      requestsPerMinute: 100,
      requestsPerSecond: 10,
    },
    urls: {
      spot: 'https://www.gate.io/trade',
      futures: 'https://www.gate.io/futures_trade',
    },
    api: {
      baseUrl: 'https://api.gateio.ws',
      authType: 'HMAC_SHA512',
      endpoints: {
        spot: '/api/v4/spot/tickers',
        futures: '/api/v4/futures/usdt/tickers',
        symbols: '/api/v4/spot/currency_pairs'
      },
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    },
  },
  mexc: {
    name: 'MEXC',
    spotPairs: 2429,
    futuresPairs: 787,
    fee: 0.1, // 0.1%
    rateLimit: {
      requestsPerMinute: 120,
      requestsPerSecond: 20,
    },
    urls: {
      spot: 'https://www.mexc.com/exchange',
      futures: 'https://futures.mexc.com/exchange',
    },
    api: {
      baseUrl: 'https://api.mexc.com',
      authType: 'HMAC_SHA256',
      endpoints: {
        spot: '/api/v3/ticker/24hr',
        futures: '/api/v1/contract/ticker',
        symbols: '/api/v3/exchangeInfo'
      },
      headers: {
        'Content-Type': 'application/json',
        'X-MEXC-APIKEY': ''
      }
    },
  },
  bitget: {
    name: 'Bitget',
    spotPairs: 799,
    futuresPairs: 513,
    fee: 0.1, // 0.1%
    rateLimit: {
      requestsPerMinute: 200,
      requestsPerSecond: 30,
    },
    urls: {
      spot: 'https://www.bitget.com/spot',
      futures: 'https://www.bitget.com/futures',
    },
    api: {
      baseUrl: 'https://api.bitget.com',
      authType: 'HMAC_SHA256_BASE64',
      endpoints: {
        spot: '/api/spot/v1/market/tickers',
        futures: '/api/mix/v1/market/tickers',
        symbols: '/api/spot/v1/public/products'
      },
      headers: {
        'Content-Type': 'application/json',
        'ACCESS-KEY': '',
        'ACCESS-PASSPHRASE': ''
      }
    },
  },
} as const

// Classificações de rentabilidade
export const PROFITABILITY_CONFIG = {
  HIGH: {
    threshold: 1.0, // >= 1%
    color: 'profit-high',
    label: 'Alta',
    priority: 1,
  },
  MEDIUM: {
    threshold: 0.5, // 0.5% - 1%
    color: 'profit-medium',
    label: 'Média',
    priority: 2,
  },
  LOW: {
    threshold: 0.05, // 0.05% - 0.5%
    color: 'profit-low',
    label: 'Baixa',
    priority: 3,
  },
} as const

// Configurações avançadas de alertas e notificações
export const ALERT_CONFIG = {
  THRESHOLDS: {
    VISUAL_ALERT: 0.5, // 0.5% - alerta visual
    SOUND_ALERT: 1.0, // 1.0% - alerta sonoro
    HIGH_PRIORITY: 2.0, // 2.0% - alta prioridade
    CRITICAL: 5.0, // 5.0% - crítico
  },
  BEHAVIOR: {
    AUTO_REMOVE_DELAY: 3000, // 3 segundos
    MAX_ALERTS: 10,
    COOLDOWN_PERIOD: 5000, // 5 segundos entre alertas similares
    GROUP_SIMILAR: true, // Agrupar alertas similares
  },
  NOTIFICATIONS: {
    SOUND_ENABLED: true,
    VIBRATION_ENABLED: true,
    PUSH_NOTIFICATIONS: true,
    EMAIL_NOTIFICATIONS: false,
  },
  SOUNDS: {
    LOW_PRIORITY: 'notification.mp3',
    MEDIUM_PRIORITY: 'alert.mp3',
    HIGH_PRIORITY: 'urgent.mp3',
    CRITICAL: 'critical.mp3',
  }
} as const

// Configurações de performance otimizada
export const PERFORMANCE_CONFIG = {
  TARGETS: {
    INITIAL_LOAD_TIME: 2000, // 2 segundos - carregamento inicial
    UPDATE_LATENCY: 100, // 100ms - latência de atualizações
    TRANSITION_DURATION: 300, // 300ms - duração de transições
    API_RESPONSE_TIME: 2000, // 2 segundos - timeout de API
  },
  OPTIMIZATION: {
    VIRTUALIZATION_THRESHOLD: 100, // Virtualizar listas > 100 items
    DEBOUNCE_DELAY: 300, // 300ms para filtros
    THROTTLE_DELAY: 100, // 100ms para scroll/resize
    BATCH_SIZE: 50, // Tamanho do batch para processamento
    MAX_CONCURRENT_REQUESTS: 10, // Máximo de requests simultâneos
  },
  MEMORY: {
    MAX_OPPORTUNITIES: 2000, // Máximo de oportunidades em memória
    CLEANUP_INTERVAL: 300000, // 5 minutos - limpeza de memória
    GC_THRESHOLD: 0.8, // 80% - threshold para garbage collection
  }
} as const

// Configurações de WebSocket
export const WEBSOCKET_CONFIG = {
  RECONNECT_DELAY: 5000, // 5 segundos
  MAX_RECONNECT_ATTEMPTS: 10,
  HEARTBEAT_INTERVAL: 30000, // 30 segundos
  MESSAGE_QUEUE_SIZE: 1000,
} as const

// Tipos de arbitragem suportados
export const ARBITRAGE_TYPES = {
  SPOT_FUTURES_CROSS: {
    id: 'spot-futures-cross',
    name: 'Spot vs Futuros Cross-Exchange',
    description: 'Comprar spot na Exchange A e shortar futuros na Exchange B',
  },
  FUTURES_FUTURES_CROSS: {
    id: 'futures-futures-cross',
    name: 'Futuros vs Futuros Cross-Exchange',
    description: 'Long futuros na Exchange A e short futuros na Exchange B',
  },
} as const

// Configurações de desenvolvimento
export const DEV_CONFIG = {
  MOCK_DATA_ENABLED: process.env.NODE_ENV === 'development',
  DEBUG_LOGS: process.env.NODE_ENV === 'development',
  PERFORMANCE_MONITORING: true,
  ERROR_REPORTING: true,
} as const

// Funções removidas - duplicadas abaixo

// Configurações para diferentes categorias de moedas
export const CURRENCY_CATEGORIES = {
  MAJOR: {
    symbols: ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'SOL/USDT'],
    minVolume: 10000, // $10,000
    minSpread: 0.03, // 0.03%
    priority: 1,
  },
  POPULAR: {
    symbols: ['DOGE/USDT', 'MATIC/USDT', 'DOT/USDT', 'AVAX/USDT', 'LINK/USDT'],
    minVolume: 5000, // $5,000
    minSpread: 0.05, // 0.05%
    priority: 2,
  },
  ALTCOINS: {
    symbols: [], // Será preenchido dinamicamente
    minVolume: 1000, // $1,000
    minSpread: 0.1, // 0.1%
    priority: 3,
  }
} as const

// Configurações de descoberta automática de símbolos
export const SYMBOL_DISCOVERY = {
  ENABLED: true,
  UPDATE_INTERVAL: 3600000, // 1 hora
  MIN_VOLUME_24H: 100000, // $100,000 volume 24h mínimo
  MAX_SYMBOLS_PER_EXCHANGE: 1000,
  BLACKLIST: ['BULL', 'BEAR', '3L', '3S'], // Tokens alavancados
  WHITELIST_QUOTE_ASSETS: ['USDT', 'USDC', 'BUSD'],
} as const

// Utilitários para configuração
export function getExchangeConfig(exchange: Exchange) {
  return EXCHANGE_CONFIG[exchange]
}

export function classifyProfitability(spreadPercentage: number): Profitability {
  // Só considerar spreads positivos - spreads negativos ou zero não são rentáveis
  if (spreadPercentage <= 0) {
    return 'NONE'
  } else if (spreadPercentage >= PROFITABILITY_CONFIG.HIGH.threshold) {
    return 'HIGH'
  } else if (spreadPercentage >= PROFITABILITY_CONFIG.MEDIUM.threshold) {
    return 'MEDIUM'
  } else if (spreadPercentage >= PROFITABILITY_CONFIG.LOW.threshold) {
    return 'LOW'
  }
  return 'NONE'
}

export function isValidCrossExchangeOpportunity(
  spotExchange: string,
  futuresExchange: string,
  spreadPercentage: number,
  volume: number,
  dataAge: number
): boolean {
  return (
    spotExchange !== futuresExchange && // Deve ser cross-exchange
    spreadPercentage > 0 && // Só spreads positivos são válidos
    spreadPercentage >= VALIDATION_THRESHOLDS.MIN_SPREAD_PERCENTAGE &&
    volume >= VALIDATION_THRESHOLDS.MIN_VOLUME_USD &&
    dataAge <= VALIDATION_THRESHOLDS.MAX_DATA_AGE_MS
  )
}

export function getCurrencyCategory(symbol: string) {
  if (CURRENCY_CATEGORIES.MAJOR.symbols.includes(symbol as any)) return 'MAJOR'
  if (CURRENCY_CATEGORIES.POPULAR.symbols.includes(symbol as any)) return 'POPULAR'
  return 'ALTCOINS'
}

export function getMinRequirements(symbol: string) {
  const category = getCurrencyCategory(symbol)
  return CURRENCY_CATEGORIES[category]
}

export function shouldProcessSymbol(symbol: string, volume24h: number): boolean {
  const requirements = getMinRequirements(symbol)
  return volume24h >= requirements.minVolume
}

// Configuração de URLs para redirecionamento
export function getExchangeUrls(exchange: Exchange, symbol: string) {
  const config = EXCHANGE_CONFIG[exchange]
  const baseSymbol = symbol.replace('/', '_')
  
  return {
    spot: `${config.urls.spot}/${baseSymbol}`,
    futures: `${config.urls.futures}/${baseSymbol}`
  }
}

// Configuração de taxas por exchange
export function getExchangeFee(exchange: Exchange): number {
  return EXCHANGE_CONFIG[exchange].fee
}

// Configuração de rate limiting
export function getRateLimit(exchange: Exchange) {
  return EXCHANGE_CONFIG[exchange].rateLimit
}