// Exchange Endpoints - URLs Reais das APIs das Exchanges

export interface ExchangeEndpoints {
    name: string
    baseUrl: string
    endpoints: {
        spot: {
            tickers: string
            orderbook: string
            trades: string
        }
        futures: {
            tickers: string
            orderbook: string
            trades: string
            fundingRate: string
        }
    }
    rateLimit: {
        requestsPerSecond: number
        requestsPerMinute: number
    }
    pairs: {
        spot: number
        futures: number
        total: number
    }
}

/**
 * Configurações das APIs reais das exchanges
 */
export const EXCHANGE_ENDPOINTS: Record<string, ExchangeEndpoints> = {
    gateio: {
        name: 'Gate.io',
        baseUrl: 'https://api.gateio.ws',
        endpoints: {
            spot: {
                tickers: '/api/v4/spot/tickers',
                orderbook: '/api/v4/spot/order_book',
                trades: '/api/v4/spot/trades'
            },
            futures: {
                tickers: '/api/v4/futures/usdt/tickers',
                orderbook: '/api/v4/futures/usdt/order_book',
                trades: '/api/v4/futures/usdt/trades',
                fundingRate: '/api/v4/futures/usdt/funding_rate'
            }
        },
        rateLimit: {
            requestsPerSecond: 10,
            requestsPerMinute: 600
        },
        pairs: {
            spot: 2670,
            futures: 602,
            total: 3272
        }
    },

    mexc: {
        name: 'MEXC',
        baseUrl: 'https://api.mexc.com',
        endpoints: {
            spot: {
                tickers: '/api/v3/ticker/24hr',
                orderbook: '/api/v3/depth',
                trades: '/api/v3/trades'
            },
            futures: {
                tickers: '/api/v1/contract/ticker',
                orderbook: '/api/v1/contract/depth',
                trades: '/api/v1/contract/deals',
                fundingRate: '/api/v1/contract/funding_rate'
            }
        },
        rateLimit: {
            requestsPerSecond: 20,
            requestsPerMinute: 1200
        },
        pairs: {
            spot: 2429,
            futures: 787,
            total: 3216
        }
    },

    bitget: {
        name: 'Bitget',
        baseUrl: 'https://api.bitget.com',
        endpoints: {
            spot: {
                tickers: '/api/spot/v1/market/tickers',
                orderbook: '/api/spot/v1/market/depth',
                trades: '/api/spot/v1/market/fills'
            },
            futures: {
                tickers: '/api/mix/v1/market/tickers',
                orderbook: '/api/mix/v1/market/depth',
                trades: '/api/mix/v1/market/fills',
                fundingRate: '/api/mix/v1/market/current-fundRate'
            }
        },
        rateLimit: {
            requestsPerSecond: 10,
            requestsPerMinute: 600
        },
        pairs: {
            spot: 799,
            futures: 513,
            total: 1312
        }
    }
}

/**
 * Obter configuração de uma exchange específica
 */
export function getExchangeEndpoints(exchange: string): ExchangeEndpoints {
    const config = EXCHANGE_ENDPOINTS[exchange]
    if (!config) {
        throw new Error(`Exchange ${exchange} not supported`)
    }
    return config
}

/**
 * Obter total de pares de todas as exchanges
 */
export function getTotalPairs(): {
    spot: number
    futures: number
    total: number
    byExchange: Record<string, { spot: number; futures: number; total: number }>
} {
    const exchanges = Object.keys(EXCHANGE_ENDPOINTS)
    let totalSpot = 0
    let totalFutures = 0
    const byExchange: Record<string, { spot: number; futures: number; total: number }> = {}

    exchanges.forEach(exchange => {
        const config = EXCHANGE_ENDPOINTS[exchange]
        totalSpot += config.pairs.spot
        totalFutures += config.pairs.futures
        byExchange[exchange] = {
            spot: config.pairs.spot,
            futures: config.pairs.futures,
            total: config.pairs.total
        }
    })

    return {
        spot: totalSpot,
        futures: totalFutures,
        total: totalSpot + totalFutures,
        byExchange
    }
}

/**
 * Verificar se uma exchange suporta um tipo de mercado
 */
export function supportsMarketType(exchange: string, marketType: 'spot' | 'futures'): boolean {
    const config = EXCHANGE_ENDPOINTS[exchange]
    if (!config) return false

    return config.pairs[marketType] > 0
}

/**
 * Obter URL completa para um endpoint
 */
export function getFullEndpointUrl(
    exchange: string,
    marketType: 'spot' | 'futures',
    endpoint: 'tickers' | 'orderbook' | 'trades' | 'fundingRate'
): string {
    const config = getExchangeEndpoints(exchange)
    const endpointPath = config.endpoints[marketType][endpoint as keyof typeof config.endpoints.spot]

    if (!endpointPath) {
        throw new Error(`Endpoint ${endpoint} not available for ${exchange} ${marketType}`)
    }

    return `${config.baseUrl}${endpointPath}`
}

/**
 * Estatísticas das exchanges
 */
export const EXCHANGE_STATS = {
    totalExchanges: Object.keys(EXCHANGE_ENDPOINTS).length,
    totalPairs: getTotalPairs().total,
    supportedMarkets: ['spot', 'futures'] as const,
    authMethods: {
        gateio: 'HMAC SHA512',
        mexc: 'HMAC SHA256',
        bitget: 'HMAC SHA256 + Base64'
    } as const
}

export default EXCHANGE_ENDPOINTS