// Configurações específicas para produção

export const PRODUCTION_CONFIG = {
  // URLs de produção das APIs
  API_BASE_URL: process.env.NODE_ENV === 'production' 
    ? 'https://api.crypto-arbitrage.com' 
    : 'http://localhost:5001',
  
  WEBSOCKET_URL: process.env.NODE_ENV === 'production'
    ? 'wss://ws.crypto-arbitrage.com'
    : 'ws://localhost:5001',

  // Configurações de performance para produção
  PERFORMANCE: {
    ENABLE_ANALYTICS: process.env.NODE_ENV === 'production',
    ENABLE_ERROR_REPORTING: process.env.NODE_ENV === 'production',
    ENABLE_PERFORMANCE_MONITORING: true,
    CACHE_STRATEGY: 'aggressive', // 'conservative' | 'aggressive'
  },

  // Configurações de segurança
  SECURITY: {
    ENABLE_HTTPS_ONLY: process.env.NODE_ENV === 'production',
    ENABLE_CSP: process.env.NODE_ENV === 'production',
    API_RATE_LIMITING: process.env.NODE_ENV === 'production',
  },

  // Configurações de logging
  LOGGING: {
    LEVEL: process.env.NODE_ENV === 'production' ? 'error' : 'debug',
    ENABLE_CONSOLE: process.env.NODE_ENV !== 'production',
    ENABLE_REMOTE: process.env.NODE_ENV === 'production',
  },

  // Configurações de features
  FEATURES: {
    ENABLE_MOCK_DATA: process.env.NODE_ENV === 'development',
    ENABLE_DEBUG_PANEL: process.env.NODE_ENV === 'development',
    ENABLE_PERFORMANCE_OVERLAY: process.env.NODE_ENV === 'development',
  },

  // Configurações de build
  BUILD: {
    ENABLE_SOURCE_MAPS: process.env.NODE_ENV !== 'production',
    ENABLE_BUNDLE_ANALYZER: false,
    OPTIMIZE_CHUNKS: process.env.NODE_ENV === 'production',
  }
} as const

// Função para obter configuração baseada no ambiente
export function getEnvironmentConfig() {
  return {
    isDevelopment: process.env.NODE_ENV === 'development',
    isProduction: process.env.NODE_ENV === 'production',
    isTest: process.env.NODE_ENV === 'test',
    ...PRODUCTION_CONFIG
  }
}