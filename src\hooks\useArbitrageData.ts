// useArbitrageData - Hook Otimizado para Dados de Arbitragem com APIs Reais + WebSocket

import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect, useRef, useCallback, useState } from 'react'
import { DataCollector } from '@/services/DataCollector'
import { AlertSystem } from '@/services/AlertSystem'
import type { ArbitrageOpportunity, DashboardMetrics } from '@/types/arbitrage'

interface UseArbitrageDataOptions {
  enabled?: boolean
  refetchInterval?: number
  staleTime?: number
  cacheTime?: number
  enableAlerts?: boolean
  enableRealTime?: boolean
  enableWebSocket?: boolean // OTIMIZAÇÃO: Nova opção para WebSocket
}

// OTIMIZAÇÃO: Interface para mensagens WebSocket
interface WebSocketMessage {
  type: 'opportunity' | 'heartbeat' | 'error' | 'metrics'
  data: any
  timestamp: number
}

// OTIMIZAÇÃO: Status de conexão WebSocket
type WebSocketStatus = 'connecting' | 'connected' | 'disconnected' | 'error'

interface UseArbitrageDataReturn {
  opportunities: ArbitrageOpportunity[]
  metrics: DashboardMetrics | null
  isLoading: boolean
  isError: boolean
  error: Error | null
  isRefetching: boolean
  refetch: () => void
  lastUpdate: Date | null
  dataAge: number
  connectionStatus: 'connected' | 'connecting' | 'disconnected'
  performanceMetrics: {
    responseTime: number
    cacheHitRate: number
    errorRate: number
  }
  // OTIMIZAÇÃO: Novos campos para WebSocket
  webSocketStatus: WebSocketStatus
  webSocketMetrics: {
    connectedClients: number
    messagesSent: number
    messagesReceived: number
    averageLatency: number
  }
  realTimeUpdates: number // Contador de atualizações em tempo real
}

const DEFAULT_OPTIONS: UseArbitrageDataOptions = {
  enabled: true,
  refetchInterval: 1000,  // OTIMIZAÇÃO: Reduzido de 15s para 1s
  staleTime: 500,         // OTIMIZAÇÃO: Reduzido de 2s para 500ms
  cacheTime: 300000,      // 5 minutos
  enableAlerts: true,
  enableRealTime: true,
  enableWebSocket: true   // OTIMIZAÇÃO: WebSocket habilitado por padrão
}

export function useArbitrageData(
  options: UseArbitrageDataOptions = {}
): UseArbitrageDataReturn {
  const opts = { ...DEFAULT_OPTIONS, ...options }
  const queryClient = useQueryClient()

  // Instâncias dos services
  const dataCollector = DataCollector.getInstance()
  const alertSystem = AlertSystem.getInstance()

  // Refs para controle
  const previousOpportunities = useRef<ArbitrageOpportunity[]>([])
  const lastAlertCheck = useRef<number>(0)
  const performanceMetrics = useRef({
    responseTime: 0,
    cacheHitRate: 0.8,
    errorRate: 0
  })

  // OTIMIZAÇÃO: Estados para WebSocket
  const [webSocketStatus, setWebSocketStatus] = useState<WebSocketStatus>('disconnected')
  const [webSocketMetrics, setWebSocketMetrics] = useState({
    connectedClients: 0,
    messagesSent: 0,
    messagesReceived: 0,
    averageLatency: 0
  })
  const [realTimeUpdates, setRealTimeUpdates] = useState(0)
  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // OTIMIZAÇÃO: Função para conectar WebSocket
  const connectWebSocket = useCallback(() => {
    if (!opts.enableWebSocket) return

    const wsUrl = import.meta.env.VITE_BACKEND_URL?.replace('http', 'ws') || 'ws://localhost:5001'

    try {
      setWebSocketStatus('connecting')
      const ws = new WebSocket(wsUrl)
      wsRef.current = ws

      ws.onopen = () => {
        console.log('⚡ WebSocket conectado')
        setWebSocketStatus('connected')

        // Limpar timeout de reconexão
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current)
          reconnectTimeoutRef.current = null
        }
      }

      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)

          switch (message.type) {
            case 'opportunity':
              // Atualizar cache do React Query com nova oportunidade
              queryClient.setQueryData(['arbitrage-data'], (oldData: any) => {
                if (!oldData) return oldData

                const newOpportunity = message.data
                const existingOpportunities = oldData.opportunities || []

                // Adicionar nova oportunidade no topo
                const updatedOpportunities = [
                  newOpportunity,
                  ...existingOpportunities.filter((opp: any) => opp.id !== newOpportunity.id)
                ].slice(0, 1000) // Manter apenas últimas 1000

                setRealTimeUpdates(prev => prev + 1)

                return {
                  ...oldData,
                  opportunities: updatedOpportunities
                }
              })
              break

            case 'metrics':
              setWebSocketMetrics(message.data)
              break

            case 'heartbeat':
              // Responder ao heartbeat
              if (ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                  type: 'heartbeat',
                  data: { clientTime: Date.now() },
                  timestamp: Date.now()
                }))
              }
              break
          }
        } catch (error) {
          console.error('❌ Erro ao processar mensagem WebSocket:', error)
        }
      }

      ws.onclose = () => {
        console.log('🔌 WebSocket desconectado')
        setWebSocketStatus('disconnected')

        // Tentar reconectar após 5 segundos
        if (opts.enableWebSocket) {
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log('🔄 Tentando reconectar WebSocket...')
            connectWebSocket()
          }, 5000)
        }
      }

      ws.onerror = (error) => {
        console.error('❌ Erro WebSocket:', error)
        setWebSocketStatus('error')
      }

    } catch (error) {
      console.error('❌ Erro ao conectar WebSocket:', error)
      setWebSocketStatus('error')
    }
  }, [opts.enableWebSocket, queryClient])

  // OTIMIZAÇÃO: Desconectar WebSocket
  const disconnectWebSocket = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close()
      wsRef.current = null
    }

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    setWebSocketStatus('disconnected')
  }, [])

  // OTIMIZAÇÃO: Gerenciar conexão WebSocket
  useEffect(() => {
    if (opts.enableWebSocket) {
      connectWebSocket()
    }

    // Cleanup na desmontagem
    return () => {
      disconnectWebSocket()
    }
  }, [opts.enableWebSocket, connectWebSocket, disconnectWebSocket])

  // Query principal para coleta de dados
  const {
    data,
    isLoading,
    isError,
    error,
    isRefetching,
    refetch,
    dataUpdatedAt
  } = useQuery({
    queryKey: ['arbitrage-data'],
    queryFn: async (): Promise<{
      opportunities: ArbitrageOpportunity[]
      metrics: DashboardMetrics
    }> => {
      const startTime = Date.now()
      
      try {
        console.log('🔄 useArbitrageData: Coletando dados das APIs reais...')
        
        // Coletar oportunidades usando APIs reais
        const opportunities = await dataCollector.collectAllData()
        
        // Verificar se MDT está presente
        const mdtOpportunities = opportunities.filter(opp => opp.symbol === 'MDT/USDT');
        console.log(`🔍 useArbitrageData: MDT encontradas: ${mdtOpportunities.length}`);
        if (mdtOpportunities.length > 0) {
          console.log(`🎯 useArbitrageData: MDT detalhes:`, mdtOpportunities[0]);
        }
        
        // Calcular métricas do dashboard
        const metrics = dataCollector.calculateDashboardMetrics(opportunities)
        
        // Atualizar métricas de performance
        const responseTime = Date.now() - startTime
        performanceMetrics.current.responseTime = responseTime
        
        console.log(`✅ useArbitrageData: ${opportunities.length} oportunidades coletadas em ${responseTime}ms`)
        
        return { opportunities, metrics }
        
      } catch (error) {
        console.error('❌ useArbitrageData: Erro na coleta:', error)
        
        // Atualizar taxa de erro
        performanceMetrics.current.errorRate = Math.min(
          performanceMetrics.current.errorRate + 0.1, 
          1
        )
        
        throw error
      }
    },
    enabled: opts.enabled,
    refetchInterval: opts.refetchInterval,
    staleTime: opts.staleTime,
    cacheTime: opts.cacheTime,
    retry: 2, // Reduzido de 3 para 2
    retryDelay: (attemptIndex) => Math.min(500 * 2 ** attemptIndex, 5000), // Mais rápido
    refetchOnWindowFocus: false, // Evitar refetch desnecessário
    refetchOnReconnect: true,
    onSuccess: (data) => {
      // Reduzir taxa de erro em caso de sucesso
      performanceMetrics.current.errorRate = Math.max(
        performanceMetrics.current.errorRate - 0.05,
        0
      )
    },
    onError: (error) => {
      console.error('🚨 useArbitrageData: Query error:', error)
    }
  })

  // Detectar novas oportunidades para alertas
  const checkForNewOpportunities = useCallback((
    newOpportunities: ArbitrageOpportunity[]
  ) => {
    if (!opts.enableAlerts) return
    
    const now = Date.now()
    
    // Throttle de alertas (máximo 1 verificação por 5 segundos)
    if (now - lastAlertCheck.current < 5000) return
    
    lastAlertCheck.current = now
    
    // Verificar oportunidades de alta rentabilidade
    const highProfitOpportunities = newOpportunities.filter(
      opp => opp.profitability === 'HIGH' && Math.abs(opp.spreadPercentage) > 1.0
    )
    
    // Verificar novas oportunidades (que não estavam na lista anterior)
    const previousIds = new Set(previousOpportunities.current.map(opp => opp.id))
    const newHighProfitOpportunities = highProfitOpportunities.filter(
      opp => !previousIds.has(opp.id)
    )
    
    // Disparar alertas para novas oportunidades de alta rentabilidade
    newHighProfitOpportunities.forEach(opportunity => {
      alertSystem.checkCrossExchangeSpreadAlert(opportunity)
    })
    
    if (newHighProfitOpportunities.length > 0) {
      console.log(`🔔 useArbitrageData: ${newHighProfitOpportunities.length} novos alertas disparados`)
    }
    
    // Atualizar referência
    previousOpportunities.current = newOpportunities
    
  }, [opts.enableAlerts, alertSystem])

  // Effect para verificar alertas quando dados mudam
  useEffect(() => {
    if (data?.opportunities && opts.enableAlerts) {
      checkForNewOpportunities(data.opportunities)
    }
  }, [data?.opportunities, checkForNewOpportunities, opts.enableAlerts])

  // Effect para limpeza de cache antigo
  useEffect(() => {
    const cleanup = () => {
      // Limpar queries antigas (mais de 10 minutos)
      queryClient.removeQueries({
        queryKey: ['arbitrage-data'],
        exact: false,
        predicate: (query) => {
          const dataUpdatedAt = query.state.dataUpdatedAt
          return dataUpdatedAt && Date.now() - dataUpdatedAt > 600000 // 10 minutos
        }
      })
      
      // Limpar histórico de alertas
      alertSystem.cleanupAlertHistory()
    }

    const interval = setInterval(cleanup, 300000) // 5 minutos
    return () => clearInterval(interval)
  }, [queryClient, alertSystem])

  // Calcular idade dos dados
  const dataAge = dataUpdatedAt ? Date.now() - dataUpdatedAt : 0
  const lastUpdate = dataUpdatedAt ? new Date(dataUpdatedAt) : null

  // Determinar status de conexão
  const connectionStatus: 'connected' | 'connecting' | 'disconnected' = 
    isLoading ? 'connecting' :
    isError ? 'disconnected' :
    'connected'

  // Função de refresh manual
  const manualRefetch = useCallback(() => {
    console.log('🔄 useArbitrageData: Refresh manual solicitado')
    refetch()
  }, [refetch])

  return {
    opportunities: data?.opportunities || [],
    metrics: data?.metrics || null,
    isLoading,
    isError,
    error: error as Error | null,
    isRefetching,
    refetch: manualRefetch,
    lastUpdate,
    dataAge,
    connectionStatus,
    performanceMetrics: performanceMetrics.current,
    // OTIMIZAÇÃO: Novos campos WebSocket
    webSocketStatus,
    webSocketMetrics,
    realTimeUpdates
  }
}

// Hook auxiliar para métricas de performance
export function useArbitragePerformance() {
  const queryClient = useQueryClient()
  
  const getQueryStats = useCallback(() => {
    const queries = queryClient.getQueryCache().getAll()
    const arbitrageQueries = queries.filter(q => 
      q.queryKey[0] === 'arbitrage-data'
    )
    
    const totalQueries = arbitrageQueries.length
    const successfulQueries = arbitrageQueries.filter(q => 
      q.state.status === 'success'
    ).length
    const errorQueries = arbitrageQueries.filter(q => 
      q.state.status === 'error'
    ).length
    
    return {
      totalQueries,
      successfulQueries,
      errorQueries,
      successRate: totalQueries > 0 ? successfulQueries / totalQueries : 0,
      errorRate: totalQueries > 0 ? errorQueries / totalQueries : 0
    }
  }, [queryClient])
  
  const clearCache = useCallback(() => {
    queryClient.removeQueries({ queryKey: ['arbitrage-data'] })
    console.log('🧹 useArbitrageData: Cache limpo')
  }, [queryClient])
  
  return {
    getQueryStats,
    clearCache
  }
}

// Hook para controle de tempo real
export function useRealTimeArbitrage(enabled: boolean = true) {
  const queryClient = useQueryClient()
  
  const enableRealTime = useCallback(() => {
    queryClient.setQueryDefaults(['arbitrage-data'], {
      refetchInterval: 5000, // 5 segundos para tempo real
      staleTime: 1000 // 1 segundo
    })
    console.log('⚡ useArbitrageData: Modo tempo real ativado')
  }, [queryClient])
  
  const disableRealTime = useCallback(() => {
    queryClient.setQueryDefaults(['arbitrage-data'], {
      refetchInterval: 30000, // 30 segundos normal
      staleTime: 5000 // 5 segundos
    })
    console.log('🔄 useArbitrageData: Modo normal ativado')
  }, [queryClient])
  
  useEffect(() => {
    if (enabled) {
      enableRealTime()
    } else {
      disableRealTime()
    }
    
    return () => {
      disableRealTime() // Cleanup
    }
  }, [enabled, enableRealTime, disableRealTime])
  
  return {
    enableRealTime,
    disableRealTime
  }
}

export default useArbitrageData