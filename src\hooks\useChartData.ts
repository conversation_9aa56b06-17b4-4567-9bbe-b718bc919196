// useChartData - Hook para Dados Históricos de Gráficos Cross-Exchange

import { useQuery } from '@tanstack/react-query'
import { useMemo } from 'react'
import type { ArbitrageOpportunity } from '@/types/arbitrage'

export interface ChartDataPoint {
  timestamp: number
  datetime: string
  spotPrice: number
  futuresPrice: number
  spread: number
  spreadPercentage: number
  volume: number
  profitability: 'HIGH' | 'MEDIUM' | 'LOW'
}

export interface ChartMetrics {
  averageSpread: number
  maxSpread: number
  minSpread: number
  spreadVolatility: number
  totalVolume: number
  openings: number
  closings: number
  inversions: number
  trendDirection: 'up' | 'down' | 'sideways'
  correlationScore: number
}

export interface UseChartDataReturn {
  data: ChartDataPoint[]
  metrics: ChartMetrics
  isLoading: boolean
  isError: boolean
  error: Error | null
  timeframe: string
  symbol: string
  spotExchange: string
  futuresExchange: string
}

interface UseChartDataOptions {
  symbol: string
  spotExchange: string
  futuresExchange: string
  timeframe?: '1m' | '5m' | '15m' | '1h' | '4h' | '1d'
  enabled?: boolean
}

// Função para gerar dados históricos simulados (será substituída por dados reais)
function generateHistoricalData(
  symbol: string,
  spotExchange: string,
  futuresExchange: string,
  timeframe: string,
  baseSpotPrice: number = 45000,
  baseFuturesPrice: number = 45150
): ChartDataPoint[] {
  const points: ChartDataPoint[] = []
  const now = Date.now()
  
  // Determinar intervalo e quantidade de pontos baseado no timeframe
  const intervals = {
    '1m': { interval: 60000, points: 60 }, // 1 hora
    '5m': { interval: 300000, points: 72 }, // 6 horas
    '15m': { interval: 900000, points: 96 }, // 24 horas
    '1h': { interval: 3600000, points: 168 }, // 7 dias
    '4h': { interval: 14400000, points: 180 }, // 30 dias
    '1d': { interval: 86400000, points: 90 } // 90 dias
  }
  
  const config = intervals[timeframe as keyof typeof intervals] || intervals['1h']
  
  // Gerar pontos históricos
  for (let i = config.points; i >= 0; i--) {
    const timestamp = now - (i * config.interval)
    
    // Simular variação de preços com tendência
    const timeProgress = (config.points - i) / config.points
    const trendFactor = Math.sin(timeProgress * Math.PI * 2) * 0.02 // 2% de variação
    const randomFactor = (Math.random() - 0.5) * 0.01 // 1% de ruído
    
    const spotPrice = baseSpotPrice * (1 + trendFactor + randomFactor)
    const futuresPrice = baseFuturesPrice * (1 + trendFactor + randomFactor * 0.8)
    
    const spread = futuresPrice - spotPrice
    const spreadPercentage = (spread / spotPrice) * 100
    
    // Só considerar spreads positivos
    if (spreadPercentage <= 0) {
      continue
    }
    
    const volume = Math.random() * 100000 + 50000 // Volume entre 50k-150k
    
    // Determinar profitabilidade
    const profitability: 'HIGH' | 'MEDIUM' | 'LOW' = 
      spreadPercentage > 1.0 ? 'HIGH' :
      spreadPercentage > 0.5 ? 'MEDIUM' : 'LOW'
    
    points.push({
      timestamp,
      datetime: new Date(timestamp).toISOString(),
      spotPrice,
      futuresPrice,
      spread,
      spreadPercentage,
      volume,
      profitability
    })
  }
  
  return points
}

// Função para calcular métricas dos dados históricos
function calculateChartMetrics(data: ChartDataPoint[]): ChartMetrics {
  if (data.length === 0) {
    return {
      averageSpread: 0,
      maxSpread: 0,
      minSpread: 0,
      spreadVolatility: 0,
      totalVolume: 0,
      openings: 0,
      closings: 0,
      inversions: 0,
      trendDirection: 'sideways',
      correlationScore: 0
    }
  }
  
  const spreads = data.map(d => d.spreadPercentage)
  const volumes = data.map(d => d.volume)
  
  // Métricas básicas
  const averageSpread = spreads.reduce((sum, s) => sum + s, 0) / spreads.length
  const maxSpread = Math.max(...spreads)
  const minSpread = Math.min(...spreads)
  const totalVolume = volumes.reduce((sum, v) => sum + v, 0)
  
  // Volatilidade (desvio padrão)
  const variance = spreads.reduce((sum, s) => sum + Math.pow(s - averageSpread, 2), 0) / spreads.length
  const spreadVolatility = Math.sqrt(variance)
  
  // Contar eventos (openings, closings, inversions)
  let openings = 0
  let closings = 0
  let inversions = 0
  
  for (let i = 1; i < data.length; i++) {
    const prev = data[i - 1]
    const curr = data[i]
    
    // Opening: spread aumenta significativamente
    if (Math.abs(curr.spreadPercentage) > Math.abs(prev.spreadPercentage) + 0.1) {
      openings++
    }
    
    // Closing: spread diminui significativamente
    if (Math.abs(curr.spreadPercentage) < Math.abs(prev.spreadPercentage) - 0.1) {
      closings++
    }
    
    // Inversion: spread muda de sinal
    if ((prev.spreadPercentage > 0 && curr.spreadPercentage < 0) ||
        (prev.spreadPercentage < 0 && curr.spreadPercentage > 0)) {
      inversions++
    }
  }
  
  // Determinar direção da tendência
  const firstHalf = data.slice(0, Math.floor(data.length / 2))
  const secondHalf = data.slice(Math.floor(data.length / 2))
  
  const firstHalfAvg = firstHalf.reduce((sum, d) => sum + Math.abs(d.spreadPercentage), 0) / firstHalf.length
  const secondHalfAvg = secondHalf.reduce((sum, d) => sum + Math.abs(d.spreadPercentage), 0) / secondHalf.length
  
  const trendDirection: 'up' | 'down' | 'sideways' = 
    secondHalfAvg > firstHalfAvg + 0.1 ? 'up' :
    secondHalfAvg < firstHalfAvg - 0.1 ? 'down' : 'sideways'
  
  // Correlação entre spot e futures (simplificada)
  const spotPrices = data.map(d => d.spotPrice)
  const futuresPrices = data.map(d => d.futuresPrice)
  
  const spotAvg = spotPrices.reduce((sum, p) => sum + p, 0) / spotPrices.length
  const futuresAvg = futuresPrices.reduce((sum, p) => sum + p, 0) / futuresPrices.length
  
  let numerator = 0
  let spotVariance = 0
  let futuresVariance = 0
  
  for (let i = 0; i < data.length; i++) {
    const spotDiff = spotPrices[i] - spotAvg
    const futuresDiff = futuresPrices[i] - futuresAvg
    
    numerator += spotDiff * futuresDiff
    spotVariance += spotDiff * spotDiff
    futuresVariance += futuresDiff * futuresDiff
  }
  
  const correlationScore = numerator / Math.sqrt(spotVariance * futuresVariance) || 0
  
  return {
    averageSpread,
    maxSpread,
    minSpread,
    spreadVolatility,
    totalVolume,
    openings,
    closings,
    inversions,
    trendDirection,
    correlationScore
  }
}

export function useChartData({
  symbol,
  spotExchange,
  futuresExchange,
  timeframe = '1h',
  enabled = true
}: UseChartDataOptions): UseChartDataReturn {
  
  // Query para dados históricos
  const {
    data: rawData,
    isLoading,
    isError,
    error
  } = useQuery({
    queryKey: ['chart-data', symbol, spotExchange, futuresExchange, timeframe],
    queryFn: async (): Promise<ChartDataPoint[]> => {
      console.log(`📊 useChartData: Coletando dados históricos para ${symbol} (${spotExchange} → ${futuresExchange})`)
      
      // TODO: Substituir por chamada real para API de dados históricos
      // Por enquanto, usar dados simulados baseados em preços realistas
      const baseSpotPrice = symbol.includes('BTC') ? 45000 : 
                           symbol.includes('ETH') ? 2500 : 
                           symbol.includes('BNB') ? 300 : 1
      
      const baseFuturesPrice = baseSpotPrice * (1 + (Math.random() - 0.5) * 0.01) // Pequena diferença
      
      const data = generateHistoricalData(
        symbol,
        spotExchange,
        futuresExchange,
        timeframe,
        baseSpotPrice,
        baseFuturesPrice
      )
      
      console.log(`✅ useChartData: ${data.length} pontos históricos gerados`)
      
      return data
    },
    enabled,
    staleTime: 300000, // 5 minutos
    cacheTime: 600000, // 10 minutos
    retry: 2,
    retryDelay: 2000
  })

  // Calcular métricas dos dados
  const metrics = useMemo(() => {
    return calculateChartMetrics(rawData || [])
  }, [rawData])

  return {
    data: rawData || [],
    metrics,
    isLoading,
    isError,
    error: error as Error | null,
    timeframe,
    symbol,
    spotExchange,
    futuresExchange
  }
}

// Hook auxiliar para múltiplos símbolos
export function useMultipleChartData(
  symbols: Array<{
    symbol: string
    spotExchange: string
    futuresExchange: string
  }>,
  timeframe: '1m' | '5m' | '15m' | '1h' | '4h' | '1d' = '1h'
) {
  const queries = symbols.map(({ symbol, spotExchange, futuresExchange }) =>
    useChartData({
      symbol,
      spotExchange,
      futuresExchange,
      timeframe,
      enabled: true
    })
  )

  const isLoading = queries.some(q => q.isLoading)
  const isError = queries.some(q => q.isError)
  const errors = queries.filter(q => q.error).map(q => q.error)

  return {
    data: queries.map(q => ({
      symbol: q.symbol,
      spotExchange: q.spotExchange,
      futuresExchange: q.futuresExchange,
      data: q.data,
      metrics: q.metrics
    })),
    isLoading,
    isError,
    errors,
    timeframe
  }
}

// Hook para comparação de spreads entre diferentes pares
export function useSpreadComparison(
  opportunities: ArbitrageOpportunity[],
  timeframe: '1h' | '4h' | '1d' = '1h'
) {
  const topOpportunities = useMemo(() => {
    return opportunities
      .filter(opp => opp.profitability === 'HIGH')
      .sort((a, b) => Math.abs(b.spreadPercentage) - Math.abs(a.spreadPercentage))
      .slice(0, 5) // Top 5
  }, [opportunities])

  const chartData = useMultipleChartData(
    topOpportunities.map(opp => ({
      symbol: opp.symbol,
      spotExchange: opp.spotExchange,
      futuresExchange: opp.futuresExchange
    })),
    timeframe
  )

  return {
    ...chartData,
    opportunities: topOpportunities
  }
}

export default useChartData