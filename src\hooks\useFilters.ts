// useFilters - Hook para Gerenciamento de Estado de Filtros

import { useState, useEffect, useMemo, useCallback } from 'react'
import type { ArbitrageOpportunity } from '@/types/arbitrage'
import type { FilterState } from '@/components/opportunities/AdvancedFilters'

const STORAGE_KEY = 'crypto-arbitrage-filters'

const DEFAULT_FILTERS: FilterState = {
  searchTerm: '',
  spotExchange: 'all',
  futuresExchange: 'all',
  type: 'all',
  profitability: 'all',
  spreadRange: [0, 100], // Aumentado de 10% para 100% para capturar todas as oportunidades
  volumeRange: [0, 1000000000], // Aumentado para 1B para capturar todos os volumes possíveis
  priceRange: [0, 1000000], // Aumentado para 1M para capturar todos os preços
  timeframe: '1h',
  autoRefresh: true,
  realTimeUpdates: true,
  notifications: false,
  showOnlyHighVolume: false,
  showOnlyRecentData: false, // Temporariamente desabilitado
  hideStaleData: false // Temporariamente desabilitado
}

interface UseFiltersOptions {
  persistToStorage?: boolean
  debounceMs?: number
}

interface UseFiltersReturn {
  filters: FilterState
  setFilters: (filters: FilterState) => void
  updateFilter: <K extends keyof FilterState>(key: K, value: FilterState[K]) => void
  resetFilters: () => void
  clearFilters: () => void
  filteredOpportunities: ArbitrageOpportunity[]
  activeFiltersCount: number
  exchanges: {
    spot: string[]
    futures: string[]
  }
  isFiltering: boolean
}

export function useFilters(
  opportunities: ArbitrageOpportunity[],
  options: UseFiltersOptions = {}
): UseFiltersReturn {
  const { 
    persistToStorage = true, 
    debounceMs = 300 
  } = options

  // Estado dos filtros
  const [filters, setFiltersState] = useState<FilterState>(() => {
    if (persistToStorage && typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(STORAGE_KEY)
        if (stored) {
          const parsed = JSON.parse(stored)
          // Forçar atualização dos ranges para valores mais altos
          const updatedFilters = { 
            ...DEFAULT_FILTERS, 
            ...parsed,
            volumeRange: [0, 1000000000], // Forçar volume range para 1B
            spreadRange: [0, 100], // Forçar spread range alto
            priceRange: [0, 1000000] // Forçar price range alto
          }
          console.log('🔧 useFilters: Filtros carregados do localStorage e atualizados:', updatedFilters);
          return updatedFilters
        }
      } catch (error) {
        console.warn('Erro ao carregar filtros do localStorage:', error)
      }
    }
    console.log('🔧 useFilters: Usando filtros padrão:', DEFAULT_FILTERS);
    return DEFAULT_FILTERS
  })

  // Estado de debounce para busca
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(filters.searchTerm)
  const [isFiltering, setIsFiltering] = useState(false)

  // Debounce otimizado para o termo de busca
  useEffect(() => {
    setIsFiltering(true)
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(filters.searchTerm)
      setIsFiltering(false)
    }, debounceMs)

    return () => clearTimeout(timer)
  }, [filters.searchTerm, debounceMs])

  // Persistir filtros no localStorage
  useEffect(() => {
    if (persistToStorage && typeof window !== 'undefined') {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(filters))
      } catch (error) {
        console.warn('Erro ao salvar filtros no localStorage:', error)
      }
    }
  }, [filters, persistToStorage])

  // Extrair exchanges únicas das oportunidades
  const exchanges = useMemo(() => {
    const spotExchanges = new Set<string>()
    const futuresExchanges = new Set<string>()

    opportunities.forEach(opp => {
      spotExchanges.add(opp.spotExchange)
      futuresExchanges.add(opp.futuresExchange)
    })

    return {
      spot: Array.from(spotExchanges).sort(),
      futures: Array.from(futuresExchanges).sort()
    }
  }, [opportunities])

  // Contar filtros ativos
  const activeFiltersCount = useMemo(() => {
    let count = 0
    
    if (debouncedSearchTerm.trim()) count++
    if (filters.spotExchange !== 'all') count++
    if (filters.futuresExchange !== 'all') count++
    if (filters.type !== 'all') count++
    if (filters.profitability !== 'all') count++
    if (filters.spreadRange[0] > 0 || filters.spreadRange[1] < 10) count++
    if (filters.volumeRange[0] > 0 || filters.volumeRange[1] < 1000000) count++
    if (filters.priceRange[0] > 0 || filters.priceRange[1] < 100000) count++
    if (filters.showOnlyHighVolume) count++
    if (!filters.showOnlyRecentData) count++
    if (!filters.hideStaleData) count++
    
    return count
  }, [filters, debouncedSearchTerm])

  // Aplicar filtros às oportunidades
  const filteredOpportunities = useMemo(() => {
    let filtered = opportunities
    
    // Log inicial para MDT
    const mdtBefore = opportunities.filter(opp => opp.symbol === 'MDT/USDT');
    console.log(`🔍 useFilters: MDT antes dos filtros: ${mdtBefore.length}`);
    if (mdtBefore.length > 0) {
      console.log(`🎯 useFilters: MDT original:`, mdtBefore[0]);
      const mdt = mdtBefore[0];
      const mdtVolume = Math.min(mdt.spotVolume, mdt.futuresVolume);
      const mdtSpread = Math.abs(mdt.spreadPercentage);
      const mdtPrice = (mdt.spotPrice + mdt.futuresPrice) / 2;
      console.log(`📊 useFilters: MDT valores - Volume: ${mdtVolume.toLocaleString()}, Spread: ${mdtSpread.toFixed(3)}%, Preço: $${mdtPrice.toFixed(4)}`);
      console.log(`🔧 useFilters: Filtros ativos - Volume: ${filters.volumeRange[0].toLocaleString()}-${filters.volumeRange[1].toLocaleString()}, Spread: ${filters.spreadRange[0]}-${filters.spreadRange[1]}%, Preço: $${filters.priceRange[0]}-$${filters.priceRange[1].toLocaleString()}`);
    }

    // Filtro de busca (debounced)
    if (debouncedSearchTerm.trim()) {
      const searchLower = debouncedSearchTerm.toLowerCase()
      filtered = filtered.filter(opp => 
        opp.symbol.toLowerCase().includes(searchLower) ||
        opp.baseAsset.toLowerCase().includes(searchLower) ||
        opp.quoteAsset.toLowerCase().includes(searchLower)
      )
    }

    // Filtro de spot exchange
    if (filters.spotExchange !== 'all') {
      filtered = filtered.filter(opp => opp.spotExchange === filters.spotExchange)
    }

    // Filtro de futures exchange
    if (filters.futuresExchange !== 'all') {
      filtered = filtered.filter(opp => opp.futuresExchange === filters.futuresExchange)
    }

    // Filtro de tipo
    if (filters.type !== 'all') {
      filtered = filtered.filter(opp => opp.type === filters.type)
    }

    // Filtro de rentabilidade
    if (filters.profitability !== 'all') {
      filtered = filtered.filter(opp => opp.profitability === filters.profitability)
    }

    // Filtro de spread range
    const beforeSpreadFilter = filtered.length;
    filtered = filtered.filter(opp => {
      const absSpread = Math.abs(opp.spreadPercentage)
      const passes = absSpread >= filters.spreadRange[0] && absSpread <= filters.spreadRange[1];
      
      // Log específico para MDT
      if (opp.symbol === 'MDT/USDT' && !passes) {
        console.log(`❌ useFilters: MDT REMOVIDA pelo filtro de spread! Spread: ${absSpread.toFixed(3)}%, Range: ${filters.spreadRange[0]}-${filters.spreadRange[1]}%`);
      }
      
      return passes;
    })
    const afterSpreadFilter = filtered.length;
    if (beforeSpreadFilter !== afterSpreadFilter) {
      console.log(`🔍 useFilters: Filtro de spread removeu ${beforeSpreadFilter - afterSpreadFilter} oportunidades`);
    }

    // Filtro de volume range
    const beforeVolumeFilter = filtered.length;
    filtered = filtered.filter(opp => {
      const minVolume = Math.min(opp.spotVolume, opp.futuresVolume)
      const passes = minVolume >= filters.volumeRange[0] && minVolume <= filters.volumeRange[1];
      
      // Log específico para MDT
      if (opp.symbol === 'MDT/USDT' && !passes) {
        console.log(`❌ useFilters: MDT REMOVIDA pelo filtro de volume! Volume: ${minVolume.toLocaleString()}, Range: ${filters.volumeRange[0].toLocaleString()}-${filters.volumeRange[1].toLocaleString()}`);
      }
      
      return passes;
    })
    const afterVolumeFilter = filtered.length;
    if (beforeVolumeFilter !== afterVolumeFilter) {
      console.log(`🔍 useFilters: Filtro de volume removeu ${beforeVolumeFilter - afterVolumeFilter} oportunidades`);
    }

    // Filtro de price range
    const beforePriceFilter = filtered.length;
    filtered = filtered.filter(opp => {
      const avgPrice = (opp.spotPrice + opp.futuresPrice) / 2
      const passes = avgPrice >= filters.priceRange[0] && avgPrice <= filters.priceRange[1];
      
      // Log específico para MDT
      if (opp.symbol === 'MDT/USDT' && !passes) {
        console.log(`❌ useFilters: MDT REMOVIDA pelo filtro de preço! Preço médio: $${avgPrice.toFixed(4)}, Range: $${filters.priceRange[0]}-$${filters.priceRange[1].toLocaleString()}`);
      }
      
      return passes;
    })
    const afterPriceFilter = filtered.length;
    if (beforePriceFilter !== afterPriceFilter) {
      console.log(`🔍 useFilters: Filtro de preço removeu ${beforePriceFilter - afterPriceFilter} oportunidades`);
    }

    // Filtro de alto volume
    if (filters.showOnlyHighVolume) {
      const volumeThreshold = 50000 // $50k volume mínimo
      const beforeHighVolumeFilter = filtered.length;
      filtered = filtered.filter(opp => {
        const minVolume = Math.min(opp.spotVolume, opp.futuresVolume)
        const passes = minVolume >= volumeThreshold;
        
        // Log específico para MDT
        if (opp.symbol === 'MDT/USDT' && !passes) {
          console.log(`❌ useFilters: MDT REMOVIDA pelo filtro de alto volume! Volume: ${minVolume.toLocaleString()}, Threshold: ${volumeThreshold.toLocaleString()}`);
        }
        
        return passes;
      })
      const afterHighVolumeFilter = filtered.length;
      if (beforeHighVolumeFilter !== afterHighVolumeFilter) {
        console.log(`🔍 useFilters: Filtro de alto volume removeu ${beforeHighVolumeFilter - afterHighVolumeFilter} oportunidades`);
      }
    }

    // Filtro de dados recentes
    if (filters.showOnlyRecentData) {
      const maxAge = 60000 // 1 minuto
      const beforeRecentFilter = filtered.length;
      filtered = filtered.filter(opp => {
        const passes = opp.dataAge <= maxAge;
        
        // Log específico para MDT
        if (opp.symbol === 'MDT/USDT' && !passes) {
          console.log(`❌ useFilters: MDT REMOVIDA pelo filtro de dados recentes! Data age: ${opp.dataAge}ms, Max: ${maxAge}ms`);
        }
        
        return passes;
      })
      const afterRecentFilter = filtered.length;
      if (beforeRecentFilter !== afterRecentFilter) {
        console.log(`🔍 useFilters: Filtro de dados recentes removeu ${beforeRecentFilter - afterRecentFilter} oportunidades`);
      }
    }

    // Filtro de dados obsoletos
    if (filters.hideStaleData) {
      const beforeStaleFilter = filtered.length;
      filtered = filtered.filter(opp => {
        const passes = opp.isValid !== false;
        
        // Log específico para MDT
        if (opp.symbol === 'MDT/USDT' && !passes) {
          console.log(`❌ useFilters: MDT REMOVIDA pelo filtro de dados obsoletos! isValid: ${opp.isValid}`);
        }
        
        return passes;
      })
      const afterStaleFilter = filtered.length;
      if (beforeStaleFilter !== afterStaleFilter) {
        console.log(`🔍 useFilters: Filtro de dados obsoletos removeu ${beforeStaleFilter - afterStaleFilter} oportunidades`);
      }
    }

    // Log final para MDT
    const mdtAfter = filtered.filter(opp => opp.symbol === 'MDT/USDT');
    console.log(`🔍 useFilters: MDT após filtros: ${mdtAfter.length}`);
    if (mdtAfter.length > 0) {
      console.log(`🎯 useFilters: MDT filtrada:`, mdtAfter[0]);
    } else if (mdtBefore.length > 0) {
      console.log(`❌ useFilters: MDT foi REMOVIDA pelos filtros!`);
      console.log(`🔍 useFilters: Filtros ativos:`, {
        searchTerm: debouncedSearchTerm,
        spotExchange: filters.spotExchange,
        futuresExchange: filters.futuresExchange,
        type: filters.type,
        profitability: filters.profitability,
        spreadRange: filters.spreadRange,
        volumeRange: filters.volumeRange,
        priceRange: filters.priceRange
      });
    }
    
    return filtered
  }, [opportunities, filters, debouncedSearchTerm])

  // Função para atualizar filtros
  const setFilters = useCallback((newFilters: FilterState) => {
    setFiltersState(newFilters)
  }, [])

  // Função para atualizar um filtro específico
  const updateFilter = useCallback(<K extends keyof FilterState>(
    key: K, 
    value: FilterState[K]
  ) => {
    setFiltersState(prev => ({
      ...prev,
      [key]: value
    }))
  }, [])

  // Função para resetar filtros
  const resetFilters = useCallback(() => {
    setFiltersState(DEFAULT_FILTERS)
  }, [])

  // Função para limpar filtros (alias para resetFilters)
  const clearFilters = useCallback(() => {
    resetFilters()
  }, [resetFilters])

  return {
    filters,
    setFilters,
    updateFilter,
    resetFilters,
    clearFilters,
    filteredOpportunities,
    activeFiltersCount,
    exchanges,
    isFiltering
  }
}

export default useFilters

// Hook auxiliar para filtros rápidos
export function useQuickFilters(opportunities: ArbitrageOpportunity[]) {
  const filterByProfitability = useCallback((profitability: 'HIGH' | 'MEDIUM' | 'LOW') => {
    return opportunities.filter(opp => opp.profitability === profitability)
  }, [opportunities])

  const filterByType = useCallback((type: 'spot-futures-cross' | 'futures-futures-cross') => {
    return opportunities.filter(opp => opp.type === type)
  }, [opportunities])

  const filterByExchange = useCallback((exchange: string, side: 'spot' | 'futures') => {
    if (side === 'spot') {
      return opportunities.filter(opp => opp.spotExchange === exchange)
    } else {
      return opportunities.filter(opp => opp.futuresExchange === exchange)
    }
  }, [opportunities])

  const filterBySpreadRange = useCallback((min: number, max: number) => {
    return opportunities.filter(opp => {
      const absSpread = Math.abs(opp.spreadPercentage)
      return absSpread >= min && absSpread <= max
    })
  }, [opportunities])

  const getTopOpportunities = useCallback((count: number = 10) => {
    return opportunities
      .sort((a, b) => Math.abs(b.spreadPercentage) - Math.abs(a.spreadPercentage))
      .slice(0, count)
  }, [opportunities])

  return {
    filterByProfitability,
    filterByType,
    filterByExchange,
    filterBySpreadRange,
    getTopOpportunities
  }
}