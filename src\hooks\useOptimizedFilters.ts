// OTIMIZAÇÃO: Hook para filtros com debounce e memoização

import { useState, useMemo, useCallback, useEffect } from 'react'
import type { ArbitrageOpportunity } from '@/types/arbitrage'

export interface FilterState {
  searchTerm: string
  spotExchange: string
  futuresExchange: string
  minSpread: number
  maxSpread: number
  minVolume: number
  profitability: 'all' | 'high' | 'medium' | 'low'
  type: 'all' | 'spot-futures-cross' | 'futures-futures-cross'
  sortBy: 'spread' | 'volume' | 'symbol' | 'age'
  sortOrder: 'asc' | 'desc'
}

const DEFAULT_FILTERS: FilterState = {
  searchTerm: '',
  spotExchange: '',
  futuresExchange: '',
  minSpread: 0,
  maxSpread: 100,
  minVolume: 0,
  profitability: 'all',
  type: 'all',
  sortBy: 'spread',
  sortOrder: 'desc'
}

interface UseOptimizedFiltersOptions {
  debounceMs?: number
  enableLocalStorage?: boolean
  storageKey?: string
}

export function useOptimizedFilters(
  opportunities: ArbitrageOpportunity[],
  options: UseOptimizedFiltersOptions = {}
) {
  const {
    debounceMs = 300,
    enableLocalStorage = true,
    storageKey = 'arbitrage-filters'
  } = options

  // OTIMIZAÇÃO: Estado dos filtros com localStorage
  const [filters, setFilters] = useState<FilterState>(() => {
    if (enableLocalStorage && typeof window !== 'undefined') {
      try {
        const saved = localStorage.getItem(storageKey)
        if (saved) {
          return { ...DEFAULT_FILTERS, ...JSON.parse(saved) }
        }
      } catch (error) {
        console.warn('Erro ao carregar filtros do localStorage:', error)
      }
    }
    return DEFAULT_FILTERS
  })

  // OTIMIZAÇÃO: Estado para filtros com debounce
  const [debouncedFilters, setDebouncedFilters] = useState(filters)

  // OTIMIZAÇÃO: Debounce dos filtros
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedFilters(filters)
    }, debounceMs)

    return () => clearTimeout(timer)
  }, [filters, debounceMs])

  // OTIMIZAÇÃO: Salvar filtros no localStorage
  useEffect(() => {
    if (enableLocalStorage && typeof window !== 'undefined') {
      try {
        localStorage.setItem(storageKey, JSON.stringify(filters))
      } catch (error) {
        console.warn('Erro ao salvar filtros no localStorage:', error)
      }
    }
  }, [filters, enableLocalStorage, storageKey])

  // OTIMIZAÇÃO: Função para atualizar filtros
  const updateFilter = useCallback(<K extends keyof FilterState>(
    key: K,
    value: FilterState[K]
  ) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }, [])

  // OTIMIZAÇÃO: Função para resetar filtros
  const resetFilters = useCallback(() => {
    setFilters(DEFAULT_FILTERS)
  }, [])

  // OTIMIZAÇÃO: Oportunidades filtradas com memoização
  const filteredOpportunities = useMemo(() => {
    const startTime = performance.now()
    
    let filtered = opportunities.filter(opp => {
      // Filtro de busca por símbolo
      if (debouncedFilters.searchTerm) {
        const searchLower = debouncedFilters.searchTerm.toLowerCase()
        if (!opp.symbol.toLowerCase().includes(searchLower)) {
          return false
        }
      }

      // Filtro por exchange spot
      if (debouncedFilters.spotExchange && opp.spotExchange !== debouncedFilters.spotExchange) {
        return false
      }

      // Filtro por exchange futures
      if (debouncedFilters.futuresExchange && opp.futuresExchange !== debouncedFilters.futuresExchange) {
        return false
      }

      // Filtro por spread mínimo
      const absSpread = Math.abs(opp.spreadPercentage)
      if (absSpread < debouncedFilters.minSpread) {
        return false
      }

      // Filtro por spread máximo
      if (absSpread > debouncedFilters.maxSpread) {
        return false
      }

      // Filtro por volume mínimo
      const volume = opp.spotVolume || 0
      if (volume < debouncedFilters.minVolume) {
        return false
      }

      // Filtro por rentabilidade
      if (debouncedFilters.profitability !== 'all' && opp.profitability !== debouncedFilters.profitability) {
        return false
      }

      // Filtro por tipo
      if (debouncedFilters.type !== 'all' && opp.type !== debouncedFilters.type) {
        return false
      }

      return true
    })

    // OTIMIZAÇÃO: Ordenação otimizada
    filtered.sort((a, b) => {
      let comparison = 0

      switch (debouncedFilters.sortBy) {
        case 'spread':
          comparison = Math.abs(b.spreadPercentage) - Math.abs(a.spreadPercentage)
          break
        case 'volume':
          comparison = (b.spotVolume || 0) - (a.spotVolume || 0)
          break
        case 'symbol':
          comparison = a.symbol.localeCompare(b.symbol)
          break
        case 'age':
          comparison = (a.dataAge || 0) - (b.dataAge || 0)
          break
      }

      return debouncedFilters.sortOrder === 'asc' ? -comparison : comparison
    })

    const processingTime = performance.now() - startTime
    
    if (processingTime > 10) {
      console.log(`⚡ Filtros processados: ${filtered.length}/${opportunities.length} em ${processingTime.toFixed(2)}ms`)
    }

    return filtered
  }, [opportunities, debouncedFilters])

  // OTIMIZAÇÃO: Estatísticas dos filtros memoizadas
  const filterStats = useMemo(() => {
    const total = opportunities.length
    const filtered = filteredOpportunities.length
    const filterRate = total > 0 ? (filtered / total) * 100 : 0

    // Estatísticas por exchange
    const exchangeStats = filteredOpportunities.reduce((acc, opp) => {
      const key = `${opp.spotExchange}-${opp.futuresExchange}`
      acc[key] = (acc[key] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // Estatísticas por rentabilidade
    const profitabilityStats = filteredOpportunities.reduce((acc, opp) => {
      acc[opp.profitability || 'unknown'] = (acc[opp.profitability || 'unknown'] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return {
      total,
      filtered,
      filterRate: Math.round(filterRate * 100) / 100,
      exchangeStats,
      profitabilityStats
    }
  }, [opportunities.length, filteredOpportunities])

  // OTIMIZAÇÃO: Opções únicas para selects memoizadas
  const filterOptions = useMemo(() => {
    const spotExchanges = new Set<string>()
    const futuresExchanges = new Set<string>()
    const symbols = new Set<string>()

    opportunities.forEach(opp => {
      if (opp.spotExchange) spotExchanges.add(opp.spotExchange)
      if (opp.futuresExchange) futuresExchanges.add(opp.futuresExchange)
      if (opp.symbol) symbols.add(opp.symbol)
    })

    return {
      spotExchanges: Array.from(spotExchanges).sort(),
      futuresExchanges: Array.from(futuresExchanges).sort(),
      symbols: Array.from(symbols).sort()
    }
  }, [opportunities])

  // OTIMIZAÇÃO: Função para aplicar filtro rápido
  const applyQuickFilter = useCallback((preset: 'high-profit' | 'recent' | 'high-volume' | 'cross-exchange') => {
    switch (preset) {
      case 'high-profit':
        setFilters(prev => ({
          ...prev,
          minSpread: 1.0,
          profitability: 'high',
          sortBy: 'spread',
          sortOrder: 'desc'
        }))
        break
      case 'recent':
        setFilters(prev => ({
          ...prev,
          sortBy: 'age',
          sortOrder: 'asc'
        }))
        break
      case 'high-volume':
        setFilters(prev => ({
          ...prev,
          minVolume: 10000,
          sortBy: 'volume',
          sortOrder: 'desc'
        }))
        break
      case 'cross-exchange':
        setFilters(prev => ({
          ...prev,
          type: 'spot-futures-cross'
        }))
        break
    }
  }, [])

  return {
    filters,
    debouncedFilters,
    filteredOpportunities,
    filterStats,
    filterOptions,
    updateFilter,
    resetFilters,
    applyQuickFilter,
    isFiltering: JSON.stringify(filters) !== JSON.stringify(DEFAULT_FILTERS)
  }
}

export default useOptimizedFilters
