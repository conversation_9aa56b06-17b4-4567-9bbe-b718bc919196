// useProjectStructure - Hook para Análise de Estrutura do Projeto

import { useState, useCallback } from 'react'

export interface ProjectStructureAnalysis {
  timestamp: number
  overallScore: number
  status: 'excellent' | 'good' | 'needs_improvement' | 'critical'
  structure: StructureAnalysis
  dependencies: DependencyAnalysis
  codeQuality: CodeQualityAnalysis
  organization: OrganizationAnalysis
  recommendations: string[]
}

export interface StructureAnalysis {
  totalFiles: number
  totalDirectories: number
  filesByType: Record<string, number>
  directoryStructure: DirectoryNode[]
  missingDirectories: string[]
  unexpectedFiles: string[]
  structureScore: number
}

export interface DirectoryNode {
  name: string
  path: string
  type: 'directory' | 'file'
  size?: number
  children?: DirectoryNode[]
  purpose?: string
}

export interface DependencyAnalysis {
  totalDependencies: number
  productionDeps: number
  devDependencies: number
  outdatedDeps: string[]
  vulnerabilities: string[]
  unusedDeps: string[]
  dependencyScore: number
  packageJsonExists: boolean
  lockFileExists: boolean
}

export interface CodeQualityAnalysis {
  totalLinesOfCode: number
  codeFiles: number
  testFiles: number
  testCoverage: number
  duplicatedCode: number
  complexityScore: number
  maintainabilityIndex: number
  technicalDebt: string[]
  codeQualityScore: number
}

export interface OrganizationAnalysis {
  followsConventions: boolean
  hasProperNaming: boolean
  hasDocumentation: boolean
  hasTests: boolean
  hasTypeScript: boolean
  hasLinting: boolean
  hasFormatting: boolean
  organizationScore: number
  issues: string[]
}

export const useProjectStructure = () => {
  const [analysis, setAnalysis] = useState<ProjectStructureAnalysis | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const analyzeProject = useCallback(async () => {
    setLoading(true)
    setError(null)
    
    try {
      // Em produção, aqui chamaria o ProjectStructureAnalyzer real
      // const analyzer = new ProjectStructureAnalyzer()
      // const result = await analyzer.analyzeProject()
      
      // Por enquanto, simular análise com dados realistas
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const mockAnalysis: ProjectStructureAnalysis = {
        timestamp: Date.now(),
        overallScore: 87,
        status: 'good',
        structure: {
          totalFiles: 156,
          totalDirectories: 28,
          filesByType: {
            '.ts': 45,
            '.tsx': 38,
            '.js': 12,
            '.jsx': 8,
            '.json': 15,
            '.md': 18,
            '.css': 6,
            '.html': 3,
            '': 11
          },
          directoryStructure: [
            {
              name: 'src',
              path: 'src',
              type: 'directory',
              purpose: 'Source code',
              children: [
                { name: 'components', path: 'src/components', type: 'directory', purpose: 'React components' },
                { name: 'hooks', path: 'src/hooks', type: 'directory', purpose: 'Custom React hooks' },
                { name: 'services', path: 'src/services', type: 'directory', purpose: 'Business logic and API calls' },
                { name: 'types', path: 'src/types', type: 'directory', purpose: 'TypeScript type definitions' },
                { name: 'utils', path: 'src/utils', type: 'directory', purpose: 'Utility functions' },
                { name: '__tests__', path: 'src/__tests__', type: 'directory', purpose: 'Test files' }
              ]
            },
            {
              name: 'public',
              path: 'public',
              type: 'directory',
              purpose: 'Static assets'
            },
            {
              name: '.kiro',
              path: '.kiro',
              type: 'directory',
              purpose: 'Kiro configuration'
            }
          ],
          missingDirectories: ['docs', 'scripts'],
          unexpectedFiles: ['TASK_18.1_COMPLETA.md', 'FASE_4_COMPLETA.md'],
          structureScore: 85
        },
        dependencies: {
          totalDependencies: 42,
          productionDeps: 28,
          devDependencies: 14,
          outdatedDeps: ['react-query@3.39.0'],
          vulnerabilities: [],
          unusedDeps: ['lodash'],
          dependencyScore: 90,
          packageJsonExists: true,
          lockFileExists: true
        },
        codeQuality: {
          totalLinesOfCode: 12450,
          codeFiles: 91,
          testFiles: 8,
          testCoverage: 72,
          duplicatedCode: 249,
          complexityScore: 78,
          maintainabilityIndex: 82,
          technicalDebt: ['Low test coverage in services', 'High complexity in DataCollector'],
          codeQualityScore: 77
        },
        organization: {
          followsConventions: true,
          hasProperNaming: true,
          hasDocumentation: true,
          hasTests: true,
          hasTypeScript: true,
          hasLinting: false,
          hasFormatting: false,
          organizationScore: 85,
          issues: ['Missing ESLint configuration', 'Missing Prettier configuration']
        },
        recommendations: [
          'Add ESLint and Prettier configuration for code quality',
          'Increase test coverage from 72% to at least 80%',
          'Create docs directory for project documentation',
          'Remove unused dependency: lodash',
          'Update outdated dependency: react-query',
          'Organize or remove task completion files from root',
          'Add scripts directory for build and deployment scripts',
          'Consider implementing automated code quality checks',
          'Add pre-commit hooks for code formatting',
          'Implement continuous integration for automated testing'
        ]
      }
      
      setAnalysis(mockAnalysis)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to analyze project structure')
    } finally {
      setLoading(false)
    }
  }, [])

  const exportAnalysis = useCallback(() => {
    if (!analysis) return null
    
    const report = {
      timestamp: new Date(analysis.timestamp).toISOString(),
      overallScore: analysis.overallScore,
      status: analysis.status,
      summary: {
        totalFiles: analysis.structure.totalFiles,
        totalDirectories: analysis.structure.totalDirectories,
        totalLinesOfCode: analysis.codeQuality.totalLinesOfCode,
        testCoverage: analysis.codeQuality.testCoverage,
        totalDependencies: analysis.dependencies.totalDependencies,
        structureScore: analysis.structure.structureScore,
        dependencyScore: analysis.dependencies.dependencyScore,
        codeQualityScore: analysis.codeQuality.codeQualityScore,
        organizationScore: analysis.organization.organizationScore
      },
      details: {
        structure: analysis.structure,
        dependencies: analysis.dependencies,
        codeQuality: analysis.codeQuality,
        organization: analysis.organization
      },
      recommendations: analysis.recommendations
    }
    
    return report
  }, [analysis])

  const getHealthStatus = useCallback(() => {
    if (!analysis) return null
    
    const criticalIssues = []
    const warnings = []
    const suggestions = []
    
    // Verificar issues críticos
    if (analysis.codeQuality.testCoverage < 50) {
      criticalIssues.push('Very low test coverage')
    }
    if (analysis.dependencies.vulnerabilities.length > 0) {
      criticalIssues.push('Security vulnerabilities found')
    }
    if (!analysis.dependencies.packageJsonExists) {
      criticalIssues.push('Missing package.json')
    }
    
    // Verificar warnings
    if (analysis.codeQuality.testCoverage < 80) {
      warnings.push('Test coverage below recommended 80%')
    }
    if (analysis.structure.missingDirectories.length > 0) {
      warnings.push('Missing recommended directories')
    }
    if (analysis.dependencies.outdatedDeps.length > 0) {
      warnings.push('Outdated dependencies found')
    }
    
    // Verificar sugestões
    if (!analysis.organization.hasLinting) {
      suggestions.push('Add ESLint for code quality')
    }
    if (!analysis.organization.hasFormatting) {
      suggestions.push('Add Prettier for code formatting')
    }
    if (analysis.structure.unexpectedFiles.length > 0) {
      suggestions.push('Clean up root directory')
    }
    
    return {
      criticalIssues,
      warnings,
      suggestions,
      overallHealth: analysis.overallScore >= 90 ? 'excellent' : 
                    analysis.overallScore >= 75 ? 'good' :
                    analysis.overallScore >= 50 ? 'needs_improvement' : 'critical'
    }
  }, [analysis])

  return {
    analysis,
    loading,
    error,
    analyzeProject,
    exportAnalysis,
    getHealthStatus
  }
}