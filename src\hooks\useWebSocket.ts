// useWebSocket - Hook para Conexão WebSocket com Reconexão Automática

import { useEffect, useRef, useState, useCallback } from 'react'

export interface WebSocketMessage {
  type: 'arbitrage_update' | 'position_alert' | 'exchange_status' | 'heartbeat' | 'error'
  data: any
  timestamp: number
  exchange?: string
}

export interface WebSocketConfig {
  url: string
  protocols?: string[]
  reconnectInterval?: number
  maxReconnectAttempts?: number
  heartbeatInterval?: number
  debug?: boolean
}

export interface WebSocketState {
  isConnected: boolean
  isConnecting: boolean
  lastMessage: WebSocketMessage | null
  connectionAttempts: number
  lastError: string | null
  latency: number
}

const DEFAULT_CONFIG: Required<WebSocketConfig> = {
  url: 'ws://localhost:5001/ws',
  protocols: [],
  reconnectInterval: 3000,
  maxReconnectAttempts: 10,
  heartbeatInterval: 30000,
  debug: false
}

export function useWebSocket(
  config: WebSocketConfig,
  onMessage?: (message: WebSocketMessage) => void,
  onConnect?: () => void,
  onDisconnect?: () => void,
  onError?: (error: string) => void
) {
  const fullConfig = { ...DEFAULT_CONFIG, ...config }
  
  const [state, setState] = useState<WebSocketState>({
    isConnected: false,
    isConnecting: false,
    lastMessage: null,
    connectionAttempts: 0,
    lastError: null,
    latency: 0
  })
  
  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const heartbeatTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const pingTimeRef = useRef<number>(0)
  const messageQueueRef = useRef<WebSocketMessage[]>([])
  
  // Callbacks refs para evitar re-renders desnecessários
  const onMessageRef = useRef(onMessage)
  const onConnectRef = useRef(onConnect)
  const onDisconnectRef = useRef(onDisconnect)
  const onErrorRef = useRef(onError)
  
  onMessageRef.current = onMessage
  onConnectRef.current = onConnect
  onDisconnectRef.current = onDisconnect
  onErrorRef.current = onError
  
  // Log debug
  const debugLog = useCallback((message: string, data?: any) => {
    if (fullConfig.debug) {
      console.log(`[WebSocket] ${message}`, data || '')
    }
  }, [fullConfig.debug])
  
  // Calcular latência
  const calculateLatency = useCallback(() => {
    if (pingTimeRef.current > 0) {
      const latency = Date.now() - pingTimeRef.current
      setState(prev => ({ ...prev, latency }))
      debugLog(`Latency: ${latency}ms`)
    }
  }, [debugLog])
  
  // Enviar heartbeat
  const sendHeartbeat = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      pingTimeRef.current = Date.now()
      const heartbeat: WebSocketMessage = {
        type: 'heartbeat',
        data: { ping: true },
        timestamp: Date.now()
      }
      wsRef.current.send(JSON.stringify(heartbeat))
      debugLog('Heartbeat sent')
    }
  }, [debugLog])
  
  // Configurar heartbeat
  const setupHeartbeat = useCallback(() => {
    if (heartbeatTimeoutRef.current) {
      clearInterval(heartbeatTimeoutRef.current)
    }
    
    heartbeatTimeoutRef.current = setInterval(() => {
      sendHeartbeat()
    }, fullConfig.heartbeatInterval)
    
    debugLog(`Heartbeat configured: ${fullConfig.heartbeatInterval}ms`)
  }, [fullConfig.heartbeatInterval, sendHeartbeat, debugLog])
  
  // Processar mensagem recebida
  const processMessage = useCallback((event: MessageEvent) => {
    try {
      const message: WebSocketMessage = JSON.parse(event.data)
      
      // Atualizar estado
      setState(prev => ({ ...prev, lastMessage: message }))
      
      // Processar tipos especiais
      if (message.type === 'heartbeat' && message.data.pong) {
        calculateLatency()
        return
      }
      
      // Chamar callback
      if (onMessageRef.current) {
        onMessageRef.current(message)
      }
      
      debugLog('Message received', message)
      
    } catch (error) {
      const errorMsg = `Failed to parse message: ${error}`
      setState(prev => ({ ...prev, lastError: errorMsg }))
      onErrorRef.current?.(errorMsg)
      debugLog('Parse error', error)
    }
  }, [calculateLatency, debugLog])
  
  // Conectar WebSocket
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN || 
        wsRef.current?.readyState === WebSocket.CONNECTING) {
      debugLog('Already connected or connecting')
      return
    }
    
    setState(prev => ({ 
      ...prev, 
      isConnecting: true, 
      lastError: null 
    }))
    
    debugLog(`Connecting to ${fullConfig.url}`)
    
    try {
      wsRef.current = new WebSocket(fullConfig.url, fullConfig.protocols)
      
      wsRef.current.onopen = () => {
        debugLog('Connected successfully')
        
        setState(prev => ({ 
          ...prev, 
          isConnected: true, 
          isConnecting: false,
          connectionAttempts: 0,
          lastError: null
        }))
        
        setupHeartbeat()
        onConnectRef.current?.()
        
        // Enviar mensagens em fila
        while (messageQueueRef.current.length > 0) {
          const queuedMessage = messageQueueRef.current.shift()
          if (queuedMessage && wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify(queuedMessage))
          }
        }
      }
      
      wsRef.current.onmessage = processMessage
      
      wsRef.current.onclose = (event) => {
        debugLog(`Connection closed: ${event.code} - ${event.reason}`)
        
        setState(prev => ({ 
          ...prev, 
          isConnected: false, 
          isConnecting: false 
        }))
        
        if (heartbeatTimeoutRef.current) {
          clearInterval(heartbeatTimeoutRef.current)
        }
        
        onDisconnectRef.current?.()
        
        // Tentar reconectar se não foi fechamento manual
        if (event.code !== 1000) {
          scheduleReconnect()
        }
      }
      
      wsRef.current.onerror = (error) => {
        const errorMsg = `WebSocket error: ${error}`
        debugLog('Connection error', error)
        
        setState(prev => ({ 
          ...prev, 
          lastError: errorMsg,
          isConnecting: false 
        }))
        
        onErrorRef.current?.(errorMsg)
      }
      
    } catch (error) {
      const errorMsg = `Failed to create WebSocket: ${error}`
      setState(prev => ({ 
        ...prev, 
        lastError: errorMsg,
        isConnecting: false 
      }))
      onErrorRef.current?.(errorMsg)
      debugLog('Create error', error)
    }
  }, [fullConfig.url, fullConfig.protocols, setupHeartbeat, processMessage, debugLog])
  
  // Agendar reconexão
  const scheduleReconnect = useCallback(() => {
    setState(prev => {
      if (prev.connectionAttempts >= fullConfig.maxReconnectAttempts) {
        const errorMsg = `Max reconnection attempts (${fullConfig.maxReconnectAttempts}) reached`
        onErrorRef.current?.(errorMsg)
        return { ...prev, lastError: errorMsg }
      }
      
      const newAttempts = prev.connectionAttempts + 1
      debugLog(`Scheduling reconnect attempt ${newAttempts}/${fullConfig.maxReconnectAttempts}`)
      
      reconnectTimeoutRef.current = setTimeout(() => {
        connect()
      }, fullConfig.reconnectInterval * newAttempts) // Backoff exponencial
      
      return { ...prev, connectionAttempts: newAttempts }
    })
  }, [fullConfig.maxReconnectAttempts, fullConfig.reconnectInterval, connect, debugLog])
  
  // Desconectar
  const disconnect = useCallback(() => {
    debugLog('Disconnecting...')
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }
    
    if (heartbeatTimeoutRef.current) {
      clearInterval(heartbeatTimeoutRef.current)
    }
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect')
      wsRef.current = null
    }
    
    setState(prev => ({ 
      ...prev, 
      isConnected: false, 
      isConnecting: false,
      connectionAttempts: 0
    }))
  }, [debugLog])
  
  // Enviar mensagem
  const sendMessage = useCallback((message: Omit<WebSocketMessage, 'timestamp'>) => {
    const fullMessage: WebSocketMessage = {
      ...message,
      timestamp: Date.now()
    }
    
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(fullMessage))
      debugLog('Message sent', fullMessage)
    } else {
      // Adicionar à fila se não conectado
      messageQueueRef.current.push(fullMessage)
      debugLog('Message queued (not connected)', fullMessage)
    }
  }, [debugLog])
  
  // Reconectar manualmente
  const reconnect = useCallback(() => {
    debugLog('Manual reconnect requested')
    disconnect()
    setTimeout(connect, 1000)
  }, [connect, disconnect, debugLog])
  
  // Effect principal
  useEffect(() => {
    connect()
    
    return () => {
      disconnect()
    }
  }, []) // Apenas na montagem
  
  // Cleanup no unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
      if (heartbeatTimeoutRef.current) {
        clearInterval(heartbeatTimeoutRef.current)
      }
    }
  }, [])
  
  return {
    // Estado
    ...state,
    
    // Ações
    connect,
    disconnect,
    reconnect,
    sendMessage,
    
    // Utilitários
    isReady: state.isConnected && !state.isConnecting,
    hasError: !!state.lastError,
    shouldReconnect: state.connectionAttempts < fullConfig.maxReconnectAttempts,
    
    // Configuração
    config: fullConfig
  }
}

// Hook auxiliar para arbitragem em tempo real
export function useArbitrageWebSocket(
  onOpportunityUpdate?: (opportunities: any[]) => void,
  onPositionAlert?: (alert: any) => void,
  onExchangeStatus?: (status: any) => void
) {
  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'arbitrage_update':
        onOpportunityUpdate?.(message.data.opportunities || [])
        break
        
      case 'position_alert':
        onPositionAlert?.(message.data)
        break
        
      case 'exchange_status':
        onExchangeStatus?.(message.data)
        break
        
      default:
        console.log('Unknown message type:', message.type)
    }
  }, [onOpportunityUpdate, onPositionAlert, onExchangeStatus])
  
  const ws = useWebSocket(
    {
      url: 'ws://localhost:5001/arbitrage',
      debug: process.env.NODE_ENV === 'development'
    },
    handleMessage
  )
  
  // Funções específicas para arbitragem
  const subscribeToSymbol = useCallback((symbol: string) => {
    ws.sendMessage({
      type: 'arbitrage_update',
      data: { action: 'subscribe', symbol }
    })
  }, [ws])
  
  const unsubscribeFromSymbol = useCallback((symbol: string) => {
    ws.sendMessage({
      type: 'arbitrage_update',
      data: { action: 'unsubscribe', symbol }
    })
  }, [ws])
  
  const requestSnapshot = useCallback(() => {
    ws.sendMessage({
      type: 'arbitrage_update',
      data: { action: 'snapshot' }
    })
  }, [ws])
  
  return {
    ...ws,
    subscribeToSymbol,
    unsubscribeFromSymbol,
    requestSnapshot
  }
}

export default useWebSocket