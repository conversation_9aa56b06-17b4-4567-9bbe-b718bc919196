// Constantes do sistema de arbitragem

// Configurações de atualização
export const UPDATE_INTERVALS = {
  REAL_TIME: 5000, // 5 segundos
  CHART_DATA: 30000, // 30 segundos
  POSITION_UPDATE: 1000, // 1 segundo
} as const

// Thresholds de validação
export const VALIDATION_THRESHOLDS = {
  MIN_SPREAD_PERCENTAGE: 0.05, // 0.05%
  MIN_VOLUME_USD: 1000, // $1,000
  MAX_DATA_AGE_MS: 15000, // 15 segundos
  MIN_LIQUIDITY_SCORE: 0.3,
} as const

// Configurações de exchanges
export const EXCHANGES = {
  GATEIO: 'gateio',
  MEXC: 'mexc',
  BITGET: 'bitget',
} as const

export const EXCHANGE_INFO = {
  [EXCHANGES.GATEIO]: {
    name: 'Gate.io',
    spotPairs: 2670,
    futuresPairs: 602,
    color: 'exchange-gateio',
    fee: 0.1, // 0.1%
  },
  [EXCHANGES.MEXC]: {
    name: 'MEXC',
    spotPairs: 2429,
    futuresPairs: 787,
    color: 'exchange-mexc',
    fee: 0.1, // 0.1%
  },
  [EXCHANGES.BITGET]: {
    name: 'Bitget',
    spotPairs: 799,
    futuresPairs: 513,
    color: 'exchange-bitget',
    fee: 0.1, // 0.1%
  },
} as const

// Tipos de arbitragem
export const ARBITRAGE_TYPES = {
  SPOT_FUTURES_CROSS: 'spot-futures-cross',
  FUTURES_FUTURES_CROSS: 'futures-futures-cross',
} as const

// Classificações de rentabilidade
export const PROFITABILITY_LEVELS = {
  HIGH: 'high', // >= 1%
  MEDIUM: 'medium', // 0.5% - 1%
  LOW: 'low', // 0.05% - 0.5%
} as const

export const PROFITABILITY_THRESHOLDS = {
  HIGH_MIN: 1.0,
  MEDIUM_MIN: 0.5,
  LOW_MIN: 0.05,
} as const

// Configurações de cache
export const CACHE_LEVELS = {
  L1: 2000, // 2 segundos
  L2: 5000, // 5 segundos
  L3: 10000, // 10 segundos
} as const

// Configurações de alertas
export const ALERT_THRESHOLDS = {
  VISUAL_ALERT: 0.5, // 0.5%
  SOUND_ALERT: 1.0, // 1.0%
  AUTO_REMOVE_DELAY: 3000, // 3 segundos
} as const

// URLs das exchanges para redirecionamento
export const EXCHANGE_URLS = {
  [EXCHANGES.GATEIO]: {
    spot: 'https://www.gate.io/trade',
    futures: 'https://www.gate.io/futures_trade',
  },
  [EXCHANGES.MEXC]: {
    spot: 'https://www.mexc.com/exchange',
    futures: 'https://futures.mexc.com/exchange',
  },
  [EXCHANGES.BITGET]: {
    spot: 'https://www.bitget.com/spot',
    futures: 'https://www.bitget.com/futures',
  },
} as const

// Configurações de performance
export const PERFORMANCE_TARGETS = {
  INITIAL_LOAD_TIME: 2000, // 2 segundos
  UPDATE_LATENCY: 100, // 100ms
  TRANSITION_DURATION: 300, // 300ms
  MAX_OPPORTUNITIES: 2000,
} as const