import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Utilitários para formatação de dados de arbitragem
export function formatCurrency(value: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 6,
  }).format(value)
}

export function formatPercentage(value: number, decimals: number = 2): string {
  return `${value.toFixed(decimals)}%`
}

export function formatNumber(value: number, decimals: number = 2): string {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value)
}

export function formatTimestamp(timestamp: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  }).format(timestamp)
}

// Utilitário para classificar rentabilidade
export function getProfitabilityColor(profitability: 'high' | 'medium' | 'low'): string {
  switch (profitability) {
    case 'high':
      return 'text-profit-high'
    case 'medium':
      return 'text-profit-medium'
    case 'low':
      return 'text-profit-low'
    default:
      return 'text-muted-foreground'
  }
}

// Utilitário para cores de exchanges
export function getExchangeColor(exchange: string): string {
  switch (exchange.toLowerCase()) {
    case 'gateio':
      return 'bg-exchange-gateio'
    case 'mexc':
      return 'bg-exchange-mexc'
    case 'bitget':
      return 'bg-exchange-bitget'
    default:
      return 'bg-muted'
  }
}