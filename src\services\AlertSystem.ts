// AlertSystem Service Avançado para Alertas Cross-Exchange

import type {
  ArbitrageOpportunity,
  Position
} from '@/types/arbitrage'

import { ALERT_CONFIG } from '@/config/arbitrage'

interface AudioContext {
  createOscillator(): OscillatorNode
  createGain(): GainNode
  destination: AudioDestinationNode
  currentTime: number
}

interface NotificationOptions {
  title: string
  body: string
  icon?: string
  tag?: string
  requireInteraction?: boolean
}

export class AlertSystem {
  private static instance: AlertSystem
  private audioContext: AudioContext | null = null
  private alertHistory = new Map<string, number>() // Para cooldown
  private activeAlerts = new Set<string>()
  private notificationPermission: NotificationPermission = 'default'
  private alertsEnabled: boolean = false // 🔴 ALERTAS DESATIVADOS

  // Singleton pattern
  public static getInstance(): AlertSystem {
    if (!AlertSystem.instance) {
      AlertSystem.instance = new AlertSystem()
    }
    return AlertSystem.instance
  }

  constructor() {
    this.initializeAudioContext()
    this.requestNotificationPermission()
  }

  /**
   * Inicializa Web Audio API para sons sintéticos
   */
  private initializeAudioContext(): void {
    try {
      // @ts-ignore - Web Audio API
      const AudioContextClass = window.AudioContext || window.webkitAudioContext
      if (AudioContextClass) {
        this.audioContext = new AudioContextClass()
        console.log('🔊 Web Audio API inicializada')
      }
    } catch (error) {
      console.warn('⚠️ Web Audio API não disponível:', error)
    }
  }

  /**
   * Solicita permissão para notificações
   */
  private async requestNotificationPermission(): Promise<void> {
    if ('Notification' in window) {
      this.notificationPermission = await Notification.requestPermission()
      console.log(`🔔 Permissão de notificação: ${this.notificationPermission}`)
    }
  }

  /**
   * Verifica alertas de spread cross-exchange
   */
  public checkCrossExchangeSpreadAlert(opportunity: ArbitrageOpportunity): void {
    if (!this.alertsEnabled) return // 🔴 ALERTAS DESATIVADOS
    
    const spreadPercentage = Math.abs(opportunity.spreadPercentage)
    const alertKey = `spread_${opportunity.id}`

    // Verificar cooldown
    if (this.isInCooldown(alertKey)) {
      return
    }

    // Determinar tipo de alerta baseado no spread
    let alertType: 'visual' | 'sound' | 'high' | 'critical' | null = null
    let message = ''

    if (spreadPercentage >= ALERT_CONFIG.THRESHOLDS.CRITICAL) {
      alertType = 'critical'
      message = `🚨 CRÍTICO: Spread de ${spreadPercentage.toFixed(2)}% em ${opportunity.symbol} (${opportunity.spotExchange} → ${opportunity.futuresExchange})`
    } else if (spreadPercentage >= ALERT_CONFIG.THRESHOLDS.HIGH_PRIORITY) {
      alertType = 'high'
      message = `🔥 ALTA PRIORIDADE: Spread de ${spreadPercentage.toFixed(2)}% em ${opportunity.symbol} (${opportunity.spotExchange} → ${opportunity.futuresExchange})`
    } else if (spreadPercentage >= ALERT_CONFIG.THRESHOLDS.SOUND_ALERT) {
      alertType = 'sound'
      message = `🔊 Spread de ${spreadPercentage.toFixed(2)}% em ${opportunity.symbol} (${opportunity.spotExchange} → ${opportunity.futuresExchange})`
    } else if (spreadPercentage >= ALERT_CONFIG.THRESHOLDS.VISUAL_ALERT) {
      alertType = 'visual'
      message = `👁️ Spread de ${spreadPercentage.toFixed(2)}% em ${opportunity.symbol} (${opportunity.spotExchange} → ${opportunity.futuresExchange})`
    }

    if (alertType) {
      this.triggerAlert(alertType, message, opportunity)
      this.setAlertCooldown(alertKey)
    }
  }

  /**
   * Verifica alertas de fechamento de posição cross-exchange
   */
  public checkCrossExchangeCloseAlert(position: Position): void {
    if (!this.alertsEnabled) return // 🔴 ALERTAS DESATIVADOS
    if (!position.alertEnabled) return

    const currentSpread = Math.abs(position.currentSpread)
    const alertKey = `close_${position.id}`

    // Verificar cooldown
    if (this.isInCooldown(alertKey)) {
      return
    }

    // Alerta quando spread se aproxima do threshold
    if (currentSpread <= position.alertThreshold) {
      const message = `⚠️ FECHAR POSIÇÃO: ${position.symbol} spread em ${currentSpread.toFixed(3)}% (threshold: ${position.alertThreshold.toFixed(3)}%)`
      
      this.triggerAlert('high', message, position)
      this.setAlertCooldown(alertKey)
      
      // Atualizar status da posição
      position.status = 'alert'
      position.lastAlertTime = new Date()
    }
  }

  /**
   * Dispara alerta com diferentes opções e severidades
   */
  public triggerAlert(
    type: 'visual' | 'sound' | 'high' | 'critical',
    message: string,
    data?: ArbitrageOpportunity | Position
  ): void {
    if (!this.alertsEnabled) return // 🔴 ALERTAS DESATIVADOS
    
    console.log(`🔔 Alerta ${type.toUpperCase()}: ${message}`)

    // Evitar spam de alertas
    if (this.activeAlerts.has(message)) {
      return
    }

    this.activeAlerts.add(message)

    // Alerta visual
    this.showVisualAlert(type, message)

    // Alerta sonoro
    if (ALERT_CONFIG.NOTIFICATIONS.SOUND_ENABLED && 
        (type === 'sound' || type === 'high' || type === 'critical')) {
      this.playAlertSound(type)
    }

    // Vibração (dispositivos móveis)
    if (ALERT_CONFIG.NOTIFICATIONS.VIBRATION_ENABLED && 
        (type === 'high' || type === 'critical')) {
      this.triggerVibration(type)
    }

    // Notificação push
    if (ALERT_CONFIG.NOTIFICATIONS.PUSH_NOTIFICATIONS && 
        (type === 'high' || type === 'critical')) {
      this.showPushNotification(message, data)
    }

    // Auto-remover alerta após delay
    setTimeout(() => {
      this.activeAlerts.delete(message)
    }, ALERT_CONFIG.BEHAVIOR.AUTO_REMOVE_DELAY)
  }

  /**
   * Mostra alerta visual na tela
   */
  private showVisualAlert(type: string, message: string): void {
    // Criar elemento de alerta
    const alertElement = document.createElement('div')
    alertElement.className = `alert-toast alert-${type}`
    alertElement.textContent = message
    
    // Estilos baseados no tipo
    const styles = {
      visual: 'background: #3b82f6; color: white;',
      sound: 'background: #f59e0b; color: white;',
      high: 'background: #ef4444; color: white;',
      critical: 'background: #dc2626; color: white; animation: pulse 1s infinite;'
    }
    
    alertElement.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 8px;
      font-weight: 500;
      z-index: 10000;
      max-width: 400px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      ${styles[type as keyof typeof styles]}
    `
    
    document.body.appendChild(alertElement)
    
    // Remover após delay
    setTimeout(() => {
      if (alertElement.parentNode) {
        alertElement.parentNode.removeChild(alertElement)
      }
    }, ALERT_CONFIG.BEHAVIOR.AUTO_REMOVE_DELAY)
  }

  /**
   * Reproduz som sintético para alerta
   */
  private playAlertSound(type: string): void {
    if (!this.audioContext) return

    try {
      const oscillator = this.audioContext.createOscillator()
      const gainNode = this.audioContext.createGain()
      
      // Configurar frequências por tipo
      const frequencies = {
        sound: [800, 1000],
        high: [1000, 1200, 800],
        critical: [1200, 800, 1200, 800]
      }
      
      const freqs = frequencies[type as keyof typeof frequencies] || [800]
      
      oscillator.connect(gainNode)
      gainNode.connect(this.audioContext.destination)
      
      // Configurar som
      oscillator.type = 'sine'
      gainNode.gain.setValueAtTime(0.3, this.audioContext.currentTime)
      
      // Tocar sequência de frequências
      let time = this.audioContext.currentTime
      for (const freq of freqs) {
        oscillator.frequency.setValueAtTime(freq, time)
        time += 0.2
      }
      
      oscillator.start(this.audioContext.currentTime)
      oscillator.stop(this.audioContext.currentTime + freqs.length * 0.2)
      
    } catch (error) {
      console.warn('⚠️ Erro reproduzindo som:', error)
    }
  }

  /**
   * Dispara vibração em dispositivos móveis
   */
  private triggerVibration(type: string): void {
    if ('vibrate' in navigator) {
      const patterns = {
        high: [200, 100, 200],
        critical: [300, 100, 300, 100, 300]
      }
      
      const pattern = patterns[type as keyof typeof patterns] || [200]
      navigator.vibrate(pattern)
    }
  }

  /**
   * Mostra notificação push do navegador
   */
  private showPushNotification(message: string, data?: any): void {
    if (this.notificationPermission !== 'granted') return

    try {
      const notification = new Notification('Crypto Arbitrage Alert', {
        body: message,
        icon: '/favicon.ico',
        tag: 'arbitrage-alert',
        requireInteraction: true
      })

      // Auto-fechar após 5 segundos
      setTimeout(() => {
        notification.close()
      }, 5000)

    } catch (error) {
      console.warn('⚠️ Erro mostrando notificação:', error)
    }
  }

  /**
   * Verifica se alerta está em cooldown
   */
  private isInCooldown(alertKey: string): boolean {
    const lastAlert = this.alertHistory.get(alertKey)
    if (!lastAlert) return false
    
    return Date.now() - lastAlert < ALERT_CONFIG.BEHAVIOR.COOLDOWN_PERIOD
  }

  /**
   * Define cooldown para alerta
   */
  private setAlertCooldown(alertKey: string): void {
    this.alertHistory.set(alertKey, Date.now())
  }

  /**
   * Limpa histórico de alertas antigos
   */
  public cleanupAlertHistory(): void {
    const now = Date.now()
    const maxAge = ALERT_CONFIG.BEHAVIOR.COOLDOWN_PERIOD * 2
    
    for (const [key, timestamp] of this.alertHistory.entries()) {
      if (now - timestamp > maxAge) {
        this.alertHistory.delete(key)
      }
    }
  }

  /**
   * Obtém estatísticas de alertas
   */
  public getAlertStats(): {
    totalAlerts: number
    activeAlerts: number
    alertHistory: number
  } {
    return {
      totalAlerts: this.alertHistory.size,
      activeAlerts: this.activeAlerts.size,
      alertHistory: this.alertHistory.size
    }
  }

  /**
   * Configura alertas personalizados
   */
  public configureAlert(config: Partial<typeof ALERT_CONFIG>): void {
    // Implementar configuração personalizada se necessário
    console.log('🔧 Configuração de alerta atualizada:', config)
  }

  /**
   * Previne spam de alertas com agrupamento inteligente
   */
  public groupSimilarAlerts(opportunities: ArbitrageOpportunity[]): ArbitrageOpportunity[] {
    if (!ALERT_CONFIG.BEHAVIOR.GROUP_SIMILAR) {
      return opportunities
    }

    // Agrupar por exchange pair e símbolo
    const groups = new Map<string, ArbitrageOpportunity[]>()
    
    for (const opp of opportunities) {
      const groupKey = `${opp.spotExchange}-${opp.futuresExchange}-${opp.baseAsset}`
      if (!groups.has(groupKey)) {
        groups.set(groupKey, [])
      }
      groups.get(groupKey)!.push(opp)
    }

    // Retornar apenas a melhor oportunidade de cada grupo
    const grouped: ArbitrageOpportunity[] = []
    for (const group of groups.values()) {
      const best = group.reduce((prev, current) => 
        Math.abs(current.spreadPercentage) > Math.abs(prev.spreadPercentage) ? current : prev
      )
      grouped.push(best)
    }

    return grouped
  }

  /**
   * Ativa ou desativa o sistema de alertas
   */
  public setAlertsEnabled(enabled: boolean): void {
    this.alertsEnabled = enabled
    console.log(`🔔 Sistema de alertas ${enabled ? 'ATIVADO' : 'DESATIVADO'}`)
  }

  /**
   * Verifica se os alertas estão ativados
   */
  public isAlertsEnabled(): boolean {
    return this.alertsEnabled
  }

  /**
   * Testar sistema de alertas
   */
  public testAlerts(): void {
    console.log('🧪 Testando sistema de alertas...')
    
    // Testar cada tipo de alerta
    const testTypes: Array<'visual' | 'sound' | 'high' | 'critical'> = ['visual', 'sound', 'high', 'critical']
    
    testTypes.forEach((type, index) => {
      setTimeout(() => {
        this.triggerAlert(type, `Teste de alerta ${type.toUpperCase()}`)
      }, index * 1000)
    })
  }
}