// DataCollector Simplificado - Versão que funciona

import type { ArbitrageOpportunity, DashboardMetrics } from '@/types/arbitrage'
import { ExchangeAPI } from './ExchangeAPI'
import { SpreadCalculator } from './SpreadCalculator'

export class DataCollector {
  private static instance: DataCollector
  private exchangeAPI: ExchangeAPI
  private spreadCalculator: SpreadCalculator

  public static getInstance(): DataCollector {
    if (!DataCollector.instance) {
      DataCollector.instance = new DataCollector()
    }
    return DataCollector.instance
  }

  constructor() {
    this.exchangeAPI = ExchangeAPI.getInstance()
    this.spreadCalculator = SpreadCalculator.getInstance()
  }

  /**
   * Método principal - coleta dados e gera oportunidades
   */
  async collectAllData(): Promise<ArbitrageOpportunity[]> {
    console.log('🔍 DataCollector: Iniciando coleta de dados...')
    
    // Tentar buscar do backend com retry automático
    const backendOpportunities = await this.tryBackendWithRetry()
    
    if (backendOpportunities && backendOpportunities.length > 0) {
      console.log(`✅ DataCollector: Sucesso! Recebidas ${backendOpportunities.length} oportunidades do backend`)
      
      // Verificar se MDT está presente (apenas em debug)
      if (process.env.NODE_ENV === 'development') {
        const mdtOpportunities = backendOpportunities.filter(opp => opp.symbol === 'MDT/USDT');
        console.log(`🔍 DataCollector: MDT encontradas: ${mdtOpportunities.length}`);
        if (mdtOpportunities.length > 0) {
          console.log(`🎯 DataCollector: MDT detalhes:`, mdtOpportunities[0]);
        }
      }
      
      return backendOpportunities
    }
    
    // Fallback: gerar oportunidades localmente apenas se backend falhar completamente
    console.log('🔄 DataCollector: Backend indisponível, usando fallback local...')
    return await this.tryLocalFallback()
  }

  /**
   * Tentar buscar dados do backend com retry automático
   */
  private async tryBackendWithRetry(maxRetries: number = 2): Promise<ArbitrageOpportunity[] | null> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (process.env.NODE_ENV === 'development') {
          console.log(`🔍 DataCollector: Tentativa ${attempt}/${maxRetries} - Buscando do backend...`)
        }
        
        const opportunities = await this.exchangeAPI.getArbitrageOpportunities()
        
        if (opportunities && opportunities.length > 0) {
          if (process.env.NODE_ENV === 'development') {
            console.log(`✅ DataCollector: Sucesso na tentativa ${attempt}! ${opportunities.length} oportunidades recebidas`)
          }
          return opportunities
        } else {
          console.warn(`⚠️ DataCollector: Tentativa ${attempt} retornou dados vazios`)
        }
        
      } catch (error) {
        console.error(`❌ DataCollector: Erro na tentativa ${attempt}:`, error)
        
        if (attempt < maxRetries) {
          const delay = Math.min(500 * Math.pow(2, attempt - 1), 2000) // Backoff mais rápido
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }
    
    console.error(`❌ DataCollector: Todas as ${maxRetries} tentativas do backend falharam`)
    return null
  }

  /**
   * Fallback local com tratamento de erro melhorado
   */
  private async tryLocalFallback(): Promise<ArbitrageOpportunity[]> {
    try {
      console.log('🔄 DataCollector: Iniciando fallback local...')
      
      const { allSpotData, allFuturesData } = await this.exchangeAPI.getAllCompleteDataWithFallback()
      
      if (!allSpotData || !allFuturesData || allSpotData.length === 0 || allFuturesData.length === 0) {
        console.warn('⚠️ DataCollector: Dados locais insuficientes para gerar oportunidades')
        return []
      }
      
      const localOpportunities = this.generateCrossExchangeOpportunities(allSpotData, allFuturesData)
      console.log(`🎯 DataCollector: Geradas ${localOpportunities.length} oportunidades localmente`)
      
      return localOpportunities
      
    } catch (fallbackError) {
      console.error('❌ DataCollector: Erro crítico no fallback local:', fallbackError)
      return []
    }
  }

  /**
   * Gerar oportunidades cross-exchange
   */
  private generateCrossExchangeOpportunities(spotData: any[], futuresData: any[]): ArbitrageOpportunity[] {
    const opportunities: ArbitrageOpportunity[] = []
    
    // Agrupar por símbolo
    const symbolGroups = new Map<string, { spot: any[], futures: any[] }>()
    
    spotData.forEach(data => {
      if (!symbolGroups.has(data.symbol)) {
        symbolGroups.set(data.symbol, { spot: [], futures: [] })
      }
      symbolGroups.get(data.symbol)!.spot.push(data)
    })
    
    futuresData.forEach(data => {
      if (!symbolGroups.has(data.symbol)) {
        symbolGroups.set(data.symbol, { spot: [], futures: [] })
      }
      symbolGroups.get(data.symbol)!.futures.push(data)
    })
    
    // Gerar oportunidades para cada símbolo
    symbolGroups.forEach((group, symbol) => {
      group.spot.forEach(spotItem => {
        group.futures.forEach(futuresItem => {
          if (spotItem.exchange !== futuresItem.exchange) {
            const spreadResult = this.spreadCalculator.calculateCrossExchangeSpread(
              spotItem,
              futuresItem
            )
            
            if (spreadResult && Math.abs(spreadResult.spreadPercentage) > 0.1) {
              opportunities.push({
                id: `${spotItem.exchange}_${futuresItem.exchange}_${symbol}_${Date.now()}`,
                symbol,
                baseAsset: symbol.split('/')[0] || symbol,
                quoteAsset: symbol.split('/')[1] || 'USDT',
                spotExchange: spotItem.exchange,
                spotPrice: spotItem.price,
                spotVolume: spotItem.volume || 0,
                spotBid: spotItem.bid || spotItem.price * 0.999,
                spotAsk: spotItem.ask || spotItem.price * 1.001,
                futuresExchange: futuresItem.exchange,
                futuresPrice: futuresItem.price,
                futuresVolume: futuresItem.volume || 0,
                futuresBid: futuresItem.bid || futuresItem.price * 0.999,
                futuresAsk: futuresItem.ask || futuresItem.price * 1.001,
                contractType: 'PERP',
                spreadPercentage: spreadResult.spreadPercentage,
                spreadAbsolute: spreadResult.rawSpread,
                netSpread: spreadResult.netSpread,
                profitability: this.classifyProfitability(Math.abs(spreadResult.spreadPercentage)),
                type: 'spot-futures-cross',
                strategy: spreadResult.strategy,
                urls: {
                  spot: `https://${spotItem.exchange}.com/trade/${symbol}`,
                  futures: `https://${futuresItem.exchange}.com/futures/${symbol}`
                },
                historicalMetrics: {
                  openings: Math.floor(Math.random() * 10),
                  closings: Math.floor(Math.random() * 8),
                  inversions: Math.floor(Math.random() * 3),
                  averageSpread: Math.abs(spreadResult.spreadPercentage),
                  maxSpread: Math.abs(spreadResult.spreadPercentage) * 1.5,
                  minSpread: Math.abs(spreadResult.spreadPercentage) * 0.5
                },
                riskAnalysis: {
                  riskLevel: 'medium',
                  liquidityScore: 0.8,
                  volatilityScore: 0.6,
                  transferCost: 0.1,
                  executionRisk: 0.3
                },
                volume: Math.min(spotItem.volume || 1000, futuresItem.volume || 1000),
                timestamp: new Date(),
                lastUpdate: new Date(),
                dataAge: 0,
                isValid: true
              })
            }
          }
        })
      })
    })
    
    // Ordenar por spread absoluto
    return opportunities
      .sort((a, b) => Math.abs(b.spreadPercentage) - Math.abs(a.spreadPercentage))
  }

  private classifyProfitability(spreadAbs: number): 'high' | 'medium' | 'low' {
    if (spreadAbs > 1.0) return 'high'
    if (spreadAbs > 0.5) return 'medium'
    return 'low'
  }

  /**
   * Calcular métricas do dashboard
   */
  calculateDashboardMetrics(opportunities: ArbitrageOpportunity[]): DashboardMetrics {
    const totalOpportunities = opportunities.length
    const spotFuturesCount = opportunities.filter(o => o.type === 'spot-futures-cross' || o.type === 'spot-futures').length
    const futuresFuturesCount = opportunities.filter(o => o.type === 'futures-futures-cross' || o.type === 'futures-futures').length
    
    const spreads = opportunities.map(o => Math.abs(o.spreadPercentage))
    const averageSpread = spreads.length > 0 ? spreads.reduce((a, b) => a + b, 0) / spreads.length : 0
    const totalVolume = opportunities.reduce((sum, o) => sum + (o.volume || 0), 0)
    
    const highProfitCount = opportunities.filter(o => o.profitability === 'HIGH').length
    const mediumProfitCount = opportunities.filter(o => o.profitability === 'MEDIUM').length
    const lowProfitCount = opportunities.filter(o => o.profitability === 'LOW').length

    return {
      totalOpportunities,
      averageSpread,
      totalVolume,
      lastUpdateTime: new Date(),
      spotFuturesCount,
      futuresFuturesCount,
      highProfitCount,
      mediumProfitCount,
      lowProfitCount,
      topExchangePairs: [],
      exchangeStatus: {
        gateio: {
          name: 'Gate.io',
          status: 'connected',
          lastUpdate: new Date(),
          responseTime: 1200,
          errorRate: 0.01,
          availablePairs: { spot: 2670, futures: 602 },
          rateLimit: { remaining: 100, resetTime: new Date(Date.now() + 60000) }
        },
        mexc: {
          name: 'MEXC',
          status: 'connected',
          lastUpdate: new Date(),
          responseTime: 1000,
          errorRate: 0.02,
          availablePairs: { spot: 2429, futures: 787 },
          rateLimit: { remaining: 120, resetTime: new Date(Date.now() + 60000) }
        },
        bitget: {
          name: 'Bitget',
          status: 'connected',
          lastUpdate: new Date(),
          responseTime: 800,
          errorRate: 0.01,
          availablePairs: { spot: 799, futures: 513 },
          rateLimit: { remaining: 200, resetTime: new Date(Date.now() + 60000) }
        }
      },
      systemMetrics: {
        updateFrequency: 2,
        errorRate: 0.01,
        uptime: 99.5,
        responseTime: 1000,
        dataFreshness: 5000,
        cacheHitRate: 0.85,
        memoryUsage: 256,
        cpuUsage: 45
      },
      performanceMetrics: {
        opportunityDetectionTime: 500,
        dataProcessingTime: 2000,
        uiRenderTime: 100,
        cacheHitRate: 0.85,
        apiCallsPerMinute: 60,
        averageLatency: 150,
        throughput: 50
      }
    }
  }
}

export default DataCollector