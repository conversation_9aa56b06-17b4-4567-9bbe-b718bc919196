// ExchangeAPI - Produção Real com APIs das Exchanges

import type { Exchange, ExchangeData, ArbitrageOpportunity, Profitability, RiskLevel, ContractType, ArbitrageType } from '@/types/arbitrage'
import { classifyProfitability } from '@/config/arbitrage'
import { DataNormalizer } from './data/DataNormalizer'

export class ExchangeAPI {
  private static instance: ExchangeAPI
  private enableRealAPIs: boolean

  constructor() {
    const viteEnv = import.meta.env.VITE_ENABLE_REAL_APIS
    
    this.enableRealAPIs = viteEnv === 'true'
    
    console.log('🔧 ExchangeAPI Configuração:')
    console.log('  - VITE_ENABLE_REAL_APIS:', viteEnv)
    console.log('  - enableRealAPIs final:', this.enableRealAPIs)
  }

  public static getInstance(): ExchangeAPI {
    if (!ExchangeAPI.instance) {
      ExchangeAPI.instance = new ExchangeAPI()
    }
    return ExchangeAPI.instance
  }

  /**
   * Método principal - coleta dados reais ou simulados
   */
  async getAllCompleteDataWithFallback(): Promise<{
    allSpotData: ExchangeData[]
    allFuturesData: ExchangeData[]
    metadata: any
  }> {
    if (this.enableRealAPIs) {
      console.log('🚀 Tentando coletar dados REAIS das exchanges...')
      try {
        const realData = await this.collectRealData()
        // Se conseguiu dados reais, retorna
        if (realData.allSpotData.length > 0 || realData.allFuturesData.length > 0) {
          return realData
        }
      } catch (error) {
        console.warn('⚠️ Falha na coleta de dados reais, usando simulados:', error)
      }
    }
    
    console.log('🔄 Usando dados simulados realistas...')
    return await this.collectRealisticMockData()
  }

  /**
   * Coleta dados reais de todas as exchanges via backend
   */
  private async collectRealData(): Promise<{
    allSpotData: ExchangeData[]
    allFuturesData: ExchangeData[]
    metadata: any
  }> {
    try {
      console.log('🚀 Conectando ao backend para coletar dados reais...')
      const startTime = Date.now()

      // Conectar ao backend Node.js
      const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:5001'
      const response = await fetch(`${backendUrl}/api/exchanges/data`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        // Timeout de 30 segundos
        signal: AbortSignal.timeout(30000)
      })

      if (!response.ok) {
        throw new Error(`Backend retornou erro ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.message || 'Backend retornou erro')
      }

      const processingTime = Date.now() - startTime
      console.log(`✅ Dados recebidos do backend em ${processingTime}ms`)
      console.log(`📊 Total: ${result.data.metadata.totalPairs} pares (${result.data.metadata.spotPairs} spot + ${result.data.metadata.futuresPairs} futures)`)

      return {
        allSpotData: result.data.allSpotData,
        allFuturesData: result.data.allFuturesData,
        metadata: {
          ...result.data.metadata,
          frontendProcessingTime: processingTime,
          backendProcessingTime: result.data.metadata.processingTime,
          source: 'backend-real-apis'
        }
      }

    } catch (error) {
      console.error('❌ Erro ao conectar com backend:', error)
      console.log('🔄 Fallback para dados mock...')
      
      // Em caso de erro, retorna dados mock
      return await this.collectRealisticMockData()
    }
  }

  /**
   * Coleta dados simulados realistas (com preços e volumes próximos ao real)
   */
  private async collectRealisticMockData(): Promise<{
    allSpotData: ExchangeData[]
    allFuturesData: ExchangeData[]
    metadata: any
  }> {
    // Simular delay de API
    await new Promise(resolve => setTimeout(resolve, 800))
    
    const allSpotData = this.generateRealisticMockData('spot')
    const allFuturesData = this.generateRealisticMockData('futures')
    
    const metadata = {
      totalPairs: allSpotData.length + allFuturesData.length,
      processingTime: 800,
      timestamp: new Date(),
      errors: [],
      warnings: ['Usando dados simulados realistas - APIs reais indisponíveis'],
      exchanges: {
        gateio: { 
          spot: allSpotData.filter(d => d.exchange === 'gateio').length, 
          futures: allFuturesData.filter(d => d.exchange === 'gateio').length 
        },
        mexc: { 
          spot: allSpotData.filter(d => d.exchange === 'mexc').length, 
          futures: allFuturesData.filter(d => d.exchange === 'mexc').length 
        },
        bitget: { 
          spot: allSpotData.filter(d => d.exchange === 'bitget').length, 
          futures: allFuturesData.filter(d => d.exchange === 'bitget').length 
        }
      }
    }

    console.log(`✅ Coletados ${allSpotData.length} spot + ${allFuturesData.length} futures SIMULADOS REALISTAS`)
    
    return {
      allSpotData,
      allFuturesData,
      metadata
    }
  }

  /**
   * Coleta oportunidades de arbitragem via backend
   */
  // Adaptador para transformar dados do backend no formato esperado pelo frontend
  private adaptBackendData(backendOpportunity: any): ArbitrageOpportunity {
    const now = new Date();
    const timestamp = new Date(backendOpportunity.timestamp || Date.now());
    const dataAge = backendOpportunity.dataAge || 0;

    // Gerar ID único com timestamp e hash para evitar duplicatas
    const baseId = `${backendOpportunity.symbol}-${backendOpportunity.spotExchange}-${backendOpportunity.futuresExchange}`;
    const uniqueHash = Math.abs(
      (backendOpportunity.spotPrice || 0) * 1000 + 
      (backendOpportunity.futuresPrice || 0) * 1000 + 
      (backendOpportunity.spreadPercentage || 0) * 10000
    ).toString(36).slice(0, 6);
    const timestampSuffix = timestamp.getTime().toString(36).slice(-4);

    return {
      id: `${baseId}-${uniqueHash}-${timestampSuffix}`,
      symbol: backendOpportunity.symbol,
      baseAsset: backendOpportunity.symbol.split('/')[0] || 'BTC',
      quoteAsset: backendOpportunity.symbol.split('/')[1] || 'USDT',
      
      // Dados do Spot
      spotExchange: backendOpportunity.spotExchange as Exchange,
      spotPrice: backendOpportunity.spotPrice,
      spotVolume: backendOpportunity.volume || 0,
      spotBid: backendOpportunity.spotPrice * 0.999, // Estimativa
      spotAsk: backendOpportunity.spotPrice * 1.001, // Estimativa
      
      // Dados do Futuros
      futuresExchange: backendOpportunity.futuresExchange as Exchange,
      futuresPrice: backendOpportunity.futuresPrice,
      futuresVolume: backendOpportunity.volume || 0,
      futuresBid: backendOpportunity.futuresPrice * 0.999, // Estimativa
      futuresAsk: backendOpportunity.futuresPrice * 1.001, // Estimativa
      contractType: 'PERP' as ContractType,
      fundingRate: backendOpportunity.fundingRate || 0.0001,
      
      // Cálculos de Spread
      spreadAbsolute: backendOpportunity.spread,
      spreadPercentage: backendOpportunity.spreadPercentage,
      netSpread: backendOpportunity.spread * 0.95, // Após taxas estimadas
      profitability: this.classifyProfitabilityLocal(backendOpportunity.spreadPercentage),
      
      // Estratégia baseada no tipo real
      type: this.mapArbitrageType(backendOpportunity.type),
      strategy: this.generateStrategy(backendOpportunity),
      
      // URLs
      urls: {
        spot: this.getExchangeUrl(backendOpportunity.spotExchange, backendOpportunity.symbol, 'spot'),
        futures: this.getExchangeUrl(backendOpportunity.futuresExchange, backendOpportunity.symbol, 'futures')
      },
      
      // Métricas Históricas (valores padrão)
      historicalMetrics: {
        openings: 5,
        closings: 3,
        inversions: 1,
        averageSpread: Math.abs(backendOpportunity.spreadPercentage),
        maxSpread: Math.abs(backendOpportunity.spreadPercentage) * 1.5,
        minSpread: Math.abs(backendOpportunity.spreadPercentage) * 0.5
      },
      
      // Análise de Risco
      riskAnalysis: {
        riskLevel: this.calculateRiskLevel(Math.abs(backendOpportunity.spreadPercentage)),
        liquidityScore: Math.min(backendOpportunity.volume / 1000000, 1), // Score baseado no volume
        volatilityScore: Math.abs(backendOpportunity.spreadPercentage) / 100,
        transferCost: 0.1, // 0.1% estimado
        executionRisk: Math.abs(backendOpportunity.spreadPercentage) > 5 ? 0.8 : 0.3
      },
      
      // Metadados
      lastUpdate: timestamp,
      dataAge: dataAge,
      timestamp: timestamp,
      isValid: Math.abs(backendOpportunity.spreadPercentage) > 0.05 && dataAge < 300000 // 5 minutos (mais permissivo)
    };
  }

  private classifyProfitabilityLocal(spreadPercentage: number): Profitability {
    return classifyProfitability(Math.abs(spreadPercentage));
  }

  private mapArbitrageType(backendType?: string): ArbitrageType {
    switch (backendType) {
      case 'spot-futures':
        return 'spot-futures-cross';
      case 'futures-futures':
        return 'futures-futures-cross';
      default:
        return 'spot-futures-cross'; // Fallback
    }
  }

  private generateStrategy(backendOpportunity: any) {
    const isSpotFutures = backendOpportunity.type === 'spot-futures';
    
    if (isSpotFutures) {
      return {
        action1: `Buy ${backendOpportunity.symbol} spot on ${backendOpportunity.spotExchange}`,
        action2: `Short ${backendOpportunity.symbol} futures on ${backendOpportunity.futuresExchange}`,
        exitCondition: "When prices converge",
        estimatedProfit: backendOpportunity.profitPotential || 0,
        requiredCapital: backendOpportunity.volume * backendOpportunity.spotPrice * 0.1,
        timeHorizon: "1-24 hours"
      };
    } else {
      // Futures vs Futures
      return {
        action1: `Long ${backendOpportunity.symbol} futures on ${backendOpportunity.spotExchange}`,
        action2: `Short ${backendOpportunity.symbol} futures on ${backendOpportunity.futuresExchange}`,
        exitCondition: "When prices converge",
        estimatedProfit: backendOpportunity.profitPotential || 0,
        requiredCapital: backendOpportunity.volume * backendOpportunity.spotPrice * 0.1,
        timeHorizon: "1-24 hours"
      };
    }
  }

  private calculateRiskLevel(spreadPercentage: number): RiskLevel {
    if (spreadPercentage >= 5.0) return 'high';
    if (spreadPercentage >= 1.0) return 'medium';
    return 'low';
  }

  private getExchangeUrl(exchange: string, symbol: string, type: 'spot' | 'futures'): string {
    const cleanSymbol = symbol.replace('/', '').replace('USDT', '_USDT');
    
    const urls: Record<string, Record<string, string>> = {
      gateio: {
        spot: `https://www.gate.io/trade/${cleanSymbol}`,
        futures: `https://www.gate.io/futures_trade/${cleanSymbol}`
      },
      mexc: {
        spot: `https://www.mexc.com/exchange/${cleanSymbol}`,
        futures: `https://futures.mexc.com/exchange/${cleanSymbol}`
      },
      bitget: {
        spot: `https://www.bitget.com/spot/${cleanSymbol}`,
        futures: `https://www.bitget.com/futures/${cleanSymbol}`
      }
    };

    return urls[exchange]?.[type] || '#';
  }

  async getArbitrageOpportunities(): Promise<ArbitrageOpportunity[]> {
    try {
      const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:5001';
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`🔍 ExchangeAPI: Fazendo requisição para ${backendUrl}/api/arbitrage/opportunities`)
      }
      
      const response = await fetch(`${backendUrl}/api/arbitrage/opportunities`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (process.env.NODE_ENV === 'development') {
        console.log(`🔍 ExchangeAPI: Response status: ${response.status}`)
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      let opportunities: any[] = [];

      // O backend retorna um objeto com { opportunities: [], count: number, ... }
      if (Array.isArray(data.opportunities)) {
        opportunities = data.opportunities;
      } 
      // Fallback: se a resposta for diretamente um array
      else if (Array.isArray(data)) {
        opportunities = data;
      } 
      else {
        console.log(`⚠️ ExchangeAPI: Dados inválidos ou sem oportunidades`, data);
        return [];
      }

      // Verificar se MDT está presente antes da adaptação
      const mdtBefore = opportunities.filter(opp => opp.symbol === 'MDT/USDT');
      console.log(`🔍 ExchangeAPI: MDT antes da adaptação: ${mdtBefore.length}`);
      if (mdtBefore.length > 0) {
        console.log(`🎯 ExchangeAPI: MDT encontrada:`, mdtBefore[0]);
      }

      // Adaptar os dados do backend para o formato esperado pelo frontend
      const adaptedOpportunities = opportunities.map(opportunity => this.adaptBackendData(opportunity));
      
      // Verificar se MDT está presente após a adaptação
      const mdtAfter = adaptedOpportunities.filter(opp => opp.symbol === 'MDT/USDT');
      console.log(`🔍 ExchangeAPI: MDT após adaptação: ${mdtAfter.length}`);
      if (mdtAfter.length > 0) {
        console.log(`🎯 ExchangeAPI: MDT adaptada:`, mdtAfter[0]);
      }
      
      console.log(`✅ ExchangeAPI: Retornando ${adaptedOpportunities.length} oportunidades adaptadas`);
      return adaptedOpportunities;
    } catch (error) {
      console.error('❌ ExchangeAPI: Erro ao buscar oportunidades:', error);
      throw error;
    }
  }

  private generateRealisticMockData(type: 'spot' | 'futures'): ExchangeData[] {
    const exchanges: Exchange[] = ['gateio', 'mexc', 'bitget']
    
    // Lista expandida de símbolos com preços realistas
    const cryptoPairs = [
      { symbol: 'BTC/USDT', basePrice: 43250 },
      { symbol: 'ETH/USDT', basePrice: 2580 },
      { symbol: 'BNB/USDT', basePrice: 315 },
      { symbol: 'ADA/USDT', basePrice: 0.485 },
      { symbol: 'SOL/USDT', basePrice: 98.5 },
      { symbol: 'XRP/USDT', basePrice: 0.625 },
      { symbol: 'DOGE/USDT', basePrice: 0.095 },
      { symbol: 'AVAX/USDT', basePrice: 36.8 },
      { symbol: 'LINK/USDT', basePrice: 14.2 },
      { symbol: 'MATIC/USDT', basePrice: 0.875 },
      { symbol: 'DOT/USDT', basePrice: 7.15 },
      { symbol: 'UNI/USDT', basePrice: 6.85 },
      { symbol: 'LTC/USDT', basePrice: 72.5 },
      { symbol: 'ATOM/USDT', basePrice: 9.25 },
      { symbol: 'FIL/USDT', basePrice: 5.45 },
      { symbol: 'NEAR/USDT', basePrice: 2.15 },
      { symbol: 'ALGO/USDT', basePrice: 0.185 },
      { symbol: 'VET/USDT', basePrice: 0.0285 },
      { symbol: 'ICP/USDT', basePrice: 12.8 },
      { symbol: 'HBAR/USDT', basePrice: 0.065 },
      { symbol: 'APT/USDT', basePrice: 8.95 },
      { symbol: 'ARB/USDT', basePrice: 1.25 },
      { symbol: 'OP/USDT', basePrice: 2.35 },
      { symbol: 'SUI/USDT', basePrice: 1.85 },
      { symbol: 'SEI/USDT', basePrice: 0.485 }
    ]
    
    const data: ExchangeData[] = []

    exchanges.forEach(exchange => {
      cryptoPairs.forEach(({ symbol, basePrice }) => {
        // Criar variação de preço entre exchanges (0.1% a 2%)
        const exchangeVariation = this.getExchangeVariation(exchange)
        const timeVariation = (Math.random() - 0.5) * 0.04 // ±2% variação temporal
        const price = basePrice * (1 + exchangeVariation + timeVariation)
        
        // Spread realista (0.01% a 0.5%)
        const spreadPercent = 0.0001 + Math.random() * 0.005
        const spread = price * spreadPercent
        
        const bid = price - spread / 2
        const ask = price + spread / 2
        
        // Volume realista baseado na popularidade do par
        const volumeMultiplier = this.getVolumeMultiplier(symbol)
        const baseVolume = 50000 + Math.random() * 500000
        const volume = baseVolume * volumeMultiplier
        
        data.push({
          exchange,
          symbol,
          type,
          price,
          volume,
          bid,
          ask,
          timestamp: new Date(),
          contractType: type === 'futures' ? 'PERP' : undefined,
          fundingRate: type === 'futures' ? this.generateFundingRate() : undefined,
          responseTime: 80 + Math.random() * 120, // 80-200ms
          dataQuality: 0.92 + Math.random() * 0.08, // 92-100%
          isStale: false
        })
      })
    })

    return data
  }

  private getExchangeVariation(exchange: Exchange): number {
    // Cada exchange tem uma tendência de preço ligeiramente diferente
    const variations = {
      gateio: -0.001 + Math.random() * 0.002, // -0.1% a +0.1%
      mexc: -0.0015 + Math.random() * 0.003, // -0.15% a +0.15%
      bitget: -0.0008 + Math.random() * 0.0016 // -0.08% a +0.08%
    }
    return variations[exchange]
  }

  private getVolumeMultiplier(symbol: string): number {
    // Pares mais populares têm volumes maiores
    const multipliers: Record<string, number> = {
      'BTC/USDT': 3.0,
      'ETH/USDT': 2.5,
      'BNB/USDT': 1.8,
      'SOL/USDT': 1.6,
      'XRP/USDT': 1.4,
      'ADA/USDT': 1.2,
      'DOGE/USDT': 1.3,
      'AVAX/USDT': 1.1,
      'LINK/USDT': 1.0,
      'MATIC/USDT': 1.1
    }
    return multipliers[symbol] || 0.8
  }

  private generateFundingRate(): number {
    // Funding rate realista entre -0.1% e +0.1%
    return (Math.random() - 0.5) * 0.002
  }

  /**
   * Método público para obter estatísticas do sistema
   */
  async getSystemStats() {
    try {
      const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:5001';
      const response = await fetch(`${backendUrl}/api/stats`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Backend retornou erro ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.message || 'Backend retornou erro');
      }

      return result.stats;

    } catch (error) {
      console.error('❌ Erro ao buscar estatísticas:', error);
      return null;
    }
  }
}

export default ExchangeAPI