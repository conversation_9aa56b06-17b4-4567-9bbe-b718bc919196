// SpreadCalculator Service Avançado para Arbitragem Cross-Exchange

import type {
  Exchange,
  ExchangeData,
  ArbitrageOpportunity,
  CrossExchangeSpreadResult,
  ArbitrageStrategy,
  Profitability,
  RiskLevel
} from '@/types/arbitrage'

import {
  getExchangeFee,
  classifyProfitability,
  isValidCrossExchangeOpportunity,
  getExchangeUrls,
  PROFITABILITY_CONFIG
} from '@/config/arbitrage'

export class SpreadCalculator {
  private static instance: SpreadCalculator

  // Singleton pattern para otimização
  public static getInstance(): SpreadCalculator {
    if (!SpreadCalculator.instance) {
      SpreadCalculator.instance = new SpreadCalculator()
    }
    return SpreadCalculator.instance
  }

  /**
   * Método principal para calcular spread cross-exchange
   * Calcula arbitragem entre exchanges diferentes usando bid/ask reais
   */
  public calculateCrossExchangeSpread(
    spotData: ExchangeData,
    futuresData: ExchangeData
  ): CrossExchangeSpreadResult {
    // Validar que são exchanges diferentes
    if (spotData.exchange === futuresData.exchange) {
      throw new Error('Cross-exchange arbitrage requires different exchanges')
    }

    // Validar dados de entrada
    this.validateExchangeData(spotData, futuresData)

    // Cálculo básico usando preços médios
    const rawSpread = futuresData.price - spotData.price
    const spreadPercentage = (rawSpread / spotData.price) * 100

    // Cálculo real usando bid/ask para maior precisão
    const realSpread = this.calculateRealSpread(spotData, futuresData)

    // Ajustar por taxas das exchanges
    const spotFee = getExchangeFee(spotData.exchange)
    const futuresFee = getExchangeFee(futuresData.exchange)
    const totalFees = spotFee + futuresFee

    // Calcular custos de transferência entre exchanges
    const transferCosts = this.calculateTransferCosts(spotData, futuresData)

    // Spread líquido após taxas e custos
    const netSpread = realSpread.netSpread - (spotData.price * totalFees / 100) - transferCosts
    const netSpreadPercentage = (netSpread / spotData.price) * 100

    // Ajuste por funding rate para futuros perpétuos
    const fundingAdjustment = this.calculateFundingRateImpact(futuresData)
    const adjustedNetSpread = netSpread - fundingAdjustment
    const adjustedNetSpreadPercentage = (adjustedNetSpread / spotData.price) * 100

    // Classificação de rentabilidade
    const profitability = classifyProfitability(adjustedNetSpreadPercentage)

    // Estratégia recomendada cross-exchange (só para spreads positivos)
    const strategy = this.generateCrossExchangeStrategy(
      spotData,
      futuresData,
      adjustedNetSpreadPercentage
    )

    // Estimativa de lucro e capital necessário
    const estimatedProfit = this.calculateEstimatedProfit(
      spotData.price,
      adjustedNetSpreadPercentage,
      1000 // $1000 como base de cálculo
    )

    const requiredCapital = this.calculateRequiredCapital(
      spotData.price,
      spotData.volume,
      futuresData.volume
    )

    return {
      rawSpread,
      spreadPercentage,
      netSpread: adjustedNetSpread,
      netSpreadPercentage: adjustedNetSpreadPercentage,
      profitability,
      strategy,
      fees: {
        spot: spotFee,
        futures: futuresFee,
        total: totalFees
      },
      estimatedProfit,
      requiredCapital
    }
  }

  /**
   * Calcula spread real usando bid/ask de exchanges diferentes
   * Mais preciso que usar apenas preços médios
   */
  public calculateRealSpread(
    spotData: ExchangeData,
    futuresData: ExchangeData
  ): { realSpread: number; netSpread: number; executionRisk: number } {
    // Para comprar spot e vender futuros
    const spotBuyPrice = spotData.ask // Preço para comprar spot
    const futuresSellPrice = futuresData.bid // Preço para vender futuros

    // Para vender spot e comprar futuros (arbitragem reversa)
    const spotSellPrice = spotData.bid // Preço para vender spot
    const futuresBuyPrice = futuresData.ask // Preço para comprar futuros

    // Calcular ambas as direções
    const longSpotShortFutures = futuresSellPrice - spotBuyPrice
    const shortSpotLongFutures = spotSellPrice - futuresBuyPrice

    // Escolher a direção mais lucrativa
    const realSpread = Math.abs(longSpotShortFutures) > Math.abs(shortSpotLongFutures)
      ? longSpotShortFutures
      : shortSpotLongFutures

    // Calcular spread líquido considerando slippage
    const slippage = this.calculateSlippage(spotData, futuresData)
    const netSpread = realSpread - slippage

    // Calcular risco de execução baseado na diferença bid/ask
    const spotSpread = (spotData.ask - spotData.bid) / spotData.price
    const futuresSpread = (futuresData.ask - futuresData.bid) / futuresData.price
    const executionRisk = (spotSpread + futuresSpread) / 2

    return {
      realSpread,
      netSpread,
      executionRisk
    }
  }

  /**
   * Calcula custos de transferência entre exchanges
   */
  public calculateTransferCosts(
    spotData: ExchangeData,
    futuresData: ExchangeData
  ): number {
    // Custos estimados de transferência por exchange (em USD)
    const transferCosts: Record<Exchange, number> = {
      gateio: 25, // $25 para transferência
      mexc: 20,   // $20 para transferência
      bitget: 15  // $15 para transferência
    }

    // Custo médio entre as duas exchanges
    const avgTransferCost = (
      transferCosts[spotData.exchange] +
      transferCosts[futuresData.exchange]
    ) / 2

    // Ajustar baseado no volume (menor custo relativo para volumes maiores)
    const minVolume = Math.min(spotData.volume, futuresData.volume)
    const volumeAdjustment = minVolume / 100000 // Normalizar para $100k
    const adjustedCost = avgTransferCost * (1 - Math.min(volumeAdjustment * 0.2, 0.6))

    return adjustedCost
  }

  /**
   * Calcula impacto do funding rate para futuros perpétuos
   */
  private calculateFundingRateImpact(futuresData: ExchangeData): number {
    if (!futuresData.fundingRate || futuresData.contractType !== 'PERP') {
      return 0
    }

    // Funding rate é cobrado a cada 8 horas
    // Impacto estimado para posição de 1 dia (3 pagamentos)
    const dailyFundingImpact = futuresData.fundingRate * 3

    // Converter para valor absoluto em USD
    return Math.abs(dailyFundingImpact * futuresData.price / 100)
  }

  /**
   * Calcula slippage estimado baseado no volume
   */
  private calculateSlippage(
    spotData: ExchangeData,
    futuresData: ExchangeData
  ): number {
    // Slippage baseado na liquidez disponível
    const spotLiquidity = spotData.volume
    const futuresLiquidity = futuresData.volume

    // Menor liquidez determina o slippage
    const minLiquidity = Math.min(spotLiquidity, futuresLiquidity)

    // Slippage inversamente proporcional à liquidez
    // Mais liquidez = menos slippage
    const slippagePercentage = Math.max(0.01, 100 / minLiquidity) // Mínimo 0.01%

    // Converter para valor absoluto
    return (slippagePercentage / 100) * spotData.price
  }

  /**
   * Gera estratégia cross-exchange recomendada (só para spreads positivos)
   */
  private generateCrossExchangeStrategy(
    spotData: ExchangeData,
    futuresData: ExchangeData,
    spreadPercentage: number
  ): ArbitrageStrategy {
    const symbol = spotData.symbol
    const spotExchange = this.formatExchangeName(spotData.exchange)
    const futuresExchange = this.formatExchangeName(futuresData.exchange)

    const estimatedProfit = this.calculateEstimatedProfit(spotData.price, spreadPercentage, 1000)
    const requiredCapital = this.calculateRequiredCapital(
      spotData.price,
      spotData.volume,
      futuresData.volume
    )

    // Só considerar spreads positivos (futuros mais caro que spot)
    return {
      action1: `Buy ${symbol} spot on ${spotExchange}`,
      action2: `Short ${symbol} futures on ${futuresExchange}`,
      exitCondition: 'When futures price converges to spot price',
      estimatedProfit,
      requiredCapital,
      timeHorizon: this.estimateTimeHorizon(Math.abs(spreadPercentage))
    }
  }

  /**
   * Valida oportunidade cross-exchange específica
   */
  public isValidCrossExchangeOpportunity(
    spotData: ExchangeData,
    futuresData: ExchangeData,
    spreadResult: CrossExchangeSpreadResult
  ): boolean {
    // Usar validação da configuração
    const basicValidation = isValidCrossExchangeOpportunity(
      spotData.exchange,
      futuresData.exchange,
      spreadResult.netSpreadPercentage,
      Math.min(spotData.volume, futuresData.volume),
      Math.max(
        Date.now() - spotData.timestamp.getTime(),
        Date.now() - futuresData.timestamp.getTime()
      )
    )

    if (!basicValidation) return false

    // Validações adicionais específicas

    // 1. Verificar se há liquidez suficiente em ambas as exchanges
    const minVolume = Math.min(spotData.volume, futuresData.volume)
    if (minVolume < 1000) return false // Mínimo $1,000

    // 2. Verificar se o spread é significativo após custos
    if (Math.abs(spreadResult.netSpreadPercentage) < 0.05) return false

    // 3. Verificar se não há risco excessivo de execução
    const realSpread = this.calculateRealSpread(spotData, futuresData)
    if (realSpread.executionRisk > 0.5) return false // Máximo 0.5% de risco

    // 4. Verificar se os dados não estão muito desatualizados
    const maxAge = 15000 // 15 segundos
    const spotAge = Date.now() - spotData.timestamp.getTime()
    const futuresAge = Date.now() - futuresData.timestamp.getTime()
    if (spotAge > maxAge || futuresAge > maxAge) return false

    return true
  }

  /**
   * Classifica rentabilidade com lógica específica
   */
  public classifyRentability(spreadPercentage: number): Profitability {
    const absSpread = Math.abs(spreadPercentage)

    if (absSpread >= PROFITABILITY_CONFIG.HIGH.threshold) return 'high'
    if (absSpread >= PROFITABILITY_CONFIG.MEDIUM.threshold) return 'medium'
    return 'low'
  }



  /**
   * Calcula lucro estimado para um capital específico
   */
  private calculateEstimatedProfit(
    price: number,
    spreadPercentage: number,
    capital: number
  ): number {
    const quantity = capital / price
    return quantity * price * (Math.abs(spreadPercentage) / 100)
  }

  /**
   * Calcula capital necessário baseado na liquidez disponível
   */
  private calculateRequiredCapital(
    price: number,
    spotVolume: number,
    futuresVolume: number
  ): number {
    // Capital recomendado é 10% do menor volume disponível
    const minVolume = Math.min(spotVolume, futuresVolume)
    return Math.min(minVolume * 0.1, 10000) // Máximo $10,000
  }

  /**
   * Estima horizonte temporal para convergência
   */
  private estimateTimeHorizon(spreadPercentage: number): string {
    if (spreadPercentage >= 2.0) return '1-4 hours' // Spreads grandes convergem rápido
    if (spreadPercentage >= 1.0) return '4-12 hours'
    if (spreadPercentage >= 0.5) return '12-24 hours'
    return '1-3 days' // Spreads pequenos podem demorar mais
  }

  /**
   * Formata nome da exchange para exibição
   */
  private formatExchangeName(exchange: Exchange): string {
    const names: Record<Exchange, string> = {
      gateio: 'Gate.io',
      mexc: 'MEXC',
      bitget: 'Bitget'
    }
    return names[exchange]
  }

  /**
   * Valida dados de entrada das exchanges
   */
  private validateExchangeData(spotData: ExchangeData, futuresData: ExchangeData): void {
    if (!spotData || !futuresData) {
      throw new Error('Both spot and futures data are required')
    }

    if (spotData.symbol !== futuresData.symbol) {
      throw new Error('Symbol mismatch between spot and futures data')
    }

    if (spotData.price <= 0 || futuresData.price <= 0) {
      throw new Error('Invalid price data')
    }

    if (spotData.volume <= 0 || futuresData.volume <= 0) {
      throw new Error('Invalid volume data')
    }

    if (!spotData.bid || !spotData.ask || !futuresData.bid || !futuresData.ask) {
      throw new Error('Bid/Ask data is required for accurate calculations')
    }
  }
}