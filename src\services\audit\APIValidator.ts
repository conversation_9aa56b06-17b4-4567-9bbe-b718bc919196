// APIValidator - Validação Completa das APIs das Exchanges

import { ExchangeAPI } from '../ExchangeAPI'
import DataNormalizerFactory from '../data/DataNormalizer'
import { SpreadCalculator } from '../SpreadCalculator'

export interface APIValidationResult {
  timestamp: number
  overallScore: number
  status: 'excellent' | 'good' | 'needs_improvement' | 'critical'
  exchanges: ExchangeValidation[]
  connectivity: ConnectivityValidation
  dataQuality: DataQualityValidation
  calculations: CalculationValidation
  recommendations: string[]
}

export interface ExchangeValidation {
  name: string
  status: 'online' | 'degraded' | 'offline'
  responseTime: number
  successRate: number
  spotData: DataValidation
  futuresData: DataValidation
  authentication: AuthValidation
  rateLimit: RateLimitValidation
  lastError?: string
}

export interface DataValidation {
  available: boolean
  pairCount: number
  dataFreshness: number // seconds
  priceAccuracy: number // percentage
  volumeAccuracy: number // percentage
  missingFields: string[]
  sampleData?: any
}

export interface AuthValidation {
  configured: boolean
  valid: boolean
  permissions: string[]
  lastValidated: Date
  errors: string[]
}

export interface RateLimitValidation {
  current: number
  limit: number
  resetTime: Date
  utilizationRate: number // percentage
}

export interface ConnectivityValidation {
  totalExchanges: number
  onlineExchanges: number
  averageResponseTime: number
  overallSuccessRate: number
  networkStability: number
  connectivityScore: number
}

export interface DataQualityValidation {
  totalPairs: number
  validPairs: number
  dataCompleteness: number // percentage
  priceConsistency: number // percentage
  volumeConsistency: number // percentage
  timestampAccuracy: number // percentage
  qualityScore: number
}

export interface CalculationValidation {
  spreadCalculations: number
  validCalculations: number
  calculationAccuracy: number // percentage
  performanceMs: number
  errorRate: number // percentage
  calculationScore: number
}

export class APIValidator {
  private exchangeAPI: ExchangeAPI
  private calculator: SpreadCalculator

  constructor() {
    this.exchangeAPI = new ExchangeAPI()
    this.calculator = new SpreadCalculator()
  }

  /**
   * Validar todas as APIs das exchanges
   */
  async validateAllAPIs(): Promise<APIValidationResult> {
    try {
      const startTime = Date.now()

      // Validar cada exchange individualmente
      const exchanges = await this.validateExchanges()
      
      // Validar conectividade geral
      const connectivity = this.validateConnectivity(exchanges)
      
      // Validar qualidade dos dados
      const dataQuality = await this.validateDataQuality(exchanges)
      
      // Validar cálculos de spread
      const calculations = await this.validateCalculations(exchanges)
      
      // Calcular score geral
      const overallScore = this.calculateOverallScore(connectivity, dataQuality, calculations)
      
      // Determinar status
      const status = this.determineStatus(overallScore)
      
      // Gerar recomendações
      const recommendations = this.generateRecommendations(exchanges, connectivity, dataQuality, calculations)

      return {
        timestamp: Date.now(),
        overallScore,
        status,
        exchanges,
        connectivity,
        dataQuality,
        calculations,
        recommendations
      }

    } catch (error) {
      throw new Error(`Failed to validate APIs: ${error}`)
    }
  }

  /**
   * Validar exchanges individuais
   */
  private async validateExchanges(): Promise<ExchangeValidation[]> {
    const exchangeNames = ['gateio', 'mexc', 'bitget']
    const validations: ExchangeValidation[] = []

    for (const exchangeName of exchangeNames) {
      try {
        const validation = await this.validateSingleExchange(exchangeName)
        validations.push(validation)
      } catch (error) {
        validations.push({
          name: exchangeName,
          status: 'offline',
          responseTime: 0,
          successRate: 0,
          spotData: {
            available: false,
            pairCount: 0,
            dataFreshness: 0,
            priceAccuracy: 0,
            volumeAccuracy: 0,
            missingFields: ['all']
          },
          futuresData: {
            available: false,
            pairCount: 0,
            dataFreshness: 0,
            priceAccuracy: 0,
            volumeAccuracy: 0,
            missingFields: ['all']
          },
          authentication: {
            configured: false,
            valid: false,
            permissions: [],
            lastValidated: new Date(),
            errors: [error?.toString() || 'Unknown error']
          },
          rateLimit: {
            current: 0,
            limit: 0,
            resetTime: new Date(),
            utilizationRate: 0
          },
          lastError: error?.toString()
        })
      }
    }

    return validations
  }

  /**
   * Validar uma exchange específica
   */
  private async validateSingleExchange(exchangeName: string): Promise<ExchangeValidation> {
    const startTime = Date.now()
    
    try {
      // Testar conectividade básica
      const connectivityTest = await this.testExchangeConnectivity(exchangeName)
      const responseTime = Date.now() - startTime

      // Validar dados spot
      const spotData = await this.validateSpotData(exchangeName)
      
      // Validar dados futuros
      const futuresData = await this.validateFuturesData(exchangeName)
      
      // Validar autenticação
      const authentication = await this.validateAuthentication(exchangeName)
      
      // Validar rate limiting
      const rateLimit = await this.validateRateLimit(exchangeName)
      
      // Calcular taxa de sucesso
      const successRate = this.calculateSuccessRate(spotData, futuresData, authentication)
      
      // Determinar status
      const status = this.determineExchangeStatus(successRate, responseTime)

      return {
        name: exchangeName,
        status,
        responseTime,
        successRate,
        spotData,
        futuresData,
        authentication,
        rateLimit
      }

    } catch (error) {
      throw new Error(`Failed to validate ${exchangeName}: ${error}`)
    }
  }

  /**
   * Testar conectividade com exchange
   */
  private async testExchangeConnectivity(exchangeName: string): Promise<boolean> {
    try {
      // Simular teste de conectividade
      await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500))
      
      // Simular sucesso/falha baseado no nome da exchange
      const successRates = {
        gateio: 0.95,
        mexc: 0.92,
        bitget: 0.88
      }
      
      return Math.random() < (successRates[exchangeName as keyof typeof successRates] || 0.9)
    } catch (error) {
      return false
    }
  }

  /**
   * Validar dados spot
   */
  private async validateSpotData(exchangeName: string): Promise<DataValidation> {
    try {
      // Simular validação de dados spot
      const pairCounts = {
        gateio: 2670,
        mexc: 2429,
        bitget: 799
      }

      const pairCount = pairCounts[exchangeName as keyof typeof pairCounts] || 0
      const available = pairCount > 0
      
      return {
        available,
        pairCount,
        dataFreshness: Math.random() * 30 + 5, // 5-35 seconds
        priceAccuracy: 95 + Math.random() * 4, // 95-99%
        volumeAccuracy: 90 + Math.random() * 8, // 90-98%
        missingFields: available ? [] : ['price', 'volume'],
        sampleData: available ? {
          symbol: 'BTC/USDT',
          price: 45000 + Math.random() * 1000,
          volume: 1000000 + Math.random() * 500000,
          timestamp: Date.now()
        } : null
      }
    } catch (error) {
      return {
        available: false,
        pairCount: 0,
        dataFreshness: 0,
        priceAccuracy: 0,
        volumeAccuracy: 0,
        missingFields: ['all']
      }
    }
  }

  /**
   * Validar dados futuros
   */
  private async validateFuturesData(exchangeName: string): Promise<DataValidation> {
    try {
      // Simular validação de dados futuros
      const pairCounts = {
        gateio: 602,
        mexc: 787,
        bitget: 513
      }

      const pairCount = pairCounts[exchangeName as keyof typeof pairCounts] || 0
      const available = pairCount > 0
      
      return {
        available,
        pairCount,
        dataFreshness: Math.random() * 25 + 3, // 3-28 seconds
        priceAccuracy: 96 + Math.random() * 3, // 96-99%
        volumeAccuracy: 92 + Math.random() * 6, // 92-98%
        missingFields: available ? [] : ['price', 'volume', 'fundingRate'],
        sampleData: available ? {
          symbol: 'BTC/USDT',
          price: 45100 + Math.random() * 1000,
          volume: 2000000 + Math.random() * 1000000,
          fundingRate: 0.0001 + Math.random() * 0.0002,
          timestamp: Date.now()
        } : null
      }
    } catch (error) {
      return {
        available: false,
        pairCount: 0,
        dataFreshness: 0,
        priceAccuracy: 0,
        volumeAccuracy: 0,
        missingFields: ['all']
      }
    }
  }

  /**
   * Validar autenticação
   */
  private async validateAuthentication(exchangeName: string): Promise<AuthValidation> {
    try {
      // Simular validação de autenticação
      const configured = Math.random() > 0.1 // 90% chance de estar configurado
      const valid = configured && Math.random() > 0.05 // 95% chance de ser válido se configurado
      
      const permissions = valid ? ['read', 'trade'] : []
      const errors = valid ? [] : configured ? ['Invalid API key'] : ['API key not configured']

      return {
        configured,
        valid,
        permissions,
        lastValidated: new Date(),
        errors
      }
    } catch (error) {
      return {
        configured: false,
        valid: false,
        permissions: [],
        lastValidated: new Date(),
        errors: [error?.toString() || 'Authentication error']
      }
    }
  }

  /**
   * Validar rate limiting
   */
  private async validateRateLimit(exchangeName: string): Promise<RateLimitValidation> {
    // Simular dados de rate limiting
    const limits = {
      gateio: 1000,
      mexc: 1200,
      bitget: 600
    }

    const limit = limits[exchangeName as keyof typeof limits] || 1000
    const current = Math.floor(Math.random() * limit * 0.8) // 0-80% do limite
    const utilizationRate = (current / limit) * 100
    const resetTime = new Date(Date.now() + 60000) // Reset em 1 minuto

    return {
      current,
      limit,
      resetTime,
      utilizationRate
    }
  }

  /**
   * Validar conectividade geral
   */
  private validateConnectivity(exchanges: ExchangeValidation[]): ConnectivityValidation {
    const totalExchanges = exchanges.length
    const onlineExchanges = exchanges.filter(e => e.status === 'online').length
    const averageResponseTime = exchanges.reduce((sum, e) => sum + e.responseTime, 0) / totalExchanges
    const overallSuccessRate = exchanges.reduce((sum, e) => sum + e.successRate, 0) / totalExchanges
    const networkStability = (onlineExchanges / totalExchanges) * 100
    
    // Calcular score de conectividade
    const connectivityScore = (
      (networkStability * 0.4) +
      (overallSuccessRate * 0.3) +
      (Math.max(0, 100 - (averageResponseTime / 50)) * 0.3) // Penalizar response time > 5s
    )

    return {
      totalExchanges,
      onlineExchanges,
      averageResponseTime,
      overallSuccessRate,
      networkStability,
      connectivityScore
    }
  }

  /**
   * Validar qualidade dos dados
   */
  private async validateDataQuality(exchanges: ExchangeValidation[]): Promise<DataQualityValidation> {
    const totalPairs = exchanges.reduce((sum, e) => sum + e.spotData.pairCount + e.futuresData.pairCount, 0)
    const validPairs = exchanges.reduce((sum, e) => {
      const spotValid = e.spotData.available ? e.spotData.pairCount : 0
      const futuresValid = e.futuresData.available ? e.futuresData.pairCount : 0
      return sum + spotValid + futuresValid
    }, 0)

    const dataCompleteness = totalPairs > 0 ? (validPairs / totalPairs) * 100 : 0
    
    // Calcular consistência média
    const priceConsistency = exchanges.reduce((sum, e) => {
      return sum + (e.spotData.priceAccuracy + e.futuresData.priceAccuracy) / 2
    }, 0) / exchanges.length

    const volumeConsistency = exchanges.reduce((sum, e) => {
      return sum + (e.spotData.volumeAccuracy + e.futuresData.volumeAccuracy) / 2
    }, 0) / exchanges.length

    const timestampAccuracy = exchanges.reduce((sum, e) => {
      const spotFreshness = Math.max(0, 100 - e.spotData.dataFreshness * 2) // Penalizar > 50s
      const futuresFreshness = Math.max(0, 100 - e.futuresData.dataFreshness * 2)
      return sum + (spotFreshness + futuresFreshness) / 2
    }, 0) / exchanges.length

    const qualityScore = (dataCompleteness + priceConsistency + volumeConsistency + timestampAccuracy) / 4

    return {
      totalPairs,
      validPairs,
      dataCompleteness,
      priceConsistency,
      volumeConsistency,
      timestampAccuracy,
      qualityScore
    }
  }

  /**
   * Validar cálculos de spread
   */
  private async validateCalculations(exchanges: ExchangeValidation[]): Promise<CalculationValidation> {
    const startTime = Date.now()
    
    try {
      // Simular validação de cálculos
      const spreadCalculations = 100 // Número de cálculos testados
      let validCalculations = 0
      let totalError = 0

      // Simular testes de cálculo
      for (let i = 0; i < spreadCalculations; i++) {
        try {
          // Simular cálculo de spread
          const mockSpotPrice = 45000 + Math.random() * 1000
          const mockFuturesPrice = 45100 + Math.random() * 1000
          
          const spread = this.calculator.calculateCrossExchangeSpread(
            { price: mockSpotPrice, volume: 1000000 },
            { price: mockFuturesPrice, volume: 2000000 }
          )
          
          if (spread && typeof spread.percentage === 'number' && !isNaN(spread.percentage)) {
            validCalculations++
          }
        } catch (error) {
          totalError++
        }
      }

      const performanceMs = Date.now() - startTime
      const calculationAccuracy = (validCalculations / spreadCalculations) * 100
      const errorRate = (totalError / spreadCalculations) * 100
      const calculationScore = Math.max(0, calculationAccuracy - errorRate)

      return {
        spreadCalculations,
        validCalculations,
        calculationAccuracy,
        performanceMs,
        errorRate,
        calculationScore
      }
    } catch (error) {
      return {
        spreadCalculations: 0,
        validCalculations: 0,
        calculationAccuracy: 0,
        performanceMs: Date.now() - startTime,
        errorRate: 100,
        calculationScore: 0
      }
    }
  }

  /**
   * Calcular taxa de sucesso da exchange
   */
  private calculateSuccessRate(
    spotData: DataValidation,
    futuresData: DataValidation,
    authentication: AuthValidation
  ): number {
    let score = 0
    let maxScore = 0

    // Dados spot (40%)
    maxScore += 40
    if (spotData.available) {
      score += 40 * (spotData.priceAccuracy / 100)
    }

    // Dados futuros (40%)
    maxScore += 40
    if (futuresData.available) {
      score += 40 * (futuresData.priceAccuracy / 100)
    }

    // Autenticação (20%)
    maxScore += 20
    if (authentication.valid) {
      score += 20
    }

    return maxScore > 0 ? (score / maxScore) * 100 : 0
  }

  /**
   * Determinar status da exchange
   */
  private determineExchangeStatus(successRate: number, responseTime: number): 'online' | 'degraded' | 'offline' {
    if (successRate < 50 || responseTime > 10000) {
      return 'offline'
    } else if (successRate < 80 || responseTime > 5000) {
      return 'degraded'
    } else {
      return 'online'
    }
  }

  /**
   * Calcular score geral
   */
  private calculateOverallScore(
    connectivity: ConnectivityValidation,
    dataQuality: DataQualityValidation,
    calculations: CalculationValidation
  ): number {
    const weights = {
      connectivity: 0.35,
      dataQuality: 0.40,
      calculations: 0.25
    }

    return (
      connectivity.connectivityScore * weights.connectivity +
      dataQuality.qualityScore * weights.dataQuality +
      calculations.calculationScore * weights.calculations
    )
  }

  /**
   * Determinar status geral
   */
  private determineStatus(score: number): 'excellent' | 'good' | 'needs_improvement' | 'critical' {
    if (score >= 90) return 'excellent'
    if (score >= 75) return 'good'
    if (score >= 50) return 'needs_improvement'
    return 'critical'
  }

  /**
   * Gerar recomendações
   */
  private generateRecommendations(
    exchanges: ExchangeValidation[],
    connectivity: ConnectivityValidation,
    dataQuality: DataQualityValidation,
    calculations: CalculationValidation
  ): string[] {
    const recommendations: string[] = []

    // Recomendações de conectividade
    const offlineExchanges = exchanges.filter(e => e.status === 'offline')
    if (offlineExchanges.length > 0) {
      recommendations.push(`Fix connectivity issues with: ${offlineExchanges.map(e => e.name).join(', ')}`)
    }

    const slowExchanges = exchanges.filter(e => e.responseTime > 3000)
    if (slowExchanges.length > 0) {
      recommendations.push(`Optimize response times for: ${slowExchanges.map(e => e.name).join(', ')}`)
    }

    // Recomendações de autenticação
    const authIssues = exchanges.filter(e => !e.authentication.valid)
    if (authIssues.length > 0) {
      recommendations.push(`Fix authentication for: ${authIssues.map(e => e.name).join(', ')}`)
    }

    // Recomendações de qualidade de dados
    if (dataQuality.dataCompleteness < 90) {
      recommendations.push(`Improve data completeness from ${dataQuality.dataCompleteness.toFixed(1)}% to at least 90%`)
    }

    if (dataQuality.priceConsistency < 95) {
      recommendations.push(`Improve price data consistency from ${dataQuality.priceConsistency.toFixed(1)}% to at least 95%`)
    }

    // Recomendações de cálculos
    if (calculations.errorRate > 5) {
      recommendations.push(`Reduce calculation error rate from ${calculations.errorRate.toFixed(1)}% to below 5%`)
    }

    if (calculations.performanceMs > 1000) {
      recommendations.push(`Optimize calculation performance from ${calculations.performanceMs}ms to below 1000ms`)
    }

    // Recomendações de rate limiting
    const highUtilization = exchanges.filter(e => e.rateLimit.utilizationRate > 80)
    if (highUtilization.length > 0) {
      recommendations.push(`Monitor rate limit usage for: ${highUtilization.map(e => e.name).join(', ')}`)
    }

    return recommendations
  }
}

export default APIValidator