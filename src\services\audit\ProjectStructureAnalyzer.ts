// ProjectStructureAnalyzer - Análise Completa da Estrutura do Projeto

import { readdir, stat, readFile } from 'fs/promises'
import { join, extname, relative } from 'path'

export interface ProjectStructureAnalysis {
  timestamp: number
  overallScore: number
  status: 'excellent' | 'good' | 'needs_improvement' | 'critical'
  structure: StructureAnalysis
  dependencies: DependencyAnalysis
  codeQuality: CodeQualityAnalysis
  organization: OrganizationAnalysis
  recommendations: string[]
}

export interface StructureAnalysis {
  totalFiles: number
  totalDirectories: number
  filesByType: Record<string, number>
  directoryStructure: DirectoryNode[]
  missingDirectories: string[]
  unexpectedFiles: string[]
  structureScore: number
}

export interface DirectoryNode {
  name: string
  path: string
  type: 'directory' | 'file'
  size?: number
  children?: DirectoryNode[]
  purpose?: string
}

export interface DependencyAnalysis {
  totalDependencies: number
  productionDeps: number
  devDependencies: number
  outdatedDeps: string[]
  vulnerabilities: string[]
  unusedDeps: string[]
  dependencyScore: number
  packageJsonExists: boolean
  lockFileExists: boolean
}

export interface CodeQualityAnalysis {
  totalLinesOfCode: number
  codeFiles: number
  testFiles: number
  testCoverage: number
  duplicatedCode: number
  complexityScore: number
  maintainabilityIndex: number
  technicalDebt: string[]
  codeQualityScore: number
}

export interface OrganizationAnalysis {
  followsConventions: boolean
  hasProperNaming: boolean
  hasDocumentation: boolean
  hasTests: boolean
  hasTypeScript: boolean
  hasLinting: boolean
  hasFormatting: boolean
  organizationScore: number
  issues: string[]
}

export class ProjectStructureAnalyzer {
  private projectRoot: string
  private expectedStructure = {
    directories: [
      'src',
      'src/components',
      'src/hooks',
      'src/services',
      'src/types',
      'src/utils',
      'src/config',
      'src/__tests__',
      'public',
      'node_modules'
    ],
    files: [
      'package.json',
      'package-lock.json',
      'tsconfig.json',
      'vite.config.ts',
      'tailwind.config.js',
      'README.md'
    ]
  }

  constructor(projectRoot: string = '.') {
    this.projectRoot = projectRoot
  }

  /**
   * Analisar estrutura completa do projeto
   */
  async analyzeProject(): Promise<ProjectStructureAnalysis> {
    try {
      // Analisar estrutura de diretórios
      const structure = await this.analyzeStructure()
      
      // Analisar dependências
      const dependencies = await this.analyzeDependencies()
      
      // Analisar qualidade do código
      const codeQuality = await this.analyzeCodeQuality()
      
      // Analisar organização
      const organization = await this.analyzeOrganization()
      
      // Calcular score geral
      const overallScore = this.calculateOverallScore(
        structure, dependencies, codeQuality, organization
      )
      
      // Determinar status
      const status = this.determineStatus(overallScore)
      
      // Gerar recomendações
      const recommendations = this.generateRecommendations(
        structure, dependencies, codeQuality, organization
      )

      return {
        timestamp: Date.now(),
        overallScore,
        status,
        structure,
        dependencies,
        codeQuality,
        organization,
        recommendations
      }

    } catch (error) {
      throw new Error(`Failed to analyze project structure: ${error}`)
    }
  }

  /**
   * Analisar estrutura de diretórios
   */
  private async analyzeStructure(): Promise<StructureAnalysis> {
    const directoryStructure = await this.buildDirectoryTree(this.projectRoot)
    const { totalFiles, totalDirectories, filesByType } = await this.countFilesAndDirectories()
    
    // Verificar diretórios esperados
    const missingDirectories: string[] = []
    for (const expectedDir of this.expectedStructure.directories) {
      try {
        await stat(join(this.projectRoot, expectedDir))
      } catch {
        missingDirectories.push(expectedDir)
      }
    }
    
    // Verificar arquivos inesperados na raiz
    const rootFiles = await readdir(this.projectRoot)
    const unexpectedFiles = rootFiles.filter(file => {
      const isExpected = this.expectedStructure.files.some(expected => 
        file === expected || file.startsWith('.') || file === 'node_modules'
      )
      return !isExpected && !file.includes('FASE') && !file.includes('TASK')
    })
    
    // Calcular score da estrutura
    const structureScore = this.calculateStructureScore(
      missingDirectories, unexpectedFiles, totalFiles, totalDirectories
    )

    return {
      totalFiles,
      totalDirectories,
      filesByType,
      directoryStructure,
      missingDirectories,
      unexpectedFiles,
      structureScore
    }
  }

  /**
   * Construir árvore de diretórios
   */
  private async buildDirectoryTree(dirPath: string, maxDepth: number = 3, currentDepth: number = 0): Promise<DirectoryNode[]> {
    if (currentDepth >= maxDepth) return []
    
    try {
      const items = await readdir(dirPath)
      const nodes: DirectoryNode[] = []
      
      for (const item of items) {
        if (item.startsWith('.') || item === 'node_modules') continue
        
        const itemPath = join(dirPath, item)
        const stats = await stat(itemPath)
        const relativePath = relative(this.projectRoot, itemPath)
        
        if (stats.isDirectory()) {
          const children = await this.buildDirectoryTree(itemPath, maxDepth, currentDepth + 1)
          nodes.push({
            name: item,
            path: relativePath,
            type: 'directory',
            children,
            purpose: this.getDirectoryPurpose(item)
          })
        } else {
          nodes.push({
            name: item,
            path: relativePath,
            type: 'file',
            size: stats.size
          })
        }
      }
      
      return nodes.sort((a, b) => {
        if (a.type !== b.type) {
          return a.type === 'directory' ? -1 : 1
        }
        return a.name.localeCompare(b.name)
      })
      
    } catch (error) {
      return []
    }
  }

  /**
   * Contar arquivos e diretórios
   */
  private async countFilesAndDirectories(): Promise<{
    totalFiles: number
    totalDirectories: number
    filesByType: Record<string, number>
  }> {
    let totalFiles = 0
    let totalDirectories = 0
    const filesByType: Record<string, number> = {}
    
    const countRecursive = async (dirPath: string) => {
      try {
        const items = await readdir(dirPath)
        
        for (const item of items) {
          if (item.startsWith('.') || item === 'node_modules') continue
          
          const itemPath = join(dirPath, item)
          const stats = await stat(itemPath)
          
          if (stats.isDirectory()) {
            totalDirectories++
            await countRecursive(itemPath)
          } else {
            totalFiles++
            const ext = extname(item).toLowerCase()
            filesByType[ext] = (filesByType[ext] || 0) + 1
          }
        }
      } catch (error) {
        // Ignorar erros de acesso
      }
    }
    
    await countRecursive(this.projectRoot)
    
    return { totalFiles, totalDirectories, filesByType }
  }

  /**
   * Analisar dependências
   */
  private async analyzeDependencies(): Promise<DependencyAnalysis> {
    let packageJsonExists = false
    let lockFileExists = false
    let totalDependencies = 0
    let productionDeps = 0
    let devDependencies = 0
    
    try {
      // Verificar package.json
      const packageJsonPath = join(this.projectRoot, 'package.json')
      const packageJsonContent = await readFile(packageJsonPath, 'utf-8')
      const packageJson = JSON.parse(packageJsonContent)
      packageJsonExists = true
      
      // Contar dependências
      if (packageJson.dependencies) {
        productionDeps = Object.keys(packageJson.dependencies).length
      }
      if (packageJson.devDependencies) {
        devDependencies = Object.keys(packageJson.devDependencies).length
      }
      totalDependencies = productionDeps + devDependencies
      
    } catch (error) {
      // package.json não existe ou é inválido
    }
    
    try {
      // Verificar lock file
      await stat(join(this.projectRoot, 'package-lock.json'))
      lockFileExists = true
    } catch {
      try {
        await stat(join(this.projectRoot, 'yarn.lock'))
        lockFileExists = true
      } catch {
        // Nenhum lock file encontrado
      }
    }
    
    // Análises simplificadas (em produção, usaria ferramentas como npm audit)
    const outdatedDeps: string[] = []
    const vulnerabilities: string[] = []
    const unusedDeps: string[] = []
    
    // Calcular score das dependências
    const dependencyScore = this.calculateDependencyScore(
      packageJsonExists, lockFileExists, totalDependencies, outdatedDeps, vulnerabilities
    )

    return {
      totalDependencies,
      productionDeps,
      devDependencies,
      outdatedDeps,
      vulnerabilities,
      unusedDeps,
      dependencyScore,
      packageJsonExists,
      lockFileExists
    }
  }

  /**
   * Analisar qualidade do código
   */
  private async analyzeCodeQuality(): Promise<CodeQualityAnalysis> {
    let totalLinesOfCode = 0
    let codeFiles = 0
    let testFiles = 0
    
    const codeExtensions = ['.ts', '.tsx', '.js', '.jsx']
    const testPatterns = ['.test.', '.spec.', '__tests__']
    
    const analyzeDirectory = async (dirPath: string) => {
      try {
        const items = await readdir(dirPath)
        
        for (const item of items) {
          if (item.startsWith('.') || item === 'node_modules') continue
          
          const itemPath = join(dirPath, item)
          const stats = await stat(itemPath)
          
          if (stats.isDirectory()) {
            await analyzeDirectory(itemPath)
          } else {
            const ext = extname(item).toLowerCase()
            if (codeExtensions.includes(ext)) {
              const content = await readFile(itemPath, 'utf-8')
              const lines = content.split('\n').length
              totalLinesOfCode += lines
              
              if (testPatterns.some(pattern => item.includes(pattern))) {
                testFiles++
              } else {
                codeFiles++
              }
            }
          }
        }
      } catch (error) {
        // Ignorar erros de acesso
      }
    }
    
    await analyzeDirectory(join(this.projectRoot, 'src'))
    
    // Métricas estimadas (em produção, usaria ferramentas como SonarQube)
    const testCoverage = testFiles > 0 ? Math.min(85, (testFiles / codeFiles) * 100) : 0
    const duplicatedCode = Math.max(0, Math.floor(totalLinesOfCode * 0.02)) // 2% estimado
    const complexityScore = Math.max(70, 100 - Math.floor(totalLinesOfCode / 1000))
    const maintainabilityIndex = Math.max(60, 100 - Math.floor(totalLinesOfCode / 500))
    
    const technicalDebt: string[] = []
    if (testCoverage < 70) technicalDebt.push('Low test coverage')
    if (duplicatedCode > totalLinesOfCode * 0.05) technicalDebt.push('High code duplication')
    if (complexityScore < 70) technicalDebt.push('High complexity')
    
    const codeQualityScore = (testCoverage + complexityScore + maintainabilityIndex) / 3

    return {
      totalLinesOfCode,
      codeFiles,
      testFiles,
      testCoverage,
      duplicatedCode,
      complexityScore,
      maintainabilityIndex,
      technicalDebt,
      codeQualityScore
    }
  }

  /**
   * Analisar organização do projeto
   */
  private async analyzeOrganization(): Promise<OrganizationAnalysis> {
    const issues: string[] = []
    
    // Verificar convenções
    const followsConventions = await this.checkNamingConventions()
    if (!followsConventions) {
      issues.push('Naming conventions not followed consistently')
    }
    
    // Verificar nomenclatura
    const hasProperNaming = await this.checkProperNaming()
    if (!hasProperNaming) {
      issues.push('Some files/directories have improper naming')
    }
    
    // Verificar documentação
    const hasDocumentation = await this.checkDocumentation()
    if (!hasDocumentation) {
      issues.push('Insufficient documentation')
    }
    
    // Verificar testes
    const hasTests = await this.checkTests()
    if (!hasTests) {
      issues.push('Insufficient test coverage')
    }
    
    // Verificar TypeScript
    const hasTypeScript = await this.checkTypeScript()
    if (!hasTypeScript) {
      issues.push('TypeScript not properly configured')
    }
    
    // Verificar linting
    const hasLinting = await this.checkLinting()
    if (!hasLinting) {
      issues.push('Linting not configured')
    }
    
    // Verificar formatação
    const hasFormatting = await this.checkFormatting()
    if (!hasFormatting) {
      issues.push('Code formatting not configured')
    }
    
    const organizationScore = this.calculateOrganizationScore({
      followsConventions,
      hasProperNaming,
      hasDocumentation,
      hasTests,
      hasTypeScript,
      hasLinting,
      hasFormatting
    })

    return {
      followsConventions,
      hasProperNaming,
      hasDocumentation,
      hasTests,
      hasTypeScript,
      hasLinting,
      hasFormatting,
      organizationScore,
      issues
    }
  }

  // Métodos auxiliares
  private getDirectoryPurpose(dirName: string): string {
    const purposes: Record<string, string> = {
      'components': 'React components',
      'hooks': 'Custom React hooks',
      'services': 'Business logic and API calls',
      'types': 'TypeScript type definitions',
      'utils': 'Utility functions',
      'config': 'Configuration files',
      '__tests__': 'Test files',
      'styles': 'CSS and styling',
      'assets': 'Static assets',
      'lib': 'Third-party libraries',
      'data': 'Data processing',
      'auth': 'Authentication logic',
      'monitoring': 'Monitoring and analytics',
      'audit': 'Audit and validation'
    }
    return purposes[dirName] || 'General purpose'
  }

  private calculateStructureScore(
    missingDirs: string[],
    unexpectedFiles: string[],
    totalFiles: number,
    totalDirs: number
  ): number {
    let score = 100
    
    // Penalizar diretórios ausentes
    score -= missingDirs.length * 10
    
    // Penalizar arquivos inesperados na raiz
    score -= unexpectedFiles.length * 5
    
    // Bonificar estrutura bem organizada
    if (totalDirs > 10 && totalFiles > 50) score += 10
    
    return Math.max(0, Math.min(100, score))
  }

  private calculateDependencyScore(
    hasPackageJson: boolean,
    hasLockFile: boolean,
    totalDeps: number,
    outdated: string[],
    vulnerabilities: string[]
  ): number {
    let score = 0
    
    if (hasPackageJson) score += 30
    if (hasLockFile) score += 20
    if (totalDeps > 0 && totalDeps < 100) score += 20
    if (outdated.length === 0) score += 15
    if (vulnerabilities.length === 0) score += 15
    
    return Math.min(100, score)
  }

  private calculateOrganizationScore(checks: {
    followsConventions: boolean
    hasProperNaming: boolean
    hasDocumentation: boolean
    hasTests: boolean
    hasTypeScript: boolean
    hasLinting: boolean
    hasFormatting: boolean
  }): number {
    const weights = {
      followsConventions: 20,
      hasProperNaming: 15,
      hasDocumentation: 15,
      hasTests: 20,
      hasTypeScript: 15,
      hasLinting: 10,
      hasFormatting: 5
    }
    
    let score = 0
    Object.entries(checks).forEach(([key, value]) => {
      if (value) {
        score += weights[key as keyof typeof weights]
      }
    })
    
    return score
  }

  private calculateOverallScore(
    structure: StructureAnalysis,
    dependencies: DependencyAnalysis,
    codeQuality: CodeQualityAnalysis,
    organization: OrganizationAnalysis
  ): number {
    const weights = {
      structure: 0.25,
      dependencies: 0.20,
      codeQuality: 0.35,
      organization: 0.20
    }
    
    return (
      structure.structureScore * weights.structure +
      dependencies.dependencyScore * weights.dependencies +
      codeQuality.codeQualityScore * weights.codeQuality +
      organization.organizationScore * weights.organization
    )
  }

  private determineStatus(score: number): 'excellent' | 'good' | 'needs_improvement' | 'critical' {
    if (score >= 90) return 'excellent'
    if (score >= 75) return 'good'
    if (score >= 50) return 'needs_improvement'
    return 'critical'
  }

  private generateRecommendations(
    structure: StructureAnalysis,
    dependencies: DependencyAnalysis,
    codeQuality: CodeQualityAnalysis,
    organization: OrganizationAnalysis
  ): string[] {
    const recommendations: string[] = []
    
    // Recomendações de estrutura
    if (structure.missingDirectories.length > 0) {
      recommendations.push(`Create missing directories: ${structure.missingDirectories.join(', ')}`)
    }
    
    if (structure.unexpectedFiles.length > 0) {
      recommendations.push(`Organize or remove unexpected root files: ${structure.unexpectedFiles.join(', ')}`)
    }
    
    // Recomendações de dependências
    if (!dependencies.packageJsonExists) {
      recommendations.push('Create package.json file')
    }
    
    if (!dependencies.lockFileExists) {
      recommendations.push('Generate package-lock.json or yarn.lock file')
    }
    
    // Recomendações de qualidade
    if (codeQuality.testCoverage < 70) {
      recommendations.push(`Increase test coverage from ${codeQuality.testCoverage.toFixed(1)}% to at least 70%`)
    }
    
    if (codeQuality.technicalDebt.length > 0) {
      recommendations.push(`Address technical debt: ${codeQuality.technicalDebt.join(', ')}`)
    }
    
    // Recomendações de organização
    organization.issues.forEach(issue => {
      recommendations.push(`Fix organization issue: ${issue}`)
    })
    
    return recommendations
  }

  // Verificações de organização (implementações simplificadas)
  private async checkNamingConventions(): Promise<boolean> {
    // Verificar se arquivos seguem convenções (camelCase, kebab-case, etc.)
    return true // Implementação simplificada
  }

  private async checkProperNaming(): Promise<boolean> {
    // Verificar nomenclatura adequada
    return true // Implementação simplificada
  }

  private async checkDocumentation(): Promise<boolean> {
    try {
      await stat(join(this.projectRoot, 'README.md'))
      return true
    } catch {
      return false
    }
  }

  private async checkTests(): Promise<boolean> {
    try {
      const testDir = join(this.projectRoot, 'src/__tests__')
      const stats = await stat(testDir)
      return stats.isDirectory()
    } catch {
      return false
    }
  }

  private async checkTypeScript(): Promise<boolean> {
    try {
      await stat(join(this.projectRoot, 'tsconfig.json'))
      return true
    } catch {
      return false
    }
  }

  private async checkLinting(): Promise<boolean> {
    try {
      await stat(join(this.projectRoot, '.eslintrc.js'))
      return true
    } catch {
      try {
        await stat(join(this.projectRoot, '.eslintrc.json'))
        return true
      } catch {
        return false
      }
    }
  }

  private async checkFormatting(): Promise<boolean> {
    try {
      await stat(join(this.projectRoot, '.prettierrc'))
      return true
    } catch {
      return false
    }
  }
}

export default ProjectStructureAnalyzer