// HMACAuth - Sistema de Autenticação HMAC para Exchanges

import CryptoJS from 'crypto-js'

export interface AuthConfig {
  apiKey: string
  secretKey: string
  passphrase?: string
  baseUrl: string
}

export interface SignedRequest {
  url: string
  method: string
  headers: Record<string, string>
  body?: string
}

export class HMACAuth {
  private config: AuthConfig

  constructor(config: AuthConfig) {
    this.config = config
  }

  /**
   * Gerar timestamp atual em segundos
   */
  private getTimestamp(): string {
    return Math.floor(Date.now() / 1000).toString()
  }

  /**
   * Gerar timestamp atual em milissegundos
   */
  private getTimestampMs(): string {
    return Date.now().toString()
  }

  /**
   * Autenticação HMAC SHA512 para Gate.io
   */
  signGateioRequest(method: string, path: string, queryString: string = '', body: string = ''): SignedRequest {
    const timestamp = this.getTimestamp()
    
    // Construir payload para assinatura
    const payload = `${method}\n${path}\n${queryString}\n${CryptoJS.SHA512(body).toString()}\n${timestamp}`
    
    // Gerar assinatura HMAC SHA512
    const signature = CryptoJS.HmacSHA512(payload, this.config.secretKey).toString()
    
    const url = `${this.config.baseUrl}${path}${queryString ? '?' + queryString : ''}`
    
    return {
      url,
      method,
      headers: {
        'KEY': this.config.apiKey,
        'Timestamp': timestamp,
        'SIGN': signature,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: body || undefined
    }
  }

  /**
   * Autenticação HMAC SHA256 para MEXC
   */
  signMexcRequest(method: string, path: string, params: Record<string, any> = {}): SignedRequest {
    const timestamp = this.getTimestampMs()
    
    // Adicionar timestamp aos parâmetros
    const allParams = {
      ...params,
      timestamp,
      recvWindow: '5000'
    }
    
    // Construir query string ordenada
    const queryString = Object.keys(allParams)
      .sort()
      .map(key => `${key}=${encodeURIComponent(allParams[key])}`)
      .join('&')
    
    // Gerar assinatura HMAC SHA256
    const signature = CryptoJS.HmacSHA256(queryString, this.config.secretKey).toString()
    
    const finalQueryString = `${queryString}&signature=${signature}`
    const url = `${this.config.baseUrl}${path}?${finalQueryString}`
    
    return {
      url,
      method,
      headers: {
        'X-MEXC-APIKEY': this.config.apiKey,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    }
  }

  /**
   * Autenticação HMAC SHA256 + Base64 para Bitget
   */
  signBitgetRequest(method: string, path: string, body: string = ''): SignedRequest {
    const timestamp = this.getTimestampMs()
    
    // Construir payload para assinatura
    const payload = `${timestamp}${method.toUpperCase()}${path}${body}`
    
    // Gerar assinatura HMAC SHA256 e converter para Base64
    const signature = CryptoJS.HmacSHA256(payload, this.config.secretKey).toString(CryptoJS.enc.Base64)
    
    const url = `${this.config.baseUrl}${path}`
    
    return {
      url,
      method,
      headers: {
        'ACCESS-KEY': this.config.apiKey,
        'ACCESS-SIGN': signature,
        'ACCESS-TIMESTAMP': timestamp,
        'ACCESS-PASSPHRASE': this.config.passphrase || '',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: body || undefined
    }
  }
}

/**
 * Factory para criar instâncias de autenticação para cada exchange
 */
export class AuthFactory {
  /**
   * Criar autenticador para Gate.io
   */
  static createGateioAuth(): HMACAuth {
    const config: AuthConfig = {
      apiKey: import.meta.env.VITE_GATEIO_API_KEY || '',
      secretKey: import.meta.env.VITE_GATEIO_SECRET_KEY || '',
      baseUrl: import.meta.env.VITE_GATEIO_API_URL || 'https://api.gateio.ws'
    }
    
    if (!config.apiKey || !config.secretKey) {
      throw new Error('Gate.io API credentials not configured')
    }
    
    return new HMACAuth(config)
  }

  /**
   * Criar autenticador para MEXC
   */
  static createMexcAuth(): HMACAuth {
    const config: AuthConfig = {
      apiKey: import.meta.env.VITE_MEXC_API_KEY || '',
      secretKey: import.meta.env.VITE_MEXC_SECRET_KEY || '',
      baseUrl: import.meta.env.VITE_MEXC_API_URL || 'https://api.mexc.com'
    }
    
    if (!config.apiKey || !config.secretKey) {
      throw new Error('MEXC API credentials not configured')
    }
    
    return new HMACAuth(config)
  }

  /**
   * Criar autenticador para Bitget
   */
  static createBitgetAuth(): HMACAuth {
    const config: AuthConfig = {
      apiKey: import.meta.env.VITE_BITGET_API_KEY || '',
      secretKey: import.meta.env.VITE_BITGET_SECRET_KEY || '',
      passphrase: import.meta.env.VITE_BITGET_PASSPHRASE || '',
      baseUrl: import.meta.env.VITE_BITGET_API_URL || 'https://api.bitget.com'
    }
    
    if (!config.apiKey || !config.secretKey || !config.passphrase) {
      throw new Error('Bitget API credentials not configured')
    }
    
    return new HMACAuth(config)
  }

  /**
   * Verificar se as credenciais estão configuradas
   */
  static checkCredentials(): {
    gateio: boolean
    mexc: boolean
    bitget: boolean
    allConfigured: boolean
  } {
    const gateio = !!(import.meta.env.VITE_GATEIO_API_KEY && import.meta.env.VITE_GATEIO_SECRET_KEY)
    const mexc = !!(import.meta.env.VITE_MEXC_API_KEY && import.meta.env.VITE_MEXC_SECRET_KEY)
    const bitget = !!(import.meta.env.VITE_BITGET_API_KEY && import.meta.env.VITE_BITGET_SECRET_KEY && import.meta.env.VITE_BITGET_PASSPHRASE)
    
    return {
      gateio,
      mexc,
      bitget,
      allConfigured: gateio && mexc && bitget
    }
  }
}

export default HMACAuth/**

 * Métodos estáticos para compatibilidade com testes
 */
export class HMACAuthStatic {
  /**
   * Criar autenticação Gate.io
   */
  static createGateioAuth(apiKey: string, secretKey: string) {
    const config: AuthConfig = {
      apiKey,
      secretKey,
      baseUrl: 'https://api.gateio.ws'
    }
    
    const auth = new HMACAuth(config)
    
    return {
      generateSignature: (method: string, path: string, queryString: string = '', body: string = '') => {
        const signed = auth.signGateioRequest(method, path, queryString, body)
        return signed.headers.SIGN
      },
      getHeaders: (method: string, path: string, queryString: string = '', body: string = '') => {
        const signed = auth.signGateioRequest(method, path, queryString, body)
        return signed.headers
      }
    }
  }

  /**
   * Criar autenticação MEXC
   */
  static createMexcAuth(apiKey: string, secretKey: string) {
    const config: AuthConfig = {
      apiKey,
      secretKey,
      baseUrl: 'https://api.mexc.com'
    }
    
    const auth = new HMACAuth(config)
    
    return {
      generateSignature: (queryString: string) => {
        const signature = CryptoJS.HmacSHA256(queryString, secretKey).toString()
        return signature
      },
      getHeaders: (queryString: string) => {
        const signed = auth.signMexcRequest('GET', '/api/v3/ticker/24hr', {})
        return signed.headers
      }
    }
  }

  /**
   * Criar autenticação Bitget
   */
  static createBitgetAuth(apiKey: string, secretKey: string, passphrase: string) {
    const config: AuthConfig = {
      apiKey,
      secretKey,
      passphrase,
      baseUrl: 'https://api.bitget.com'
    }
    
    const auth = new HMACAuth(config)
    
    return {
      generateSignature: (timestamp: string, method: string, path: string, body: string = '') => {
        const payload = `${timestamp}${method.toUpperCase()}${path}${body}`
        return CryptoJS.HmacSHA256(payload, secretKey).toString(CryptoJS.enc.Base64)
      },
      getHeaders: (timestamp: string, method: string, path: string, body: string = '') => {
        const signed = auth.signBitgetRequest(method, path, body)
        return signed.headers
      }
    }
  }

  /**
   * Validar credenciais
   */
  static validateCredentials(exchange: string, apiKey: string, secretKey: string, passphrase?: string): boolean {
    if (!apiKey || !secretKey) return false
    
    switch (exchange) {
      case 'gateio':
        return apiKey.length > 0 && secretKey.length > 0
      case 'mexc':
        return apiKey.length > 0 && secretKey.length > 0
      case 'bitget':
        return apiKey.length > 0 && secretKey.length > 0 && !!passphrase
      default:
        return false
    }
  }
}

// HMACAuthStatic já está exportada como classe acima