// DataNormalizer - Normalização de dados das exchanges

import type { ExchangeData } from '@/types/arbitrage'

export class DataNormalizer {
  // Normalizar dados do Gate.io
  static normalizeGateioData(rawData: any, type: 'spot' | 'futures'): ExchangeData {
    const baseData = {
      exchange: 'gateio' as const,
      symbol: this.normalizeSymbol(rawData.currency_pair || rawData.contract),
      type,
      price: parseFloat(rawData.last || rawData.mark_price),
      volume: parseFloat(rawData.quote_volume || rawData.base_volume || rawData.volume_24h),
      bid: parseFloat(rawData.highest_bid || rawData.bid1_price),
      ask: parseFloat(rawData.lowest_ask || rawData.ask1_price),
      timestamp: new Date(),
      responseTime: 150,
      dataQuality: 0.95,
      isStale: false
    }

    // Adicionar campos específicos para futuros
    if (type === 'futures') {
      return {
        ...baseData,
        contractType: rawData.contract_type || 'PERP',
        fundingRate: parseFloat(rawData.funding_rate || '0.0001')
      }
    }

    return baseData
  }

  // Normalizar dados do MEXC
  static normalizeMexcData(rawData: any, type: 'spot' | 'futures'): ExchangeData {
    const baseData = {
      exchange: 'mexc' as const,
      symbol: this.normalizeSymbol(rawData.symbol),
      type,
      price: parseFloat(rawData.price || rawData.lastPrice),
      volume: parseFloat(rawData.quoteVolume || rawData.volume || rawData.volume24h),
      bid: parseFloat(rawData.bidPrice),
      ask: parseFloat(rawData.askPrice),
      timestamp: new Date(),
      responseTime: 120,
      dataQuality: 0.92,
      isStale: false
    }

    // Adicionar campos específicos para futuros
    if (type === 'futures') {
      return {
        ...baseData,
        contractType: rawData.contractType || 'PERP',
        fundingRate: parseFloat(rawData.fundingRate || '0.0001')
      }
    }

    return baseData
  }

  // Normalizar dados do Bitget
  static normalizeBitgetData(rawData: any, type: 'spot' | 'futures'): ExchangeData {
    const baseData = {
      exchange: 'bitget' as const,
      symbol: this.normalizeSymbol(rawData.symbol || rawData.instId),
      type,
      price: parseFloat(rawData.close || rawData.lastPr || rawData.last),
      volume: parseFloat(rawData.quoteVol || rawData.baseVol || rawData.baseVolume || rawData.vol24h || rawData.volume),
      bid: parseFloat(rawData.bidPr || rawData.bid),
      ask: parseFloat(rawData.askPr || rawData.ask),
      timestamp: new Date(),
      responseTime: 180,
      dataQuality: 0.90,
      isStale: false
    }

    // Adicionar campos específicos para futuros
    if (type === 'futures') {
      return {
        ...baseData,
        contractType: rawData.contractType || 'PERP',
        fundingRate: parseFloat(rawData.fundingRate || '0.0001')
      }
    }

    return baseData
  }

  // Normalizar formato do símbolo
  static normalizeSymbol(symbol: string): string {
    if (!symbol) return ''
    
    // Remover sufixos específicos
    let normalized = symbol.replace(/_UMCBL$/, '').replace(/_DMCBL$/, '')
    
    // Converter diferentes formatos para BTC/USDT
    if (normalized.includes('_')) {
      normalized = normalized.replace('_', '/')
    } else if (normalized.includes('-')) {
      normalized = normalized.replace('-', '/')
    } else if (normalized.length > 3 && !normalized.includes('/')) {
      // Tentar detectar BTCUSDT -> BTC/USDT
      const commonQuotes = ['USDT', 'USDC', 'BTC', 'ETH', 'BNB']
      for (const quote of commonQuotes) {
        if (normalized.endsWith(quote)) {
          const base = normalized.slice(0, -quote.length)
          if (base.length >= 2) {
            normalized = `${base}/${quote}`
            break
          }
        }
      }
    }
    
    return normalized.toUpperCase()
  }

  // Validar dados normalizados
  static validateNormalizedData(data: ExchangeData): boolean {
    // Verificar campos obrigatórios
    if (!data.exchange || !data.symbol || !data.type) return false
    if (typeof data.price !== 'number' || data.price <= 0 || isNaN(data.price)) return false
    if (typeof data.volume !== 'number' || data.volume < 0) return false
    
    // Verificar bid/ask válidos (devem ser > 0 e não NaN)
    if (typeof data.bid !== 'number' || data.bid <= 0 || isNaN(data.bid)) return false
    if (typeof data.ask !== 'number' || data.ask <= 0 || isNaN(data.ask)) return false
    
    // Verificar spread bid/ask
    if (data.bid >= data.ask) return false
    
    // Verificar se o preço está próximo do spread (tolerância de 10%)
    const midPrice = (data.bid + data.ask) / 2
    const tolerance = midPrice * 0.1
    if (Math.abs(data.price - midPrice) > tolerance) return false
    
    return true
  }

  // Calcular qualidade dos dados
  static calculateDataQuality(data: ExchangeData): number {
    let quality = 1.0
    
    // Penalizar dados antigos
    const age = Date.now() - data.timestamp.getTime()
    if (age > 30000) quality -= 0.3 // Mais de 30s
    else if (age > 15000) quality -= 0.1 // Mais de 15s
    
    // Penalizar resposta lenta
    if (data.responseTime > 1000) quality -= 0.2
    else if (data.responseTime > 500) quality -= 0.1
    
    // Penalizar spread muito largo
    if (data.bid && data.ask) {
      const spread = (data.ask - data.bid) / data.price
      if (spread > 0.01) quality -= 0.2 // Spread > 1%
      else if (spread > 0.005) quality -= 0.1 // Spread > 0.5%
    }
    
    return Math.max(0, quality)
  }
}