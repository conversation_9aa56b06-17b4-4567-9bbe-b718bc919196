// DataQualityMonitor - Monitoramento da Qualidade dos Dados das Exchanges

import type { ExchangeData } from '@/types/arbitrage'

export interface DataQualityMetrics {
  exchange: string
  timestamp: number
  totalRecords: number
  validRecords: number
  invalidRecords: number
  validationRate: number
  avgPrice: number
  totalVolume: number
  priceRange: {
    min: number
    max: number
  }
  issues: DataQualityIssue[]
}

export interface DataQualityIssue {
  type: 'missing_data' | 'invalid_price' | 'invalid_volume' | 'stale_data' | 'duplicate_symbol'
  severity: 'low' | 'medium' | 'high'
  count: number
  description: string
  examples?: string[]
}

export interface DataFreshnessCheck {
  exchange: string
  lastUpdate: number
  isStale: boolean
  staleDuration: number
  expectedInterval: number
}

export class DataQualityMonitor {
  private static instance: DataQualityMonitor
  private qualityHistory = new Map<string, DataQualityMetrics[]>()
  private freshnessChecks = new Map<string, DataFreshnessCheck>()
  
  public static getInstance(): DataQualityMonitor {
    if (!DataQualityMonitor.instance) {
      DataQualityMonitor.instance = new DataQualityMonitor()
    }
    return DataQualityMonitor.instance
  }

  /**
   * Analisar qualidade dos dados de uma exchange
   */
  analyzeDataQuality(exchange: string, data: ExchangeData[]): DataQualityMetrics {
    const timestamp = Date.now()
    const issues: DataQualityIssue[] = []
    
    // Contadores básicos
    const totalRecords = data.length
    let validRecords = 0
    let invalidRecords = 0
    
    // Métricas de preço e volume
    let totalPrice = 0
    let totalVolume = 0
    let minPrice = Infinity
    let maxPrice = -Infinity
    
    // Verificações de qualidade
    const symbolCounts = new Map<string, number>()
    const missingDataCount = { price: 0, volume: 0, symbol: 0 }
    const invalidPriceCount = { negative: 0, zero: 0, extreme: 0 }
    const invalidVolumeCount = { negative: 0 }
    const staleDataCount = { count: 0, threshold: 60 * 60 * 1000 } // 1 hora
    
    data.forEach(item => {
      let isValid = true
      
      // Verificar dados obrigatórios
      if (!item.symbol) {
        missingDataCount.symbol++
        isValid = false
      }
      if (!item.price && item.price !== 0) {
        missingDataCount.price++
        isValid = false
      }
      if (!item.volume && item.volume !== 0) {
        missingDataCount.volume++
        isValid = false
      }
      
      // Verificar preços válidos
      if (item.price <= 0) {
        if (item.price === 0) {
          invalidPriceCount.zero++
        } else {
          invalidPriceCount.negative++
        }
        isValid = false
      } else if (item.price > 1000000) { // Preço extremamente alto
        invalidPriceCount.extreme++
        isValid = false
      }
      
      // Verificar volumes válidos
      if (item.volume < 0) {
        invalidVolumeCount.negative++
        isValid = false
      }
      
      // Verificar dados obsoletos
      const dataAge = timestamp - item.timestamp
      if (dataAge > staleDataCount.threshold) {
        staleDataCount.count++
        isValid = false
      }
      
      // Verificar símbolos duplicados
      if (item.symbol) {
        const count = symbolCounts.get(item.symbol) || 0
        symbolCounts.set(item.symbol, count + 1)
      }
      
      // Calcular métricas se válido
      if (isValid) {
        validRecords++
        totalPrice += item.price
        totalVolume += item.volume
        minPrice = Math.min(minPrice, item.price)
        maxPrice = Math.max(maxPrice, item.price)
      } else {
        invalidRecords++
      }
    })
    
    // Detectar símbolos duplicados
    const duplicateSymbols = Array.from(symbolCounts.entries())
      .filter(([_, count]) => count > 1)
      .map(([symbol, count]) => `${symbol} (${count}x)`)
    
    // Criar issues baseados nas verificações
    if (missingDataCount.symbol > 0) {
      issues.push({
        type: 'missing_data',
        severity: 'high',
        count: missingDataCount.symbol,
        description: `${missingDataCount.symbol} records missing symbol`
      })
    }
    
    if (missingDataCount.price > 0) {
      issues.push({
        type: 'missing_data',
        severity: 'high',
        count: missingDataCount.price,
        description: `${missingDataCount.price} records missing price`
      })
    }
    
    if (invalidPriceCount.negative + invalidPriceCount.zero + invalidPriceCount.extreme > 0) {
      const total = invalidPriceCount.negative + invalidPriceCount.zero + invalidPriceCount.extreme
      issues.push({
        type: 'invalid_price',
        severity: 'high',
        count: total,
        description: `${total} records with invalid prices (${invalidPriceCount.negative} negative, ${invalidPriceCount.zero} zero, ${invalidPriceCount.extreme} extreme)`
      })
    }
    
    if (invalidVolumeCount.negative > 0) {
      issues.push({
        type: 'invalid_volume',
        severity: 'medium',
        count: invalidVolumeCount.negative,
        description: `${invalidVolumeCount.negative} records with negative volume`
      })
    }
    
    if (staleDataCount.count > 0) {
      issues.push({
        type: 'stale_data',
        severity: 'medium',
        count: staleDataCount.count,
        description: `${staleDataCount.count} records older than 1 hour`
      })
    }
    
    if (duplicateSymbols.length > 0) {
      issues.push({
        type: 'duplicate_symbol',
        severity: 'low',
        count: duplicateSymbols.length,
        description: `${duplicateSymbols.length} duplicate symbols found`,
        examples: duplicateSymbols.slice(0, 5)
      })
    }
    
    const metrics: DataQualityMetrics = {
      exchange,
      timestamp,
      totalRecords,
      validRecords,
      invalidRecords,
      validationRate: totalRecords > 0 ? (validRecords / totalRecords) * 100 : 0,
      avgPrice: validRecords > 0 ? totalPrice / validRecords : 0,
      totalVolume,
      priceRange: {
        min: minPrice === Infinity ? 0 : minPrice,
        max: maxPrice === -Infinity ? 0 : maxPrice
      },
      issues
    }
    
    // Armazenar no histórico
    this.storeQualityMetrics(exchange, metrics)
    
    return metrics
  }

  /**
   * Verificar frescor dos dados
   */
  checkDataFreshness(exchange: string, expectedInterval: number = 30000): DataFreshnessCheck {
    const now = Date.now()
    const existing = this.freshnessChecks.get(exchange)
    
    if (!existing) {
      // Primeira verificação
      const check: DataFreshnessCheck = {
        exchange,
        lastUpdate: now,
        isStale: false,
        staleDuration: 0,
        expectedInterval
      }
      this.freshnessChecks.set(exchange, check)
      return check
    }
    
    const staleDuration = now - existing.lastUpdate
    const isStale = staleDuration > expectedInterval * 2 // 2x o intervalo esperado
    
    const check: DataFreshnessCheck = {
      exchange,
      lastUpdate: existing.lastUpdate,
      isStale,
      staleDuration,
      expectedInterval
    }
    
    return check
  }

  /**
   * Atualizar timestamp da última atualização
   */
  updateLastUpdate(exchange: string): void {
    const existing = this.freshnessChecks.get(exchange)
    if (existing) {
      existing.lastUpdate = Date.now()
      existing.staleDuration = 0
      existing.isStale = false
    } else {
      this.freshnessChecks.set(exchange, {
        exchange,
        lastUpdate: Date.now(),
        isStale: false,
        staleDuration: 0,
        expectedInterval: 30000
      })
    }
  }

  /**
   * Obter métricas de qualidade de uma exchange
   */
  getQualityMetrics(exchange: string, limit: number = 10): DataQualityMetrics[] {
    const history = this.qualityHistory.get(exchange) || []
    return history.slice(-limit)
  }

  /**
   * Obter resumo de qualidade de todas as exchanges
   */
  getQualitySummary(): {
    exchanges: string[]
    totalRecords: number
    avgValidationRate: number
    totalIssues: number
    staleExchanges: string[]
  } {
    const exchanges = Array.from(this.qualityHistory.keys())
    let totalRecords = 0
    let totalValidationRate = 0
    let totalIssues = 0
    const staleExchanges: string[] = []
    
    exchanges.forEach(exchange => {
      const latest = this.getLatestMetrics(exchange)
      if (latest) {
        totalRecords += latest.totalRecords
        totalValidationRate += latest.validationRate
        totalIssues += latest.issues.length
      }
      
      const freshness = this.checkDataFreshness(exchange)
      if (freshness.isStale) {
        staleExchanges.push(exchange)
      }
    })
    
    return {
      exchanges,
      totalRecords,
      avgValidationRate: exchanges.length > 0 ? totalValidationRate / exchanges.length : 0,
      totalIssues,
      staleExchanges
    }
  }

  /**
   * Obter métricas mais recentes de uma exchange
   */
  getLatestMetrics(exchange: string): DataQualityMetrics | null {
    const history = this.qualityHistory.get(exchange) || []
    return history.length > 0 ? history[history.length - 1] : null
  }

  /**
   * Armazenar métricas no histórico
   */
  private storeQualityMetrics(exchange: string, metrics: DataQualityMetrics): void {
    const history = this.qualityHistory.get(exchange) || []
    history.push(metrics)
    
    // Manter apenas os últimos 100 registros
    if (history.length > 100) {
      history.splice(0, history.length - 100)
    }
    
    this.qualityHistory.set(exchange, history)
  }

  /**
   * Limpar histórico antigo
   */
  cleanupOldData(maxAge: number = 24 * 60 * 60 * 1000): void {
    const cutoff = Date.now() - maxAge
    
    this.qualityHistory.forEach((history, exchange) => {
      const filtered = history.filter(metrics => metrics.timestamp > cutoff)
      this.qualityHistory.set(exchange, filtered)
    })
  }

  /**
   * Obter alertas de qualidade
   */
  getQualityAlerts(): {
    exchange: string
    severity: 'low' | 'medium' | 'high'
    message: string
    timestamp: number
  }[] {
    const alerts: {
      exchange: string
      severity: 'low' | 'medium' | 'high'
      message: string
      timestamp: number
    }[] = []
    
    // Verificar qualidade dos dados
    this.qualityHistory.forEach((history, exchange) => {
      const latest = history[history.length - 1]
      if (!latest) return
      
      // Alerta para baixa taxa de validação
      if (latest.validationRate < 90) {
        alerts.push({
          exchange,
          severity: latest.validationRate < 70 ? 'high' : 'medium',
          message: `Low data validation rate: ${latest.validationRate.toFixed(1)}%`,
          timestamp: latest.timestamp
        })
      }
      
      // Alertas para issues críticos
      latest.issues.forEach(issue => {
        if (issue.severity === 'high') {
          alerts.push({
            exchange,
            severity: 'high',
            message: issue.description,
            timestamp: latest.timestamp
          })
        }
      })
    })
    
    // Verificar frescor dos dados
    this.freshnessChecks.forEach((check, exchange) => {
      if (check.isStale) {
        alerts.push({
          exchange,
          severity: 'high',
          message: `Stale data: ${Math.round(check.staleDuration / 1000)}s since last update`,
          timestamp: Date.now()
        })
      }
    })
    
    return alerts.sort((a, b) => b.timestamp - a.timestamp)
  }
}

export default DataQualityMonitor