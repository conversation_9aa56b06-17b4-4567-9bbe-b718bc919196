// APIMonitor - Sistema de Monitoramento de APIs das Exchanges

export interface APIMetrics {
  exchange: string
  endpoint: string
  method: string
  timestamp: number
  responseTime: number
  statusCode: number
  success: boolean
  error?: string
  dataSize?: number
  retryCount?: number
}

export interface APIHealthStatus {
  exchange: string
  isHealthy: boolean
  lastCheck: number
  responseTime: number
  successRate: number
  errorRate: number
  totalRequests: number
  failedRequests: number
  avgResponseTime: number
  uptime: number
  issues: APIIssue[]
}

export interface APIIssue {
  type: 'high_latency' | 'high_error_rate' | 'timeout' | 'rate_limit' | 'auth_failure'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  timestamp: number
  count: number
}

export interface APIAlert {
  id: string
  exchange: string
  type: 'performance' | 'availability' | 'error_rate' | 'timeout'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  timestamp: number
  resolved: boolean
  resolvedAt?: number
}

export class APIMonitor {
  private static instance: APIMonitor
  private metrics = new Map<string, APIMetrics[]>()
  private healthStatus = new Map<string, APIHealthStatus>()
  private alerts = new Map<string, APIAlert>()
  private thresholds = {
    responseTime: {
      warning: 2000,    // 2 segundos
      critical: 5000    // 5 segundos
    },
    errorRate: {
      warning: 0.05,    // 5%
      critical: 0.15    // 15%
    },
    successRate: {
      warning: 0.95,    // 95%
      critical: 0.85    // 85%
    }
  }

  public static getInstance(): APIMonitor {
    if (!APIMonitor.instance) {
      APIMonitor.instance = new APIMonitor()
    }
    return APIMonitor.instance
  }

  /**
   * Registrar métrica de API
   */
  recordAPICall(
    exchange: string,
    endpoint: string,
    method: string,
    responseTime: number,
    statusCode: number,
    error?: string,
    dataSize?: number,
    retryCount?: number
  ): void {
    const metric: APIMetrics = {
      exchange,
      endpoint,
      method,
      timestamp: Date.now(),
      responseTime,
      statusCode,
      success: statusCode >= 200 && statusCode < 300,
      error,
      dataSize,
      retryCount
    }

    // Armazenar métrica
    const key = `${exchange}_${endpoint}`
    const exchangeMetrics = this.metrics.get(key) || []
    exchangeMetrics.push(metric)

    // Manter apenas as últimas 1000 métricas por endpoint
    if (exchangeMetrics.length > 1000) {
      exchangeMetrics.splice(0, exchangeMetrics.length - 1000)
    }

    this.metrics.set(key, exchangeMetrics)

    // Atualizar status de saúde
    this.updateHealthStatus(exchange)

    // Verificar alertas
    this.checkAlerts(exchange, metric)
  }

  /**
   * Atualizar status de saúde da exchange
   */
  private updateHealthStatus(exchange: string): void {
    const now = Date.now()
    const timeWindow = 5 * 60 * 1000 // 5 minutos
    const cutoff = now - timeWindow

    // Coletar todas as métricas da exchange nos últimos 5 minutos
    const allMetrics: APIMetrics[] = []
    this.metrics.forEach((metrics, key) => {
      if (key.startsWith(exchange)) {
        const recentMetrics = metrics.filter(m => m.timestamp > cutoff)
        allMetrics.push(...recentMetrics)
      }
    })

    if (allMetrics.length === 0) {
      return
    }

    // Calcular estatísticas
    const totalRequests = allMetrics.length
    const successfulRequests = allMetrics.filter(m => m.success).length
    const failedRequests = totalRequests - successfulRequests
    const successRate = successfulRequests / totalRequests
    const errorRate = failedRequests / totalRequests
    const avgResponseTime = allMetrics.reduce((sum, m) => sum + m.responseTime, 0) / totalRequests
    const lastResponseTime = allMetrics[allMetrics.length - 1]?.responseTime || 0

    // Detectar issues
    const issues: APIIssue[] = []

    // Issue de alta latência
    if (avgResponseTime > this.thresholds.responseTime.critical) {
      issues.push({
        type: 'high_latency',
        severity: 'critical',
        message: `Average response time is ${avgResponseTime.toFixed(0)}ms (threshold: ${this.thresholds.responseTime.critical}ms)`,
        timestamp: now,
        count: allMetrics.filter(m => m.responseTime > this.thresholds.responseTime.critical).length
      })
    } else if (avgResponseTime > this.thresholds.responseTime.warning) {
      issues.push({
        type: 'high_latency',
        severity: 'medium',
        message: `Average response time is ${avgResponseTime.toFixed(0)}ms (threshold: ${this.thresholds.responseTime.warning}ms)`,
        timestamp: now,
        count: allMetrics.filter(m => m.responseTime > this.thresholds.responseTime.warning).length
      })
    }

    // Issue de alta taxa de erro
    if (errorRate > this.thresholds.errorRate.critical) {
      issues.push({
        type: 'high_error_rate',
        severity: 'critical',
        message: `Error rate is ${(errorRate * 100).toFixed(1)}% (threshold: ${this.thresholds.errorRate.critical * 100}%)`,
        timestamp: now,
        count: failedRequests
      })
    } else if (errorRate > this.thresholds.errorRate.warning) {
      issues.push({
        type: 'high_error_rate',
        severity: 'medium',
        message: `Error rate is ${(errorRate * 100).toFixed(1)}% (threshold: ${this.thresholds.errorRate.warning * 100}%)`,
        timestamp: now,
        count: failedRequests
      })
    }

    // Issue de timeouts
    const timeouts = allMetrics.filter(m => m.error?.includes('timeout') || m.responseTime > 10000)
    if (timeouts.length > 0) {
      issues.push({
        type: 'timeout',
        severity: timeouts.length > totalRequests * 0.1 ? 'high' : 'medium',
        message: `${timeouts.length} timeout(s) detected in the last 5 minutes`,
        timestamp: now,
        count: timeouts.length
      })
    }

    // Issue de rate limiting
    const rateLimits = allMetrics.filter(m => m.statusCode === 429)
    if (rateLimits.length > 0) {
      issues.push({
        type: 'rate_limit',
        severity: 'medium',
        message: `${rateLimits.length} rate limit error(s) detected`,
        timestamp: now,
        count: rateLimits.length
      })
    }

    // Issue de falha de autenticação
    const authFailures = allMetrics.filter(m => m.statusCode === 401 || m.statusCode === 403)
    if (authFailures.length > 0) {
      issues.push({
        type: 'auth_failure',
        severity: 'high',
        message: `${authFailures.length} authentication failure(s) detected`,
        timestamp: now,
        count: authFailures.length
      })
    }

    // Calcular uptime (baseado nos últimos 30 minutos)
    const uptimeWindow = 30 * 60 * 1000
    const uptimeCutoff = now - uptimeWindow
    const uptimeMetrics = allMetrics.filter(m => m.timestamp > uptimeCutoff)
    const uptimeSuccessful = uptimeMetrics.filter(m => m.success).length
    const uptime = uptimeMetrics.length > 0 ? (uptimeSuccessful / uptimeMetrics.length) * 100 : 100

    // Determinar se está saudável
    const isHealthy = successRate >= this.thresholds.successRate.warning && 
                     avgResponseTime <= this.thresholds.responseTime.warning &&
                     issues.filter(i => i.severity === 'critical').length === 0

    const healthStatus: APIHealthStatus = {
      exchange,
      isHealthy,
      lastCheck: now,
      responseTime: lastResponseTime,
      successRate,
      errorRate,
      totalRequests,
      failedRequests,
      avgResponseTime,
      uptime,
      issues
    }

    this.healthStatus.set(exchange, healthStatus)
  }

  /**
   * Verificar e gerar alertas
   */
  private checkAlerts(exchange: string, metric: APIMetrics): void {
    const alerts: APIAlert[] = []

    // Alerta de alta latência
    if (metric.responseTime > this.thresholds.responseTime.critical) {
      alerts.push({
        id: `${exchange}_high_latency_${Date.now()}`,
        exchange,
        type: 'performance',
        severity: 'critical',
        message: `Response time ${metric.responseTime}ms exceeds critical threshold (${this.thresholds.responseTime.critical}ms)`,
        timestamp: metric.timestamp,
        resolved: false
      })
    }

    // Alerta de falha de API
    if (!metric.success) {
      const severity = metric.statusCode >= 500 ? 'high' : 'medium'
      alerts.push({
        id: `${exchange}_api_failure_${Date.now()}`,
        exchange,
        type: 'availability',
        severity,
        message: `API call failed with status ${metric.statusCode}: ${metric.error || 'Unknown error'}`,
        timestamp: metric.timestamp,
        resolved: false
      })
    }

    // Armazenar alertas
    alerts.forEach(alert => {
      this.alerts.set(alert.id, alert)
    })

    // Limpar alertas antigos (mais de 1 hora)
    const oneHourAgo = Date.now() - (60 * 60 * 1000)
    this.alerts.forEach((alert, id) => {
      if (alert.timestamp < oneHourAgo) {
        this.alerts.delete(id)
      }
    })
  }

  /**
   * Obter métricas de uma exchange
   */
  getExchangeMetrics(exchange: string, timeWindow: number = 5 * 60 * 1000): APIMetrics[] {
    const cutoff = Date.now() - timeWindow
    const allMetrics: APIMetrics[] = []

    this.metrics.forEach((metrics, key) => {
      if (key.startsWith(exchange)) {
        const recentMetrics = metrics.filter(m => m.timestamp > cutoff)
        allMetrics.push(...recentMetrics)
      }
    })

    return allMetrics.sort((a, b) => b.timestamp - a.timestamp)
  }

  /**
   * Obter status de saúde de uma exchange
   */
  getHealthStatus(exchange: string): APIHealthStatus | null {
    return this.healthStatus.get(exchange) || null
  }

  /**
   * Obter status de saúde de todas as exchanges
   */
  getAllHealthStatus(): APIHealthStatus[] {
    return Array.from(this.healthStatus.values())
  }

  /**
   * Obter alertas ativos
   */
  getActiveAlerts(exchange?: string): APIAlert[] {
    const alerts = Array.from(this.alerts.values())
      .filter(alert => !alert.resolved)
      .sort((a, b) => b.timestamp - a.timestamp)

    return exchange ? alerts.filter(alert => alert.exchange === exchange) : alerts
  }

  /**
   * Resolver um alerta
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.get(alertId)
    if (alert && !alert.resolved) {
      alert.resolved = true
      alert.resolvedAt = Date.now()
      return true
    }
    return false
  }

  /**
   * Obter estatísticas gerais
   */
  getOverallStats(timeWindow: number = 5 * 60 * 1000): {
    totalRequests: number
    successRate: number
    avgResponseTime: number
    activeAlerts: number
    healthyExchanges: number
    totalExchanges: number
  } {
    const cutoff = Date.now() - timeWindow
    let totalRequests = 0
    let successfulRequests = 0
    let totalResponseTime = 0

    this.metrics.forEach(metrics => {
      const recentMetrics = metrics.filter(m => m.timestamp > cutoff)
      totalRequests += recentMetrics.length
      successfulRequests += recentMetrics.filter(m => m.success).length
      totalResponseTime += recentMetrics.reduce((sum, m) => sum + m.responseTime, 0)
    })

    const healthStatuses = this.getAllHealthStatus()
    const healthyExchanges = healthStatuses.filter(h => h.isHealthy).length
    const activeAlerts = this.getActiveAlerts().length

    return {
      totalRequests,
      successRate: totalRequests > 0 ? successfulRequests / totalRequests : 1,
      avgResponseTime: totalRequests > 0 ? totalResponseTime / totalRequests : 0,
      activeAlerts,
      healthyExchanges,
      totalExchanges: healthStatuses.length
    }
  }

  /**
   * Obter métricas de performance por endpoint
   */
  getEndpointMetrics(exchange: string, endpoint: string, timeWindow: number = 5 * 60 * 1000): {
    totalRequests: number
    successRate: number
    avgResponseTime: number
    minResponseTime: number
    maxResponseTime: number
    p95ResponseTime: number
    errorCount: number
    lastError?: string
  } {
    const key = `${exchange}_${endpoint}`
    const metrics = this.metrics.get(key) || []
    const cutoff = Date.now() - timeWindow
    const recentMetrics = metrics.filter(m => m.timestamp > cutoff)

    if (recentMetrics.length === 0) {
      return {
        totalRequests: 0,
        successRate: 1,
        avgResponseTime: 0,
        minResponseTime: 0,
        maxResponseTime: 0,
        p95ResponseTime: 0,
        errorCount: 0
      }
    }

    const successfulRequests = recentMetrics.filter(m => m.success).length
    const responseTimes = recentMetrics.map(m => m.responseTime).sort((a, b) => a - b)
    const p95Index = Math.floor(responseTimes.length * 0.95)
    const errors = recentMetrics.filter(m => !m.success)

    return {
      totalRequests: recentMetrics.length,
      successRate: successfulRequests / recentMetrics.length,
      avgResponseTime: recentMetrics.reduce((sum, m) => sum + m.responseTime, 0) / recentMetrics.length,
      minResponseTime: Math.min(...responseTimes),
      maxResponseTime: Math.max(...responseTimes),
      p95ResponseTime: responseTimes[p95Index] || 0,
      errorCount: errors.length,
      lastError: errors[errors.length - 1]?.error
    }
  }

  /**
   * Limpar dados antigos
   */
  cleanup(maxAge: number = 24 * 60 * 60 * 1000): void {
    const cutoff = Date.now() - maxAge

    // Limpar métricas antigas
    this.metrics.forEach((metrics, key) => {
      const filtered = metrics.filter(m => m.timestamp > cutoff)
      this.metrics.set(key, filtered)
    })

    // Limpar alertas antigos
    this.alerts.forEach((alert, id) => {
      if (alert.timestamp < cutoff) {
        this.alerts.delete(id)
      }
    })
  }

  /**
   * Configurar thresholds personalizados
   */
  setThresholds(thresholds: Partial<typeof this.thresholds>): void {
    this.thresholds = { ...this.thresholds, ...thresholds }
  }

  /**
   * Obter configuração atual dos thresholds
   */
  getThresholds() {
    return { ...this.thresholds }
  }
}

export default APIMonitor