// ProductionMonitor - Sistema de Monitoramento de Produção

export interface ProductionMetrics {
  timestamp: number
  system: SystemMetrics
  application: ApplicationMetrics
  business: BusinessMetrics
  alerts: Alert[]
  status: 'healthy' | 'warning' | 'critical' | 'down'
}

export interface SystemMetrics {
  cpu: {
    usage: number
    cores: number
    loadAverage: number[]
  }
  memory: {
    used: number
    total: number
    percentage: number
    available: number
  }
  disk: {
    used: number
    total: number
    percentage: number
    iops: number
  }
  network: {
    bytesIn: number
    bytesOut: number
    packetsIn: number
    packetsOut: number
    latency: number
  }
}

export interface ApplicationMetrics {
  uptime: number
  responseTime: {
    avg: number
    p50: number
    p90: number
    p95: number
    p99: number
  }
  throughput: {
    requestsPerSecond: number
    transactionsPerSecond: number
  }
  errors: {
    rate: number
    count: number
    types: { [key: string]: number }
  }
  database: {
    connections: number
    queryTime: number
    slowQueries: number
  }
  cache: {
    hitRate: number
    missRate: number
    evictions: number
  }
}

export interface BusinessMetrics {
  arbitrageOpportunities: {
    total: number
    profitable: number
    averageSpread: number
    topExchanges: string[]
  }
  trading: {
    volume: number
    profit: number
    successRate: number
    activePositions: number
  }
  exchanges: {
    connected: number
    total: number
    responseTime: { [exchange: string]: number }
    errorRate: { [exchange: string]: number }
  }
  users: {
    active: number
    concurrent: number
    sessions: number
  }
}

export interface Alert {
  id: string
  timestamp: number
  severity: 'low' | 'medium' | 'high' | 'critical'
  category: 'system' | 'application' | 'business' | 'security'
  title: string
  description: string
  metric: string
  threshold: number
  currentValue: number
  status: 'active' | 'resolved' | 'acknowledged'
  actions: string[]
}

export interface MonitoringConfig {
  intervals: {
    metrics: number
    alerts: number
    healthCheck: number
  }
  thresholds: {
    cpu: number
    memory: number
    disk: number
    responseTime: number
    errorRate: number
    availability: number
  }
  alertChannels: {
    email: boolean
    slack: boolean
    sms: boolean
    webhook: string[]
  }
}

export class ProductionMonitor {
  private static instance: ProductionMonitor
  private metrics: ProductionMetrics[] = []
  private alerts: Alert[] = []
  private isMonitoring = false
  private config: MonitoringConfig

  private constructor() {
    this.config = {
      intervals: {
        metrics: 30000, // 30 segundos
        alerts: 10000,  // 10 segundos
        healthCheck: 60000 // 1 minuto
      },
      thresholds: {
        cpu: 80,
        memory: 85,
        disk: 90,
        responseTime: 3000,
        errorRate: 5,
        availability: 99
      },
      alertChannels: {
        email: true,
        slack: true,
        sms: false,
        webhook: []
      }
    }
  }

  static getInstance(): ProductionMonitor {
    if (!ProductionMonitor.instance) {
      ProductionMonitor.instance = new ProductionMonitor()
    }
    return ProductionMonitor.instance
  }

  /**
   * Iniciar monitoramento
   */
  startMonitoring(): void {
    if (this.isMonitoring) {
      console.log('⚠️ Monitoring is already running')
      return
    }

    console.log('🔍 Starting production monitoring...')
    this.isMonitoring = true

    // Iniciar coleta de métricas
    this.startMetricsCollection()
    
    // Iniciar verificação de alertas
    this.startAlertMonitoring()
    
    // Iniciar health checks
    this.startHealthChecks()
  }

  /**
   * Parar monitoramento
   */
  stopMonitoring(): void {
    console.log('🛑 Stopping production monitoring...')
    this.isMonitoring = false
  }

  /**
   * Iniciar coleta de métricas
   */
  private startMetricsCollection(): void {
    const collectMetrics = async () => {
      if (!this.isMonitoring) return

      try {
        const metrics = await this.collectCurrentMetrics()
        this.metrics.push(metrics)
        
        // Manter apenas últimas 1000 métricas (aproximadamente 8 horas)
        if (this.metrics.length > 1000) {
          this.metrics.shift()
        }
        
        // Verificar alertas baseados nas métricas
        await this.checkMetricAlerts(metrics)
        
      } catch (error) {
        console.error('❌ Error collecting metrics:', error)
      }

      // Agendar próxima coleta
      setTimeout(collectMetrics, this.config.intervals.metrics)
    }

    collectMetrics()
  }

  /**
   * Coletar métricas atuais
   */
  private async collectCurrentMetrics(): Promise<ProductionMetrics> {
    const timestamp = Date.now()
    
    // Simular coleta de métricas do sistema
    const systemMetrics: SystemMetrics = {
      cpu: {
        usage: Math.random() * 40 + 30, // 30-70%
        cores: 4,
        loadAverage: [
          Math.random() * 2 + 1,
          Math.random() * 2 + 1,
          Math.random() * 2 + 1
        ]
      },
      memory: {
        used: Math.random() * 2048 + 1024, // 1-3GB
        total: 4096,
        percentage: 0,
        available: 0
      },
      disk: {
        used: Math.random() * 50 + 20, // 20-70GB
        total: 100,
        percentage: 0,
        iops: Math.random() * 1000 + 500
      },
      network: {
        bytesIn: Math.random() * 1000000 + 500000,
        bytesOut: Math.random() * 800000 + 400000,
        packetsIn: Math.random() * 10000 + 5000,
        packetsOut: Math.random() * 8000 + 4000,
        latency: Math.random() * 50 + 10
      }
    }

    // Calcular percentuais
    systemMetrics.memory.percentage = (systemMetrics.memory.used / systemMetrics.memory.total) * 100
    systemMetrics.memory.available = systemMetrics.memory.total - systemMetrics.memory.used
    systemMetrics.disk.percentage = (systemMetrics.disk.used / systemMetrics.disk.total) * 100

    // Simular métricas da aplicação
    const applicationMetrics: ApplicationMetrics = {
      uptime: Date.now() - (Date.now() - Math.random() * 86400000), // Até 24h
      responseTime: {
        avg: Math.random() * 1000 + 500,
        p50: Math.random() * 800 + 400,
        p90: Math.random() * 1500 + 800,
        p95: Math.random() * 2000 + 1000,
        p99: Math.random() * 3000 + 1500
      },
      throughput: {
        requestsPerSecond: Math.random() * 50 + 20,
        transactionsPerSecond: Math.random() * 30 + 10
      },
      errors: {
        rate: Math.random() * 3 + 0.5, // 0.5-3.5%
        count: Math.floor(Math.random() * 100 + 10),
        types: {
          'timeout': Math.floor(Math.random() * 30 + 5),
          'validation': Math.floor(Math.random() * 20 + 3),
          'connection': Math.floor(Math.random() * 15 + 2),
          'auth': Math.floor(Math.random() * 10 + 1)
        }
      },
      database: {
        connections: Math.floor(Math.random() * 50 + 10),
        queryTime: Math.random() * 100 + 20,
        slowQueries: Math.floor(Math.random() * 5)
      },
      cache: {
        hitRate: Math.random() * 15 + 85, // 85-100%
        missRate: Math.random() * 15, // 0-15%
        evictions: Math.floor(Math.random() * 100)
      }
    }

    // Simular métricas de negócio
    const businessMetrics: BusinessMetrics = {
      arbitrageOpportunities: {
        total: Math.floor(Math.random() * 500 + 200),
        profitable: Math.floor(Math.random() * 100 + 50),
        averageSpread: Math.random() * 2 + 0.5,
        topExchanges: ['gateio', 'mexc', 'bitget']
      },
      trading: {
        volume: Math.random() * 1000000 + 500000,
        profit: Math.random() * 10000 + 2000,
        successRate: Math.random() * 20 + 80, // 80-100%
        activePositions: Math.floor(Math.random() * 20 + 5)
      },
      exchanges: {
        connected: 3,
        total: 3,
        responseTime: {
          'gateio': Math.random() * 1000 + 800,
          'mexc': Math.random() * 800 + 600,
          'bitget': Math.random() * 600 + 400
        },
        errorRate: {
          'gateio': Math.random() * 2 + 0.5,
          'mexc': Math.random() * 1.5 + 0.3,
          'bitget': Math.random() * 1 + 0.2
        }
      },
      users: {
        active: Math.floor(Math.random() * 100 + 50),
        concurrent: Math.floor(Math.random() * 20 + 10),
        sessions: Math.floor(Math.random() * 150 + 75)
      }
    }

    // Determinar status geral
    const status = this.determineSystemStatus(systemMetrics, applicationMetrics, businessMetrics)

    return {
      timestamp,
      system: systemMetrics,
      application: applicationMetrics,
      business: businessMetrics,
      alerts: this.getActiveAlerts(),
      status
    }
  }

  /**
   * Determinar status do sistema
   */
  private determineSystemStatus(
    system: SystemMetrics,
    application: ApplicationMetrics,
    business: BusinessMetrics
  ): 'healthy' | 'warning' | 'critical' | 'down' {
    // Verificar condições críticas
    if (system.cpu.usage > 95 || 
        system.memory.percentage > 95 || 
        application.errors.rate > 10 ||
        business.exchanges.connected < 2) {
      return 'critical'
    }

    // Verificar condições de warning
    if (system.cpu.usage > this.config.thresholds.cpu ||
        system.memory.percentage > this.config.thresholds.memory ||
        application.responseTime.avg > this.config.thresholds.responseTime ||
        application.errors.rate > this.config.thresholds.errorRate) {
      return 'warning'
    }

    return 'healthy'
  }

  /**
   * Verificar alertas baseados em métricas
   */
  private async checkMetricAlerts(metrics: ProductionMetrics): Promise<void> {
    const newAlerts: Alert[] = []

    // Alertas de sistema
    if (metrics.system.cpu.usage > this.config.thresholds.cpu) {
      newAlerts.push(this.createAlert(
        'high-cpu',
        'critical',
        'system',
        'High CPU Usage',
        `CPU usage is ${metrics.system.cpu.usage.toFixed(1)}%`,
        'cpu_usage',
        this.config.thresholds.cpu,
        metrics.system.cpu.usage
      ))
    }

    if (metrics.system.memory.percentage > this.config.thresholds.memory) {
      newAlerts.push(this.createAlert(
        'high-memory',
        'critical',
        'system',
        'High Memory Usage',
        `Memory usage is ${metrics.system.memory.percentage.toFixed(1)}%`,
        'memory_usage',
        this.config.thresholds.memory,
        metrics.system.memory.percentage
      ))
    }

    // Alertas de aplicação
    if (metrics.application.responseTime.avg > this.config.thresholds.responseTime) {
      newAlerts.push(this.createAlert(
        'slow-response',
        'high',
        'application',
        'Slow Response Time',
        `Average response time is ${metrics.application.responseTime.avg.toFixed(0)}ms`,
        'response_time',
        this.config.thresholds.responseTime,
        metrics.application.responseTime.avg
      ))
    }

    if (metrics.application.errors.rate > this.config.thresholds.errorRate) {
      newAlerts.push(this.createAlert(
        'high-error-rate',
        'high',
        'application',
        'High Error Rate',
        `Error rate is ${metrics.application.errors.rate.toFixed(2)}%`,
        'error_rate',
        this.config.thresholds.errorRate,
        metrics.application.errors.rate
      ))
    }

    // Alertas de negócio
    if (metrics.business.exchanges.connected < metrics.business.exchanges.total) {
      newAlerts.push(this.createAlert(
        'exchange-disconnected',
        'critical',
        'business',
        'Exchange Disconnected',
        `Only ${metrics.business.exchanges.connected}/${metrics.business.exchanges.total} exchanges connected`,
        'exchanges_connected',
        metrics.business.exchanges.total,
        metrics.business.exchanges.connected
      ))
    }

    // Adicionar novos alertas
    for (const alert of newAlerts) {
      this.addAlert(alert)
    }
  }

  /**
   * Criar alerta
   */
  private createAlert(
    id: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    category: 'system' | 'application' | 'business' | 'security',
    title: string,
    description: string,
    metric: string,
    threshold: number,
    currentValue: number
  ): Alert {
    return {
      id,
      timestamp: Date.now(),
      severity,
      category,
      title,
      description,
      metric,
      threshold,
      currentValue,
      status: 'active',
      actions: this.generateAlertActions(category, severity)
    }
  }

  /**
   * Gerar ações para alerta
   */
  private generateAlertActions(
    category: string,
    severity: string
  ): string[] {
    const actions: string[] = []

    switch (category) {
      case 'system':
        actions.push('Check system resources')
        actions.push('Review process list')
        if (severity === 'critical') {
          actions.push('Consider scaling up')
        }
        break
      case 'application':
        actions.push('Check application logs')
        actions.push('Review recent deployments')
        actions.push('Monitor database performance')
        break
      case 'business':
        actions.push('Check exchange connections')
        actions.push('Verify API credentials')
        actions.push('Review trading algorithms')
        break
    }

    return actions
  }

  /**
   * Adicionar alerta
   */
  private addAlert(alert: Alert): void {
    // Verificar se alerta similar já existe
    const existingAlert = this.alerts.find(a => 
      a.id === alert.id && a.status === 'active'
    )

    if (!existingAlert) {
      this.alerts.push(alert)
      console.log(`🚨 New alert: ${alert.title} (${alert.severity})`)
      
      // Enviar notificação
      this.sendAlertNotification(alert)
    }
  }

  /**
   * Enviar notificação de alerta
   */
  private async sendAlertNotification(alert: Alert): Promise<void> {
    // Simular envio de notificação
    console.log(`📧 Sending alert notification: ${alert.title}`)
    
    if (this.config.alertChannels.email) {
      console.log(`📧 Email sent for alert: ${alert.id}`)
    }
    
    if (this.config.alertChannels.slack) {
      console.log(`💬 Slack message sent for alert: ${alert.id}`)
    }
    
    if (this.config.alertChannels.sms && alert.severity === 'critical') {
      console.log(`📱 SMS sent for critical alert: ${alert.id}`)
    }
  }

  /**
   * Iniciar monitoramento de alertas
   */
  private startAlertMonitoring(): void {
    const checkAlerts = () => {
      if (!this.isMonitoring) return

      // Auto-resolver alertas antigos
      this.autoResolveAlerts()

      // Agendar próxima verificação
      setTimeout(checkAlerts, this.config.intervals.alerts)
    }

    checkAlerts()
  }

  /**
   * Auto-resolver alertas antigos
   */
  private autoResolveAlerts(): void {
    const now = Date.now()
    const autoResolveTime = 5 * 60 * 1000 // 5 minutos

    this.alerts.forEach(alert => {
      if (alert.status === 'active' && (now - alert.timestamp) > autoResolveTime) {
        // Verificar se condição ainda existe
        const shouldResolve = this.shouldAutoResolveAlert(alert)
        
        if (shouldResolve) {
          alert.status = 'resolved'
          console.log(`✅ Auto-resolved alert: ${alert.title}`)
        }
      }
    })
  }

  /**
   * Verificar se alerta deve ser auto-resolvido
   */
  private shouldAutoResolveAlert(alert: Alert): boolean {
    const latestMetrics = this.getLatestMetrics()
    if (!latestMetrics) return false

    switch (alert.metric) {
      case 'cpu_usage':
        return latestMetrics.system.cpu.usage < alert.threshold
      case 'memory_usage':
        return latestMetrics.system.memory.percentage < alert.threshold
      case 'response_time':
        return latestMetrics.application.responseTime.avg < alert.threshold
      case 'error_rate':
        return latestMetrics.application.errors.rate < alert.threshold
      default:
        return false
    }
  }

  /**
   * Iniciar health checks
   */
  private startHealthChecks(): void {
    const runHealthChecks = async () => {
      if (!this.isMonitoring) return

      try {
        await this.performHealthChecks()
      } catch (error) {
        console.error('❌ Health check failed:', error)
      }

      // Agendar próximo health check
      setTimeout(runHealthChecks, this.config.intervals.healthCheck)
    }

    runHealthChecks()
  }

  /**
   * Executar health checks
   */
  private async performHealthChecks(): Promise<void> {
    // Simular health checks
    const checks = [
      'Database connectivity',
      'Exchange API connectivity',
      'Cache availability',
      'External services'
    ]

    for (const check of checks) {
      // Simular health check
      const isHealthy = Math.random() > 0.05 // 95% chance de sucesso
      
      if (!isHealthy) {
        const alert = this.createAlert(
          `health-check-${check.toLowerCase().replace(/\s+/g, '-')}`,
          'high',
          'system',
          'Health Check Failed',
          `${check} health check failed`,
          'health_check',
          1,
          0
        )
        
        this.addAlert(alert)
      }
    }
  }

  /**
   * Obter métricas mais recentes
   */
  getLatestMetrics(): ProductionMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null
  }

  /**
   * Obter histórico de métricas
   */
  getMetricsHistory(limit?: number): ProductionMetrics[] {
    const history = [...this.metrics]
    return limit ? history.slice(-limit) : history
  }

  /**
   * Obter alertas ativos
   */
  getActiveAlerts(): Alert[] {
    return this.alerts.filter(alert => alert.status === 'active')
  }

  /**
   * Obter todos os alertas
   */
  getAllAlerts(): Alert[] {
    return [...this.alerts]
  }

  /**
   * Resolver alerta
   */
  resolveAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId)
    if (alert) {
      alert.status = 'resolved'
      console.log(`✅ Alert resolved: ${alert.title}`)
    }
  }

  /**
   * Reconhecer alerta
   */
  acknowledgeAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId)
    if (alert) {
      alert.status = 'acknowledged'
      console.log(`👍 Alert acknowledged: ${alert.title}`)
    }
  }

  /**
   * Obter status do monitoramento
   */
  getMonitoringStatus(): {
    isRunning: boolean
    uptime: number
    metricsCollected: number
    activeAlerts: number
  } {
    return {
      isRunning: this.isMonitoring,
      uptime: this.isMonitoring ? Date.now() - (this.metrics[0]?.timestamp || Date.now()) : 0,
      metricsCollected: this.metrics.length,
      activeAlerts: this.getActiveAlerts().length
    }
  }
}

export default ProductionMonitor