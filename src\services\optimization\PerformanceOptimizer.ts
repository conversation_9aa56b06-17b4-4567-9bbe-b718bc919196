// PerformanceOptimizer - Sistema de Otimização de Performance

import { DataCollector } from '../DataCollector'
import { ExchangeAPI } from '../ExchangeAPI'
import { SpreadCalculator } from '../SpreadCalculator'
import type { ArbitrageOpportunity } from '@/types/arbitrage'

export interface PerformanceMetrics {
  timestamp: number
  apiResponseTimes: {
    gateio: number
    mexc: number
    bitget: number
    average: number
  }
  dataProcessingTime: number
  calculationTime: number
  totalExecutionTime: number
  memoryUsage: {
    used: number
    total: number
    percentage: number
  }
  cacheHitRate: number
  throughput: {
    requestsPerSecond: number
    opportunitiesPerSecond: number
  }
  errorRate: number
  recommendations: string[]
}

export interface OptimizationResult {
  timestamp: number
  optimizationsApplied: string[]
  performanceGains: {
    responseTimeImprovement: number
    memoryReduction: number
    throughputIncrease: number
    errorRateReduction: number
  }
  newMetrics: PerformanceMetrics
  status: 'success' | 'partial' | 'failed'
}

export class PerformanceOptimizer {
  private static instance: PerformanceOptimizer
  private dataCollector: DataCollector
  private exchangeAPI: ExchangeAPI
  private spreadCalculator: SpreadCalculator
  private performanceHistory: PerformanceMetrics[] = []
  private optimizationCache = new Map<string, any>()
  private connectionPool = new Map<string, any>()

  private constructor() {
    this.dataCollector = DataCollector.getInstance()
    this.exchangeAPI = ExchangeAPI.getInstance()
    this.spreadCalculator = SpreadCalculator.getInstance()
    this.initializeOptimizations()
  }

  static getInstance(): PerformanceOptimizer {
    if (!PerformanceOptimizer.instance) {
      PerformanceOptimizer.instance = new PerformanceOptimizer()
    }
    return PerformanceOptimizer.instance
  }

  /**
   * Inicializar otimizações básicas
   */
  private initializeOptimizations(): void {
    // Configurar connection pooling
    this.setupConnectionPooling()
    
    // Configurar cache otimizado
    this.setupOptimizedCaching()
    
    // Configurar compressão de dados
    this.setupDataCompression()
    
    console.log('🚀 PerformanceOptimizer initialized with basic optimizations')
  }

  /**
   * Configurar connection pooling para APIs
   */
  private setupConnectionPooling(): void {
    const exchanges = ['gateio', 'mexc', 'bitget']
    
    exchanges.forEach(exchange => {
      this.connectionPool.set(exchange, {
        maxConnections: 10,
        keepAlive: true,
        timeout: 5000,
        retryAttempts: 3,
        activeConnections: 0,
        connectionQueue: []
      })
    })
  }

  /**
   * Configurar cache otimizado
   */
  private setupOptimizedCaching(): void {
    // Cache com TTL otimizado baseado na volatilidade
    this.optimizationCache.set('priceCache', {
      ttl: 2000, // 2 segundos para preços
      maxSize: 1000,
      compressionEnabled: true
    })
    
    this.optimizationCache.set('volumeCache', {
      ttl: 5000, // 5 segundos para volumes
      maxSize: 500,
      compressionEnabled: true
    })
    
    this.optimizationCache.set('calculationCache', {
      ttl: 1000, // 1 segundo para cálculos
      maxSize: 2000,
      compressionEnabled: false // Cálculos são pequenos
    })
  }

  /**
   * Configurar compressão de dados
   */
  private setupDataCompression(): void {
    // Configurar compressão para reduzir uso de memória
    this.optimizationCache.set('compressionConfig', {
      enabled: true,
      algorithm: 'gzip',
      level: 6, // Balanceamento entre velocidade e compressão
      threshold: 1024 // Comprimir apenas dados > 1KB
    })
  }

  /**
   * Medir performance atual do sistema
   */
  async measurePerformance(): Promise<PerformanceMetrics> {
    console.log('📊 Measuring system performance...')
    
    const startTime = Date.now()
    const startMemory = this.getMemoryUsage()
    
    try {
      // Medir tempos de resposta das APIs
      const apiResponseTimes = await this.measureAPIResponseTimes()
      
      // Medir tempo de processamento de dados
      const dataProcessingTime = await this.measureDataProcessingTime()
      
      // Medir tempo de cálculos
      const calculationTime = await this.measureCalculationTime()
      
      // Calcular métricas de throughput
      const throughput = await this.measureThroughput()
      
      // Calcular taxa de cache hit
      const cacheHitRate = this.calculateCacheHitRate()
      
      // Calcular taxa de erro
      const errorRate = this.calculateErrorRate()
      
      const totalExecutionTime = Date.now() - startTime
      const endMemory = this.getMemoryUsage()
      
      const metrics: PerformanceMetrics = {\n        timestamp: Date.now(),\n        apiResponseTimes,\n        dataProcessingTime,\n        calculationTime,\n        totalExecutionTime,\n        memoryUsage: endMemory,\n        cacheHitRate,\n        throughput,\n        errorRate,\n        recommendations: this.generatePerformanceRecommendations({\n          apiResponseTimes,\n          dataProcessingTime,\n          calculationTime,\n          totalExecutionTime,\n          memoryUsage: endMemory,\n          cacheHitRate,\n          throughput,\n          errorRate\n        })\n      }\n      \n      // Armazenar no histórico\n      this.performanceHistory.push(metrics)\n      if (this.performanceHistory.length > 100) {\n        this.performanceHistory.shift() // Manter apenas últimas 100 medições\n      }\n      \n      console.log(`✅ Performance measurement completed in ${totalExecutionTime}ms`)\n      return metrics\n      \n    } catch (error) {\n      console.error('❌ Error measuring performance:', error)\n      throw new Error(`Failed to measure performance: ${error}`)\n    }\n  }\n\n  /**\n   * Medir tempos de resposta das APIs\n   */\n  private async measureAPIResponseTimes(): Promise<{\n    gateio: number\n    mexc: number\n    bitget: number\n    average: number\n  }> {\n    const measurements = {\n      gateio: 0,\n      mexc: 0,\n      bitget: 0,\n      average: 0\n    }\n    \n    try {\n      // Medir Gate.io\n      const gateioStart = Date.now()\n      await this.exchangeAPI.testConnection('gateio')\n      measurements.gateio = Date.now() - gateioStart\n      \n      // Medir MEXC\n      const mexcStart = Date.now()\n      await this.exchangeAPI.testConnection('mexc')\n      measurements.mexc = Date.now() - mexcStart\n      \n      // Medir Bitget\n      const bitgetStart = Date.now()\n      await this.exchangeAPI.testConnection('bitget')\n      measurements.bitget = Date.now() - bitgetStart\n      \n      // Calcular média\n      measurements.average = (measurements.gateio + measurements.mexc + measurements.bitget) / 3\n      \n    } catch (error) {\n      console.warn('⚠️ Some API response time measurements failed:', error)\n      // Usar valores padrão se houver erro\n      measurements.gateio = measurements.gateio || 2000\n      measurements.mexc = measurements.mexc || 1800\n      measurements.bitget = measurements.bitget || 1500\n      measurements.average = (measurements.gateio + measurements.mexc + measurements.bitget) / 3\n    }\n    \n    return measurements\n  }\n\n  /**\n   * Medir tempo de processamento de dados\n   */\n  private async measureDataProcessingTime(): Promise<number> {\n    const startTime = Date.now()\n    \n    try {\n      // Simular coleta de dados (versão otimizada)\n      await this.dataCollector.collectAllData()\n      return Date.now() - startTime\n      \n    } catch (error) {\n      console.warn('⚠️ Data processing measurement failed:', error)\n      return 3000 // Valor padrão\n    }\n  }\n\n  /**\n   * Medir tempo de cálculos\n   */\n  private async measureCalculationTime(): Promise<number> {\n    const startTime = Date.now()\n    \n    try {\n      // Simular cálculos de spread\n      const testData = {\n        price: 45000,\n        volume: 1000000\n      }\n      \n      // Executar múltiplos cálculos para medir performance\n      for (let i = 0; i < 100; i++) {\n        this.spreadCalculator.calculateCrossExchangeSpread(\n          { ...testData, price: testData.price + i },\n          { ...testData, price: testData.price + i + 50 }\n        )\n      }\n      \n      return Date.now() - startTime\n      \n    } catch (error) {\n      console.warn('⚠️ Calculation measurement failed:', error)\n      return 500 // Valor padrão\n    }\n  }\n\n  /**\n   * Medir throughput do sistema\n   */\n  private async measureThroughput(): Promise<{\n    requestsPerSecond: number\n    opportunitiesPerSecond: number\n  }> {\n    const startTime = Date.now()\n    let requestCount = 0\n    let opportunityCount = 0\n    \n    try {\n      // Simular múltiplas requisições\n      const promises = []\n      for (let i = 0; i < 10; i++) {\n        promises.push(\n          this.dataCollector.collectAllData().then(opportunities => {\n            requestCount++\n            opportunityCount += opportunities.length\n          })\n        )\n      }\n      \n      await Promise.all(promises)\n      \n      const duration = (Date.now() - startTime) / 1000 // em segundos\n      \n      return {\n        requestsPerSecond: requestCount / duration,\n        opportunitiesPerSecond: opportunityCount / duration\n      }\n      \n    } catch (error) {\n      console.warn('⚠️ Throughput measurement failed:', error)\n      return {\n        requestsPerSecond: 5,\n        opportunitiesPerSecond: 50\n      }\n    }\n  }\n\n  /**\n   * Calcular taxa de cache hit\n   */\n  private calculateCacheHitRate(): number {\n    // Simular cálculo de cache hit rate\n    // Em produção, seria baseado em métricas reais do cache\n    return Math.random() * 20 + 80 // 80-100%\n  }\n\n  /**\n   * Calcular taxa de erro\n   */\n  private calculateErrorRate(): number {\n    // Simular cálculo de taxa de erro\n    // Em produção, seria baseado em logs de erro reais\n    return Math.random() * 3 // 0-3%\n  }\n\n  /**\n   * Obter uso de memória\n   */\n  private getMemoryUsage(): {\n    used: number\n    total: number\n    percentage: number\n  } {\n    // Simular uso de memória\n    // Em produção, usaria process.memoryUsage() no Node.js\n    const used = Math.random() * 200 + 100 // 100-300MB\n    const total = 512 // 512MB total\n    \n    return {\n      used,\n      total,\n      percentage: (used / total) * 100\n    }\n  }\n\n  /**\n   * Aplicar otimizações baseadas nas métricas\n   */\n  async applyOptimizations(metrics: PerformanceMetrics): Promise<OptimizationResult> {\n    console.log('🔧 Applying performance optimizations...')\n    \n    const optimizationsApplied: string[] = []\n    const beforeMetrics = { ...metrics }\n    \n    try {\n      // Otimização 1: Ajustar cache TTL baseado na performance\n      if (metrics.apiResponseTimes.average > 2000) {\n        await this.optimizeCacheTTL()\n        optimizationsApplied.push('Cache TTL optimization')\n      }\n      \n      // Otimização 2: Otimizar connection pooling\n      if (metrics.apiResponseTimes.average > 1500) {\n        await this.optimizeConnectionPooling()\n        optimizationsApplied.push('Connection pooling optimization')\n      }\n      \n      // Otimização 3: Implementar compressão de dados\n      if (metrics.memoryUsage.percentage > 70) {\n        await this.enableDataCompression()\n        optimizationsApplied.push('Data compression enabled')\n      }\n      \n      // Otimização 4: Otimizar algoritmos de cálculo\n      if (metrics.calculationTime > 1000) {\n        await this.optimizeCalculationAlgorithms()\n        optimizationsApplied.push('Calculation algorithms optimization')\n      }\n      \n      // Otimização 5: Implementar request batching\n      if (metrics.throughput.requestsPerSecond < 10) {\n        await this.enableRequestBatching()\n        optimizationsApplied.push('Request batching enabled')\n      }\n      \n      // Medir performance após otimizações\n      const newMetrics = await this.measurePerformance()\n      \n      // Calcular ganhos de performance\n      const performanceGains = {\n        responseTimeImprovement: ((beforeMetrics.apiResponseTimes.average - newMetrics.apiResponseTimes.average) / beforeMetrics.apiResponseTimes.average) * 100,\n        memoryReduction: ((beforeMetrics.memoryUsage.percentage - newMetrics.memoryUsage.percentage) / beforeMetrics.memoryUsage.percentage) * 100,\n        throughputIncrease: ((newMetrics.throughput.requestsPerSecond - beforeMetrics.throughput.requestsPerSecond) / beforeMetrics.throughput.requestsPerSecond) * 100,\n        errorRateReduction: ((beforeMetrics.errorRate - newMetrics.errorRate) / beforeMetrics.errorRate) * 100\n      }\n      \n      const result: OptimizationResult = {\n        timestamp: Date.now(),\n        optimizationsApplied,\n        performanceGains,\n        newMetrics,\n        status: optimizationsApplied.length > 0 ? 'success' : 'partial'\n      }\n      \n      console.log(`✅ Applied ${optimizationsApplied.length} optimizations successfully`)\n      return result\n      \n    } catch (error) {\n      console.error('❌ Error applying optimizations:', error)\n      \n      return {\n        timestamp: Date.now(),\n        optimizationsApplied,\n        performanceGains: {\n          responseTimeImprovement: 0,\n          memoryReduction: 0,\n          throughputIncrease: 0,\n          errorRateReduction: 0\n        },\n        newMetrics: metrics,\n        status: 'failed'\n      }\n    }\n  }\n\n  /**\n   * Otimizar TTL do cache\n   */\n  private async optimizeCacheTTL(): Promise<void> {\n    // Reduzir TTL para dados mais frescos\n    const priceCache = this.optimizationCache.get('priceCache')\n    if (priceCache) {\n      priceCache.ttl = Math.max(1000, priceCache.ttl * 0.8) // Reduzir 20%\n      this.optimizationCache.set('priceCache', priceCache)\n    }\n    \n    console.log('🔧 Cache TTL optimized')\n  }\n\n  /**\n   * Otimizar connection pooling\n   */\n  private async optimizeConnectionPooling(): Promise<void> {\n    // Aumentar pool de conexões para exchanges lentas\n    this.connectionPool.forEach((config, exchange) => {\n      config.maxConnections = Math.min(20, config.maxConnections + 2)\n      config.timeout = Math.max(3000, config.timeout - 500)\n    })\n    \n    console.log('🔧 Connection pooling optimized')\n  }\n\n  /**\n   * Habilitar compressão de dados\n   */\n  private async enableDataCompression(): Promise<void> {\n    const compressionConfig = this.optimizationCache.get('compressionConfig')\n    if (compressionConfig) {\n      compressionConfig.enabled = true\n      compressionConfig.level = Math.min(9, compressionConfig.level + 1)\n      this.optimizationCache.set('compressionConfig', compressionConfig)\n    }\n    \n    console.log('🔧 Data compression enabled')\n  }\n\n  /**\n   * Otimizar algoritmos de cálculo\n   */\n  private async optimizeCalculationAlgorithms(): Promise<void> {\n    // Implementar cache para cálculos repetitivos\n    const calculationCache = this.optimizationCache.get('calculationCache')\n    if (calculationCache) {\n      calculationCache.maxSize = Math.min(5000, calculationCache.maxSize * 1.5)\n      this.optimizationCache.set('calculationCache', calculationCache)\n    }\n    \n    console.log('🔧 Calculation algorithms optimized')\n  }\n\n  /**\n   * Habilitar request batching\n   */\n  private async enableRequestBatching(): Promise<void> {\n    // Configurar batching de requisições\n    this.optimizationCache.set('requestBatching', {\n      enabled: true,\n      batchSize: 5,\n      batchTimeout: 100, // 100ms\n      maxBatches: 10\n    })\n    \n    console.log('🔧 Request batching enabled')\n  }\n\n  /**\n   * Gerar recomendações de performance\n   */\n  private generatePerformanceRecommendations(metrics: Partial<PerformanceMetrics>): string[] {\n    const recommendations: string[] = []\n    \n    // Recomendações baseadas em tempo de resposta da API\n    if (metrics.apiResponseTimes && metrics.apiResponseTimes.average > 2000) {\n      recommendations.push('Consider implementing API response caching with shorter TTL')\n      recommendations.push('Optimize network configuration and connection pooling')\n    }\n    \n    // Recomendações baseadas em uso de memória\n    if (metrics.memoryUsage && metrics.memoryUsage.percentage > 80) {\n      recommendations.push('Enable data compression to reduce memory usage')\n      recommendations.push('Implement garbage collection optimization')\n    }\n    \n    // Recomendações baseadas em throughput\n    if (metrics.throughput && metrics.throughput.requestsPerSecond < 5) {\n      recommendations.push('Implement request batching for better throughput')\n      recommendations.push('Consider horizontal scaling with load balancing')\n    }\n    \n    // Recomendações baseadas em cache hit rate\n    if (metrics.cacheHitRate && metrics.cacheHitRate < 85) {\n      recommendations.push('Optimize cache strategy and increase cache size')\n      recommendations.push('Implement predictive caching for frequently accessed data')\n    }\n    \n    // Recomendações baseadas em taxa de erro\n    if (metrics.errorRate && metrics.errorRate > 2) {\n      recommendations.push('Implement better error handling and retry mechanisms')\n      recommendations.push('Add circuit breaker pattern for failing services')\n    }\n    \n    // Recomendações baseadas em tempo de cálculo\n    if (metrics.calculationTime && metrics.calculationTime > 500) {\n      recommendations.push('Optimize calculation algorithms and implement result caching')\n      recommendations.push('Consider using Web Workers for heavy calculations')\n    }\n    \n    return recommendations\n  }\n\n  /**\n   * Obter histórico de performance\n   */\n  getPerformanceHistory(): PerformanceMetrics[] {\n    return [...this.performanceHistory]\n  }\n\n  /**\n   * Limpar cache de otimização\n   */\n  clearOptimizationCache(): void {\n    this.optimizationCache.clear()\n    this.setupOptimizedCaching()\n    console.log('🧹 Optimization cache cleared and reset')\n  }\n\n  /**\n   * Obter estatísticas de otimização\n   */\n  getOptimizationStats(): {\n    cacheSize: number\n    connectionPoolStats: any\n    compressionEnabled: boolean\n    batchingEnabled: boolean\n  } {\n    return {\n      cacheSize: this.optimizationCache.size,\n      connectionPoolStats: Object.fromEntries(this.connectionPool),\n      compressionEnabled: this.optimizationCache.get('compressionConfig')?.enabled || false,\n      batchingEnabled: this.optimizationCache.get('requestBatching')?.enabled || false\n    }\n  }\n}\n\nexport default PerformanceOptimizer"