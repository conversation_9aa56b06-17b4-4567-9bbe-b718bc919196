// StressTestRunner - Sistema de Stress Testing

import { DataCollector } from '../DataCollector'
import { ExchangeAPI } from '../ExchangeAPI'
import { SpreadCalculator } from '../SpreadCalculator'
import { AlertSystem } from '../AlertSystem'

export interface StressTestConfig {
  duration: number // em segundos
  concurrentUsers: number
  requestsPerSecond: number
  testScenarios: StressTestScenario[]
  thresholds: {
    maxResponseTime: number
    maxErrorRate: number
    maxMemoryUsage: number
    minThroughput: number
  }
}

export interface StressTestScenario {
  name: string
  weight: number // 0-1, peso relativo do cenário
  actions: StressTestAction[]
}

export interface StressTestAction {
  type: 'api_call' | 'data_processing' | 'calculation' | 'alert_check'
  target: string
  parameters?: any
  expectedDuration?: number
}

export interface StressTestResult {
  timestamp: number
  config: StressTestConfig
  duration: number
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  averageResponseTime: number
  maxResponseTime: number
  minResponseTime: number
  throughput: number
  errorRate: number
  memoryUsage: {
    initial: number
    peak: number
    final: number
  }
  performanceMetrics: {
    p50: number // 50th percentile
    p90: number // 90th percentile
    p95: number // 95th percentile
    p99: number // 99th percentile
  }
  errors: StressTestError[]
  recommendations: string[]
  status: 'passed' | 'failed' | 'warning'
}

export interface StressTestError {
  timestamp: number
  type: string
  message: string
  scenario: string
  action: string
}

export class StressTestRunner {
  private static instance: StressTestRunner
  private dataCollector: DataCollector
  private exchangeAPI: ExchangeAPI
  private spreadCalculator: SpreadCalculator
  private alertSystem: AlertSystem
  private isRunning = false
  private currentTest: StressTestResult | null = null

  private constructor() {
    this.dataCollector = DataCollector.getInstance()
    this.exchangeAPI = ExchangeAPI.getInstance()
    this.spreadCalculator = SpreadCalculator.getInstance()
    this.alertSystem = AlertSystem.getInstance()
  }

  static getInstance(): StressTestRunner {
    if (!StressTestRunner.instance) {
      StressTestRunner.instance = new StressTestRunner()
    }
    return StressTestRunner.instance
  }

  /**
   * Executar teste de stress
   */
  async runStressTest(config: StressTestConfig): Promise<StressTestResult> {
    if (this.isRunning) {
      throw new Error('Stress test is already running')
    }

    console.log(`🚀 Starting stress test with ${config.concurrentUsers} concurrent users for ${config.duration}s`)
    
    this.isRunning = true
    const startTime = Date.now()
    const initialMemory = this.getMemoryUsage()
    
    const result: StressTestResult = {
      timestamp: startTime,
      config,
      duration: 0,
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      maxResponseTime: 0,
      minResponseTime: Infinity,
      throughput: 0,
      errorRate: 0,
      memoryUsage: {
        initial: initialMemory,
        peak: initialMemory,
        final: initialMemory
      },
      performanceMetrics: {
        p50: 0,
        p90: 0,
        p95: 0,
        p99: 0
      },
      errors: [],
      recommendations: [],
      status: 'passed'
    }

    try {
      // Executar teste
      await this.executeStressTest(config, result)
      
      // Calcular métricas finais
      this.calculateFinalMetrics(result)
      
      // Determinar status do teste
      result.status = this.determineTestStatus(result, config)
      
      // Gerar recomendações
      result.recommendations = this.generateRecommendations(result, config)
      
      console.log(`✅ Stress test completed: ${result.status} (${result.totalRequests} requests, ${result.errorRate.toFixed(2)}% error rate)`)
      
    } catch (error) {
      console.error('❌ Stress test failed:', error)
      result.status = 'failed'
      result.errors.push({
        timestamp: Date.now(),
        type: 'test_failure',
        message: `Test execution failed: ${error}`,
        scenario: 'system',
        action: 'test_execution'
      })
    } finally {
      this.isRunning = false
      this.currentTest = result
    }

    return result
  }

  /**
   * Executar o teste de stress propriamente dito
   */
  private async executeStressTest(config: StressTestConfig, result: StressTestResult): Promise<void> {
    const startTime = Date.now()
    const endTime = startTime + (config.duration * 1000)
    const responseTimes: number[] = []
    
    // Criar workers virtuais para simular usuários concorrentes
    const workers = Array.from({ length: config.concurrentUsers }, (_, index) => 
      this.createVirtualUser(index, config, result, responseTimes)
    )
    
    // Executar workers em paralelo
    const workerPromises = workers.map(worker => worker())
    
    // Monitor de memória
    const memoryMonitor = setInterval(() => {
      const currentMemory = this.getMemoryUsage()
      if (currentMemory > result.memoryUsage.peak) {
        result.memoryUsage.peak = currentMemory
      }
    }, 1000)
    
    // Aguardar conclusão ou timeout
    await Promise.race([
      Promise.all(workerPromises),
      new Promise(resolve => setTimeout(resolve, config.duration * 1000))
    ])
    
    clearInterval(memoryMonitor)
    
    // Calcular métricas de tempo
    result.duration = Date.now() - startTime
    result.memoryUsage.final = this.getMemoryUsage()
    
    // Calcular percentis de performance
    if (responseTimes.length > 0) {
      responseTimes.sort((a, b) => a - b)
      result.performanceMetrics.p50 = this.calculatePercentile(responseTimes, 50)
      result.performanceMetrics.p90 = this.calculatePercentile(responseTimes, 90)
      result.performanceMetrics.p95 = this.calculatePercentile(responseTimes, 95)
      result.performanceMetrics.p99 = this.calculatePercentile(responseTimes, 99)
    }
  }

  /**
   * Criar usuário virtual para simular carga
   */
  private createVirtualUser(
    userId: number, 
    config: StressTestConfig, 
    result: StressTestResult,
    responseTimes: number[]
  ): () => Promise<void> {
    return async () => {
      const startTime = Date.now()
      const endTime = startTime + (config.duration * 1000)
      const requestInterval = 1000 / config.requestsPerSecond
      
      while (Date.now() < endTime) {
        try {
          // Selecionar cenário baseado no peso
          const scenario = this.selectScenario(config.testScenarios)
          
          // Executar ações do cenário
          for (const action of scenario.actions) {
            const actionStartTime = Date.now()
            
            try {\n              await this.executeAction(action, userId)\n              \n              const actionDuration = Date.now() - actionStartTime\n              responseTimes.push(actionDuration)\n              \n              result.totalRequests++\n              result.successfulRequests++\n              \n              // Atualizar métricas de tempo de resposta\n              if (actionDuration > result.maxResponseTime) {\n                result.maxResponseTime = actionDuration\n              }\n              if (actionDuration < result.minResponseTime) {\n                result.minResponseTime = actionDuration\n              }\n              \n            } catch (error) {\n              result.totalRequests++\n              result.failedRequests++\n              \n              result.errors.push({\n                timestamp: Date.now(),\n                type: 'action_error',\n                message: `Action ${action.type} failed: ${error}`,\n                scenario: scenario.name,\n                action: action.type\n              })\n            }\n          }\n          \n          // Aguardar próxima requisição\n          await new Promise(resolve => setTimeout(resolve, requestInterval))\n          \n        } catch (error) {\n          result.errors.push({\n            timestamp: Date.now(),\n            type: 'user_error',\n            message: `Virtual user ${userId} error: ${error}`,\n            scenario: 'system',\n            action: 'user_execution'\n          })\n        }\n      }\n    }\n  }\n\n  /**\n   * Selecionar cenário baseado no peso\n   */\n  private selectScenario(scenarios: StressTestScenario[]): StressTestScenario {\n    const random = Math.random()\n    let cumulativeWeight = 0\n    \n    for (const scenario of scenarios) {\n      cumulativeWeight += scenario.weight\n      if (random <= cumulativeWeight) {\n        return scenario\n      }\n    }\n    \n    return scenarios[scenarios.length - 1] // Fallback\n  }\n\n  /**\n   * Executar ação específica\n   */\n  private async executeAction(action: StressTestAction, userId: number): Promise<void> {\n    switch (action.type) {\n      case 'api_call':\n        await this.executeAPICall(action, userId)\n        break\n      case 'data_processing':\n        await this.executeDataProcessing(action, userId)\n        break\n      case 'calculation':\n        await this.executeCalculation(action, userId)\n        break\n      case 'alert_check':\n        await this.executeAlertCheck(action, userId)\n        break\n      default:\n        throw new Error(`Unknown action type: ${action.type}`)\n    }\n  }\n\n  /**\n   * Executar chamada de API\n   */\n  private async executeAPICall(action: StressTestAction, userId: number): Promise<void> {\n    const exchange = action.target as 'gateio' | 'mexc' | 'bitget'\n    \n    // Simular chamada de API\n    await new Promise(resolve => {\n      const delay = Math.random() * 2000 + 500 // 500-2500ms\n      setTimeout(resolve, delay)\n    })\n    \n    // Simular possível falha (5% chance)\n    if (Math.random() < 0.05) {\n      throw new Error(`API call to ${exchange} failed`)\n    }\n  }\n\n  /**\n   * Executar processamento de dados\n   */\n  private async executeDataProcessing(action: StressTestAction, userId: number): Promise<void> {\n    // Simular processamento de dados\n    const opportunities = await this.dataCollector.collectAllData()\n    \n    // Simular processamento adicional\n    await new Promise(resolve => {\n      const delay = Math.random() * 1000 + 200 // 200-1200ms\n      setTimeout(resolve, delay)\n    })\n    \n    if (opportunities.length === 0) {\n      throw new Error('No opportunities found during data processing')\n    }\n  }\n\n  /**\n   * Executar cálculo\n   */\n  private async executeCalculation(action: StressTestAction, userId: number): Promise<void> {\n    // Simular cálculos intensivos\n    for (let i = 0; i < 100; i++) {\n      this.spreadCalculator.calculateCrossExchangeSpread(\n        { price: 45000 + i, volume: 1000000 },\n        { price: 45100 + i, volume: 2000000 }\n      )\n    }\n    \n    // Simular delay de processamento\n    await new Promise(resolve => {\n      const delay = Math.random() * 300 + 100 // 100-400ms\n      setTimeout(resolve, delay)\n    })\n  }\n\n  /**\n   * Executar verificação de alertas\n   */\n  private async executeAlertCheck(action: StressTestAction, userId: number): Promise<void> {\n    // Simular verificação de alertas\n    const testOpportunity = {\n      id: `test_${userId}_${Date.now()}`,\n      symbol: 'BTC/USDT',\n      spotExchange: 'gateio' as const,\n      spotPrice: 45000,\n      futuresExchange: 'mexc' as const,\n      futuresPrice: 45500,\n      spreadPercentage: 1.11,\n      spreadAbsolute: 500,\n      profitability: 'high' as const,\n      volume: 1000000,\n      timestamp: Date.now(),\n      lastUpdate: new Date(),\n      strategy: 'Test strategy'\n    }\n    \n    this.alertSystem.checkCrossExchangeSpreadAlert(testOpportunity)\n    \n    // Simular delay de processamento\n    await new Promise(resolve => {\n      const delay = Math.random() * 200 + 50 // 50-250ms\n      setTimeout(resolve, delay)\n    })\n  }\n\n  /**\n   * Calcular percentil\n   */\n  private calculatePercentile(sortedArray: number[], percentile: number): number {\n    const index = Math.ceil((percentile / 100) * sortedArray.length) - 1\n    return sortedArray[Math.max(0, index)]\n  }\n\n  /**\n   * Calcular métricas finais\n   */\n  private calculateFinalMetrics(result: StressTestResult): void {\n    if (result.totalRequests > 0) {\n      result.errorRate = (result.failedRequests / result.totalRequests) * 100\n      result.throughput = result.totalRequests / (result.duration / 1000)\n      \n      if (result.successfulRequests > 0) {\n        // Calcular tempo médio apenas para requisições bem-sucedidas\n        result.averageResponseTime = result.performanceMetrics.p50\n      }\n    }\n    \n    if (result.minResponseTime === Infinity) {\n      result.minResponseTime = 0\n    }\n  }\n\n  /**\n   * Determinar status do teste\n   */\n  private determineTestStatus(result: StressTestResult, config: StressTestConfig): 'passed' | 'failed' | 'warning' {\n    const thresholds = config.thresholds\n    \n    // Verificar falhas críticas\n    if (result.errorRate > thresholds.maxErrorRate) {\n      return 'failed'\n    }\n    \n    if (result.averageResponseTime > thresholds.maxResponseTime) {\n      return 'failed'\n    }\n    \n    if (result.memoryUsage.peak > thresholds.maxMemoryUsage) {\n      return 'failed'\n    }\n    \n    if (result.throughput < thresholds.minThroughput) {\n      return 'failed'\n    }\n    \n    // Verificar warnings\n    if (result.errorRate > thresholds.maxErrorRate * 0.7 ||\n        result.averageResponseTime > thresholds.maxResponseTime * 0.8 ||\n        result.memoryUsage.peak > thresholds.maxMemoryUsage * 0.8 ||\n        result.throughput < thresholds.minThroughput * 1.2) {\n      return 'warning'\n    }\n    \n    return 'passed'\n  }\n\n  /**\n   * Gerar recomendações\n   */\n  private generateRecommendations(result: StressTestResult, config: StressTestConfig): string[] {\n    const recommendations: string[] = []\n    const thresholds = config.thresholds\n    \n    if (result.errorRate > thresholds.maxErrorRate * 0.5) {\n      recommendations.push(`Error rate is ${result.errorRate.toFixed(2)}%, consider improving error handling`)\n    }\n    \n    if (result.averageResponseTime > thresholds.maxResponseTime * 0.7) {\n      recommendations.push(`Average response time is ${result.averageResponseTime.toFixed(0)}ms, consider performance optimization`)\n    }\n    \n    if (result.memoryUsage.peak > thresholds.maxMemoryUsage * 0.7) {\n      recommendations.push(`Peak memory usage is ${result.memoryUsage.peak.toFixed(0)}MB, consider memory optimization`)\n    }\n    \n    if (result.throughput < thresholds.minThroughput * 1.5) {\n      recommendations.push(`Throughput is ${result.throughput.toFixed(1)} req/s, consider scaling improvements`)\n    }\n    \n    if (result.performanceMetrics.p99 > result.performanceMetrics.p50 * 3) {\n      recommendations.push('High variance in response times detected, investigate performance bottlenecks')\n    }\n    \n    const errorTypes = [...new Set(result.errors.map(e => e.type))]\n    if (errorTypes.length > 0) {\n      recommendations.push(`Address common error types: ${errorTypes.join(', ')}`)\n    }\n    \n    return recommendations\n  }\n\n  /**\n   * Obter uso de memória simulado\n   */\n  private getMemoryUsage(): number {\n    // Simular uso de memória crescente durante o teste\n    return Math.random() * 100 + 200 // 200-300MB\n  }\n\n  /**\n   * Verificar se teste está rodando\n   */\n  isTestRunning(): boolean {\n    return this.isRunning\n  }\n\n  /**\n   * Obter resultado do teste atual\n   */\n  getCurrentTestResult(): StressTestResult | null {\n    return this.currentTest\n  }\n\n  /**\n   * Parar teste em execução\n   */\n  stopTest(): void {\n    if (this.isRunning) {\n      this.isRunning = false\n      console.log('🛑 Stress test stopped by user')\n    }\n  }\n}\n\nexport default StressTestRunner"