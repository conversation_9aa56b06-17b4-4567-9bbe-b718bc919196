/* Estilos específicos para componentes de arbitragem */

/* Cards de Oportunidades */
.opportunity-card {
  @apply rounded-lg border p-4 transition-all duration-200 hover:shadow-md;
}

.opportunity-card-high {
  @apply border-green-500 bg-green-50 dark:bg-green-950;
}

.opportunity-card-medium {
  @apply border-yellow-500 bg-yellow-50 dark:bg-yellow-950;
}

.opportunity-card-low {
  @apply border-blue-500 bg-blue-50 dark:bg-blue-950;
}

.opportunity-card-pulse {
  @apply animate-pulse-slow;
}

/* Badges de Exchanges */
.exchange-badge {
  @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium text-white;
}

.exchange-badge-gateio {
  @apply bg-purple-600;
}

.exchange-badge-mexc {
  @apply bg-green-600;
}

.exchange-badge-bitget {
  @apply bg-orange-600;
}

/* Indicadores de Spread */
.spread-indicator {
  @apply inline-flex items-center rounded-md px-2 py-1 text-xs font-medium;
}

.spread-indicator-positive {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
}

/* Status de Conexão */
.connection-status {
  @apply inline-flex items-center gap-1.5 rounded-full px-2.5 py-1 text-xs font-medium;
}

.connection-status-connected {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
}

.connection-status-disconnected {
  @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300;
}

.connection-status-connecting {
  @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300;
}

/* Indicador de Status Online */
.status-dot {
  @apply h-2 w-2 rounded-full;
}

.status-dot-online {
  @apply bg-green-500;
}

.status-dot-offline {
  @apply bg-red-500;
}

.status-dot-connecting {
  @apply bg-yellow-500 animate-pulse;
}

/* Cards de Estatísticas */
.stats-card {
  @apply rounded-lg border bg-card p-6 text-card-foreground shadow-sm;
}

.stats-card-trend-up {
  @apply border-l-4 border-l-green-500;
}

.stats-card-trend-down {
  @apply border-l-4 border-l-red-500;
}

.stats-card-trend-neutral {
  @apply border-l-4 border-l-gray-500;
}

/* Filtros Avançados */
.filter-section {
  @apply space-y-4 rounded-lg border bg-card p-4;
}

.filter-badge {
  @apply inline-flex items-center gap-1 rounded-full bg-primary px-2.5 py-0.5 text-xs font-medium text-primary-foreground;
}

/* Tabela de Posições */
.position-table {
  @apply w-full border-collapse;
}

.position-row {
  @apply border-b transition-colors hover:bg-muted/50;
}

.position-cell {
  @apply p-4 text-left align-middle;
}

.position-pnl-positive {
  @apply text-green-600 dark:text-green-400;
}

.position-pnl-negative {
  @apply text-red-600 dark:text-red-400;
}

/* Gráficos */
.chart-container {
  @apply rounded-lg border bg-card p-4;
}

.chart-tooltip {
  @apply rounded-md border bg-popover p-2 text-popover-foreground shadow-md;
}

/* Sidebar */
.sidebar {
  @apply fixed inset-y-0 left-0 z-50 w-64 transform bg-card transition-transform duration-200 ease-in-out;
}

.sidebar-collapsed {
  @apply -translate-x-full;
}

.sidebar-expanded {
  @apply translate-x-0;
}

.sidebar-overlay {
  @apply fixed inset-0 z-40 bg-black/50 lg:hidden;
}

/* Header */
.header {
  @apply sticky top-0 z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60;
}

/* Notificações */
.notification-toast {
  @apply fixed bottom-4 right-4 z-50 rounded-lg border bg-card p-4 shadow-lg;
}

.notification-success {
  @apply border-green-500 bg-green-50 dark:bg-green-950;
}

.notification-error {
  @apply border-red-500 bg-red-50 dark:bg-red-950;
}

.notification-warning {
  @apply border-yellow-500 bg-yellow-50 dark:bg-yellow-950;
}

.notification-info {
  @apply border-blue-500 bg-blue-50 dark:bg-blue-950;
}

/* Animações customizadas */
@keyframes slideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-slide-out {
  animation: slideOut 0.3s ease-out;
}

/* Responsividade */
@media (max-width: 768px) {
  .opportunity-card {
    @apply p-3;
  }
  
  .stats-card {
    @apply p-4;
  }
  
  .sidebar {
    @apply w-full;
  }
}