/* Variáveis de tema específicas para arbitragem */

:root {
  /* Cores de rentabilidade */
  --profit-high: 142 76% 36%;
  --profit-medium: 45 93% 47%;
  --profit-low: 217 91% 60%;
  
  /* Cores das exchanges */
  --exchange-gateio: 259 100% 65%;
  --exchange-mexc: 142 76% 36%;
  --exchange-bitget: 25 95% 53%;
  
  /* Estados de conexão */
  --status-online: 142 76% 36%;
  --status-offline: 0 84% 60%;
  --status-connecting: 45 93% 47%;
  
  /* Gradientes para gráficos */
  --chart-gradient-start: 142 76% 36%;
  --chart-gradient-end: 142 76% 20%;
  
  /* Sombras customizadas */
  --shadow-opportunity: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-elevated: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.dark {
  /* Ajustes para modo escuro */
  --profit-high: 142 76% 45%;
  --profit-medium: 45 93% 55%;
  --profit-low: 217 91% 70%;
  
  --exchange-gateio: 259 100% 75%;
  --exchange-mexc: 142 76% 45%;
  --exchange-bitget: 25 95% 63%;
  
  --chart-gradient-start: 142 76% 45%;
  --chart-gradient-end: 142 76% 25%;
}

/* Classes utilitárias para temas */
.theme-profit-high {
  color: hsl(var(--profit-high));
}

.theme-profit-medium {
  color: hsl(var(--profit-medium));
}

.theme-profit-low {
  color: hsl(var(--profit-low));
}

.theme-bg-profit-high {
  background-color: hsl(var(--profit-high));
}

.theme-bg-profit-medium {
  background-color: hsl(var(--profit-medium));
}

.theme-bg-profit-low {
  background-color: hsl(var(--profit-low));
}

.theme-exchange-gateio {
  background-color: hsl(var(--exchange-gateio));
}

.theme-exchange-mexc {
  background-color: hsl(var(--exchange-mexc));
}

.theme-exchange-bitget {
  background-color: hsl(var(--exchange-bitget));
}

.theme-status-online {
  color: hsl(var(--status-online));
}

.theme-status-offline {
  color: hsl(var(--status-offline));
}

.theme-status-connecting {
  color: hsl(var(--status-connecting));
}

/* Gradientes para gráficos */
.chart-gradient {
  background: linear-gradient(
    135deg,
    hsl(var(--chart-gradient-start)) 0%,
    hsl(var(--chart-gradient-end)) 100%
  );
}

/* Sombras customizadas */
.shadow-opportunity {
  box-shadow: var(--shadow-opportunity);
}

.shadow-elevated {
  box-shadow: var(--shadow-elevated);
}