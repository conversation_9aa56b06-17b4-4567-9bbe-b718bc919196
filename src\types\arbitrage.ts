// Definições de tipos unificadas para o sistema de arbitragem

// Tipos básicos das exchanges
export type Exchange = 'gateio' | 'mexc' | 'bitget'

export type ContractType = 'PERP' | 'quarterly' | 'monthly'

export type Profitability = 'HIGH' | 'MEDIUM' | 'LOW' | 'NONE'

export type RiskLevel = 'low' | 'medium' | 'high'

export type ArbitrageType = 'spot-futures-cross' | 'futures-futures-cross'

export type PositionStatus = 'open' | 'alert' | 'closed'

export type ConnectionStatus = 'connected' | 'disconnected' | 'connecting'

// Interface principal para oportunidades de arbitragem cross-exchange
export interface ArbitrageOpportunity {
  id: string
  symbol: string              // BTC/USDT, ETH/USDT
  baseAsset: string          // BTC, ETH
  quoteAsset: string         // USDT
  
  // Dados do Spot (Exchange A)
  spotExchange: Exchange     // gateio, mexc, bitget
  spotPrice: number
  spotVolume: number
  spotBid: number
  spotAsk: number
  
  // Dados do Futuros (Exchange B - diferente do spot)
  futuresExchange: Exchange  // Diferente do spotExchange
  futuresPrice: number
  futuresVolume: number
  futuresBid: number
  futuresAsk: number
  contractType: ContractType // PERP, quarterly, monthly
  fundingRate?: number       // Taxa de funding para futuros perpétuos
  
  // Cálculos de Spread Cross-Exchange
  spreadAbsolute: number     // Diferença de preço absoluta
  spreadPercentage: number   // Percentual de diferença
  netSpread: number          // Após taxas e custos
  profitability: Profitability // Classificação
  
  // Estratégia Cross-Exchange
  type: ArbitrageType
  strategy: ArbitrageStrategy
  
  // URLs para Redirecionamento
  urls: {
    spot: string             // URL da exchange spot
    futures: string          // URL da exchange futures
  }
  
  // Métricas Históricas (4h window)
  historicalMetrics: {
    openings: number          // Vezes que spread abriu
    closings: number          // Vezes que spread fechou
    inversions: number        // Vezes que direção inverteu
    averageSpread: number     // Spread médio no período
    maxSpread: number         // Spread máximo
    minSpread: number         // Spread mínimo
  }
  
  // Análise de Risco
  riskAnalysis: {
    riskLevel: RiskLevel
    liquidityScore: number    // Score de liquidez (0-1)
    volatilityScore: number   // Score de volatilidade
    transferCost: number      // Custo estimado de transferência
    executionRisk: number     // Risco de execução (0-1)
  }
  
  // Metadados
  lastUpdate: Date
  dataAge: number           // Idade em milliseconds
  timestamp: Date
  isValid: boolean          // Se passou na validação
}

// Estratégia de arbitragem
export interface ArbitrageStrategy {
  action1: string          // "Buy BTC spot on MEXC"
  action2: string          // "Short BTC futures on Gate.io"
  exitCondition: string    // "When prices converge"
  estimatedProfit: number  // Lucro estimado em USD
  requiredCapital: number  // Capital necessário
  timeHorizon: string      // Horizonte temporal estimado
}

// Dados normalizados de exchange
export interface ExchangeData {
  exchange: Exchange
  symbol: string
  type: 'spot' | 'futures'
  price: number
  volume: number
  bid: number
  ask: number
  timestamp: Date
  contractType?: ContractType
  fundingRate?: number
  
  // Metadados da coleta
  responseTime: number     // Tempo de resposta da API
  dataQuality: number      // Score de qualidade (0-1)
  isStale: boolean         // Se os dados estão desatualizados
}

// Posição de arbitragem cross-exchange
export interface Position {
  id: string
  symbol: string
  
  // Exchanges Cross-Exchange
  spotExchange: Exchange    // Exchange para posição spot
  futuresExchange: Exchange // Exchange para posição futures (diferente)
  
  // Dados de Entrada
  entrySpotPrice: number
  entryFuturesPrice: number
  entrySpread: number
  entryTime: Date
  positionSize: number      // Tamanho da posição
  
  // Dados Atuais Cross-Exchange
  currentSpotPrice: number
  currentFuturesPrice: number
  currentSpread: number
  
  // Cálculos de P&L Cross-Exchange
  unrealizedPnL: number
  unrealizedPnLPercentage: number
  realizedPnL: number
  totalPnL: number
  
  // Sistema de Alertas
  status: PositionStatus
  alertThreshold: number
  alertEnabled: boolean
  lastAlertTime?: Date
  
  // URLs para Fechamento
  urls: {
    spotClose: string       // URL para fechar posição spot
    futuresClose: string    // URL para fechar posição futures
  }
  
  // Metadados
  notes: string
  tags: string[]           // Tags para organização
  createdAt: Date
  updatedAt: Date
}

// Métricas do dashboard
export interface DashboardMetrics {
  // Métricas Gerais
  totalOpportunities: number
  averageSpread: number
  totalVolume: number
  lastUpdateTime: Date
  
  // Distribuição por Tipo Cross-Exchange
  spotFuturesCount: number      // Spot vs Futures cross-exchange
  futuresFuturesCount: number   // Futures vs Futures cross-exchange
  
  // Distribuição por Rentabilidade
  highProfitCount: number       // > 1% spread
  mediumProfitCount: number     // 0.5% - 1% spread
  lowProfitCount: number        // 0.05% - 0.5% spread
  
  // Top Exchange Pairs Cross-Exchange
  topExchangePairs: ExchangePairMetrics[]
  
  // Status das Exchanges
  exchangeStatus: Record<Exchange, ExchangeStatus>
  
  // Métricas de Sistema
  systemMetrics: SystemMetrics
  
  // Métricas de Performance
  performanceMetrics: PerformanceMetrics
}

// Métricas de par de exchanges
export interface ExchangePairMetrics {
  spotExchange: Exchange
  futuresExchange: Exchange
  count: number
  avgSpread: number
  totalVolume: number
  bestOpportunity: ArbitrageOpportunity | null
}

// Status de exchange
export interface ExchangeStatus {
  name: string
  status: ConnectionStatus
  lastUpdate: Date
  responseTime: number
  errorRate: number
  availablePairs: {
    spot: number
    futures: number
  }
  rateLimit: {
    remaining: number
    resetTime: Date
  }
}

// Métricas de sistema
export interface SystemMetrics {
  updateFrequency: number     // Updates por minuto
  errorRate: number          // Taxa de erro
  uptime: number            // Uptime percentual
  responseTime: number      // Tempo médio de resposta
  dataFreshness: number     // Idade média dos dados
  cacheHitRate: number      // Taxa de acerto do cache
  memoryUsage: number       // Uso de memória em MB
  cpuUsage: number          // Uso de CPU em %
}

// Métricas de performance
export interface PerformanceMetrics {
  opportunityDetectionTime: number
  dataProcessingTime: number
  uiRenderTime: number
  cacheHitRate: number
  apiCallsPerMinute: number
  averageLatency: number
  throughput: number        // Oportunidades processadas por segundo
}

// Resultado de cálculo de spread cross-exchange
export interface CrossExchangeSpreadResult {
  rawSpread: number
  spreadPercentage: number
  netSpread: number
  netSpreadPercentage: number
  profitability: Profitability
  strategy: ArbitrageStrategy
  fees: {
    spot: number
    futures: number
    total: number
  }
  estimatedProfit: number
  requiredCapital: number
}

// Configuração de filtros
export interface FilterState {
  searchTerm: string
  spotExchange: Exchange | null
  futuresExchange: Exchange | null
  minSpread: number
  maxSpread: number
  minVolume: number
  maxVolume: number
  profitability: Profitability[]
  arbitrageTypes: ArbitrageType[]
  riskLevels: RiskLevel[]
  timeframe: '1h' | '4h' | '1d'
  showOnlyValid: boolean
  sortBy: 'spread' | 'volume' | 'profit' | 'risk'
  sortOrder: 'asc' | 'desc'
}

// Dados históricos para gráficos
export interface ChartDataPoint {
  timestamp: Date
  spotPrice: number
  futuresPrice: number
  spread: number
  volume: number
  profitability: Profitability
}

export interface ChartData {
  symbol: string
  spotExchange: Exchange
  futuresExchange: Exchange
  timeframe: '1h' | '4h' | '1d'
  data: ChartDataPoint[]
  metrics: {
    totalOpenings: number
    totalClosings: number
    totalInversions: number
    averageSpread: number
    maxSpread: number
    minSpread: number
    volatility: number
  }
}

// Configuração de alerta
export interface AlertConfig {
  id: string
  name: string
  enabled: boolean
  conditions: {
    minSpread?: number
    maxSpread?: number
    exchanges?: Exchange[]
    symbols?: string[]
    profitability?: Profitability[]
  }
  notifications: {
    sound: boolean
    visual: boolean
    push: boolean
    vibration: boolean
  }
  cooldown: number         // Tempo entre alertas em ms
  lastTriggered?: Date
}

// Evento de alerta
export interface AlertEvent {
  id: string
  alertConfigId: string
  opportunity: ArbitrageOpportunity
  timestamp: Date
  acknowledged: boolean
  message: string
  severity: 'low' | 'medium' | 'high'
}

// Configuração de cache
export interface CacheEntry<T = any> {
  value: T
  timestamp: number
  ttl: number
  hits: number
}

// Metadados de coleta de dados
export interface CollectionMetadata {
  totalPairs: number
  processingTime: number
  exchangeStatus: Record<Exchange, ExchangeStatus>
  timestamp: Date
  errors: string[]
  warnings: string[]
}

// Configuração de WebSocket
export interface WebSocketMessage {
  type: 'opportunity_update' | 'system_status' | 'alert' | 'heartbeat'
  data: any
  timestamp: Date
}

// Estado da aplicação
export interface AppState {
  opportunities: ArbitrageOpportunity[]
  positions: Position[]
  filters: FilterState
  alerts: AlertEvent[]
  dashboardMetrics: DashboardMetrics
  connectionStatus: ConnectionStatus
  lastUpdate: Date
  isLoading: boolean
  error: string | null
}

// Tipos para hooks
export interface UseArbitrageDataResult {
  data: ArbitrageOpportunity[]
  metadata: CollectionMetadata | null
  isLoading: boolean
  error: string | null
  refetch: () => void
}

export interface UseChartDataResult {
  data: ChartData | null
  isLoading: boolean
  error: string | null
  refetch: () => void
}

export interface UsePositionsResult {
  positions: Position[]
  addPosition: (position: Omit<Position, 'id' | 'createdAt' | 'updatedAt'>) => void
  updatePosition: (id: string, updates: Partial<Position>) => void
  removePosition: (id: string) => void
  isLoading: boolean
  error: string | null
}

// Tipos para validação
export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// Tipos para auditoria
export interface AuditResult {
  timestamp: Date
  totalChecks: number
  passedChecks: number
  failedChecks: number
  score: number
  details: AuditDetail[]
}

export interface AuditDetail {
  category: string
  check: string
  status: 'pass' | 'fail' | 'warning'
  message: string
  impact: 'low' | 'medium' | 'high'
}