// Utilitários de cálculos para arbitragem

import { EXCHANGE_CONFIG, PROFITABILITY_CONFIG } from '@/config/arbitrage'

export function calculateSpread(spotPrice: number, futuresPrice: number): number {
  return ((futuresPrice - spotPrice) / spotPrice) * 100
}

export function calculateNetSpread(
  spotPrice: number,
  futuresPrice: number,
  spotExchange: string,
  futuresExchange: string
): number {
  const rawSpread = calculateSpread(spotPrice, futuresPrice)
  
  const spotFee = EXCHANGE_CONFIG[spotExchange as keyof typeof EXCHANGE_CONFIG]?.fee || 0.1
  const futuresFee = EXCHANGE_CONFIG[futuresExchange as keyof typeof EXCHANGE_CONFIG]?.fee || 0.1
  
  const totalFees = spotFee + futuresFee
  const netSpread = rawSpread - totalFees
  
  return netSpread
}

export function calculateProfitability(spreadPercentage: number): 'high' | 'medium' | 'low' {
  const absSpread = Math.abs(spreadPercentage)
  
  if (absSpread >= PROFITABILITY_CONFIG.HIGH.threshold) return 'high'
  if (absSpread >= PROFITABILITY_CONFIG.MEDIUM.threshold) return 'medium'
  return 'low'
}

export function calculateLiquidityScore(spotVolume: number, futuresVolume: number): number {
  const minVolume = Math.min(spotVolume, futuresVolume)
  const maxVolume = Math.max(spotVolume, futuresVolume)
  
  if (maxVolume === 0) return 0
  
  // Score baseado na proporção entre volumes e volume absoluto
  const volumeRatio = minVolume / maxVolume
  const volumeScore = Math.min(minVolume / 10000, 1) // Normalizar para $10k
  
  return (volumeRatio * 0.6 + volumeScore * 0.4)
}

export function calculateVolatilityScore(priceHistory: number[]): number {
  if (priceHistory.length < 2) return 0
  
  const returns = []
  for (let i = 1; i < priceHistory.length; i++) {
    returns.push((priceHistory[i] - priceHistory[i - 1]) / priceHistory[i - 1])
  }
  
  const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length
  const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length
  
  return Math.sqrt(variance) * 100 // Converter para percentual
}

export function calculateRiskLevel(
  spreadPercentage: number,
  liquidityScore: number,
  volatilityScore: number
): 'low' | 'medium' | 'high' {
  const absSpread = Math.abs(spreadPercentage)
  
  // Spread muito alto pode indicar risco
  const spreadRisk = absSpread > 5 ? 0.8 : absSpread > 2 ? 0.5 : 0.2
  
  // Baixa liquidez aumenta risco
  const liquidityRisk = liquidityScore < 0.3 ? 0.8 : liquidityScore < 0.6 ? 0.5 : 0.2
  
  // Alta volatilidade aumenta risco
  const volatilityRisk = volatilityScore > 10 ? 0.8 : volatilityScore > 5 ? 0.5 : 0.2
  
  const totalRisk = (spreadRisk + liquidityRisk + volatilityRisk) / 3
  
  if (totalRisk > 0.6) return 'high'
  if (totalRisk > 0.4) return 'medium'
  return 'low'
}

export function calculatePositionPnL(
  entrySpotPrice: number,
  entryFuturesPrice: number,
  currentSpotPrice: number,
  currentFuturesPrice: number,
  positionSize: number = 1
): {
  unrealizedPnL: number
  unrealizedPnLPercentage: number
  currentSpread: number
  entrySpread: number
} {
  const entrySpread = calculateSpread(entrySpotPrice, entryFuturesPrice)
  const currentSpread = calculateSpread(currentSpotPrice, currentFuturesPrice)
  
  // P&L baseado na convergência do spread
  const spreadDifference = currentSpread - entrySpread
  const unrealizedPnL = (spreadDifference / 100) * entrySpotPrice * positionSize
  const unrealizedPnLPercentage = (unrealizedPnL / (entrySpotPrice * positionSize)) * 100
  
  return {
    unrealizedPnL,
    unrealizedPnLPercentage,
    currentSpread,
    entrySpread,
  }
}

export function calculateOptimalPositionSize(
  accountBalance: number,
  riskPercentage: number,
  _spreadPercentage: number,
  stopLossPercentage: number = 2
): number {
  const riskAmount = accountBalance * (riskPercentage / 100)
  const positionSize = riskAmount / (stopLossPercentage / 100)
  
  return Math.min(positionSize, accountBalance * 0.1) // Máximo 10% do saldo
}

// Funções de formatação
export function formatCurrency(value: number): string {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
  return formatter.format(value)
}

export function formatPercentage(value: number, decimals: number = 2): string {
  return `${(value * 100).toFixed(decimals)}%`
}

// Funções de volume
export function calculateVolume(volume1: number, volume2: number): number {
  return Math.min(volume1, volume2)
}

// Função de risco
export function calculateRisk(spread: number, volume: number, volatility: number): number {
  if (spread === 0 && volume === 0 && volatility === 0) return 0
  
  // Normalizar valores para score de 0-100
  const spreadScore = Math.min(Math.abs(spread) * 10, 50)
  const volumeScore = Math.max(0, 50 - (volume / 10000))
  const volatilityScore = Math.min(volatility * 100, 50)
  
  return Math.min(spreadScore + volumeScore + volatilityScore, 100)
}