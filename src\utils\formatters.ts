// Utilitários de formatação para dados de arbitragem

export function formatCurrency(value: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 6,
  }).format(value)
}

export function formatPercentage(value: number, decimals: number = 2): string {
  return `${value.toFixed(decimals)}%`
}

export function formatNumber(value: number, decimals: number = 2): string {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value)
}

export function formatTimestamp(timestamp: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  }).format(timestamp)
}

export function formatVolume(volume: number): string {
  if (volume >= 1000000) {
    return `${(volume / 1000000).toFixed(1)}M`
  }
  if (volume >= 1000) {
    return `${(volume / 1000).toFixed(1)}K`
  }
  return volume.toFixed(0)
}

export function formatSpread(spread: number): string {
  const sign = spread >= 0 ? '+' : ''
  return `${sign}${formatPercentage(spread, 3)}`
}

export function formatExchangeName(exchange: string): string {
  switch (exchange.toLowerCase()) {
    case 'gateio':
      return 'Gate.io'
    case 'mexc':
      return 'MEXC'
    case 'bitget':
      return 'Bitget'
    default:
      return exchange.charAt(0).toUpperCase() + exchange.slice(1)
  }
}

export function formatDataAge(ageMs: number): string {
  const seconds = Math.floor(ageMs / 1000)
  if (seconds < 60) {
    return `${seconds}s ago`
  }
  const minutes = Math.floor(seconds / 60)
  return `${minutes}m ago`
}