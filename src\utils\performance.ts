// Utilitários de performance e otimização

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

export function measurePerformance<T>(
  name: string,
  fn: () => T
): T {
  const start = performance.now()
  const result = fn()
  const end = performance.now()
  
  console.log(`[Performance] ${name}: ${(end - start).toFixed(2)}ms`)
  return result
}

export async function measureAsyncPerformance<T>(
  name: string,
  fn: () => Promise<T>
): Promise<T> {
  const start = performance.now()
  const result = await fn()
  const end = performance.now()
  
  console.log(`[Performance] ${name}: ${(end - start).toFixed(2)}ms`)
  return result
}

export function createCache<K, V>(maxSize: number = 1000) {
  const cache = new Map<K, { value: V; timestamp: number }>()
  
  return {
    get(key: K, ttl: number = 5000): V | undefined {
      const entry = cache.get(key)
      if (!entry) return undefined
      
      if (Date.now() - entry.timestamp > ttl) {
        cache.delete(key)
        return undefined
      }
      
      return entry.value
    },
    
    set(key: K, value: V): void {
      if (cache.size >= maxSize) {
        const firstKey = cache.keys().next().value as K
        if (firstKey !== undefined) {
          cache.delete(firstKey)
        }
      }
      
      cache.set(key, { value, timestamp: Date.now() })
    },
    
    clear(): void {
      cache.clear()
    },
    
    size(): number {
      return cache.size
    }
  }
}

export function batchRequests<T, R>(
  items: T[],
  processor: (batch: T[]) => Promise<R[]>,
  batchSize: number = 10,
  delay: number = 100
): Promise<R[]> {
  return new Promise(async (resolve, reject) => {
    try {
      const results: R[] = []
      
      for (let i = 0; i < items.length; i += batchSize) {
        const batch = items.slice(i, i + batchSize)
        const batchResults = await processor(batch)
        results.push(...batchResults)
        
        // Delay entre batches para evitar rate limiting
        if (i + batchSize < items.length) {
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
      
      resolve(results)
    } catch (error) {
      reject(error)
    }
  })
}

export function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
  maxDelay: number = 30000
): Promise<T> {
  return new Promise(async (resolve, reject) => {
    let lastError: Error
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const result = await fn()
        resolve(result)
        return
      } catch (error) {
        lastError = error as Error
        
        if (attempt === maxRetries) {
          reject(lastError)
          return
        }
        
        // Exponential backoff with jitter
        const delay = Math.min(
          baseDelay * Math.pow(2, attempt) + Math.random() * 1000,
          maxDelay
        )
        
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
  })
}

export function createRateLimiter(requestsPerSecond: number) {
  const tokens = requestsPerSecond
  let availableTokens = tokens
  let lastRefill = Date.now()
  
  return {
    async acquire(): Promise<void> {
      const now = Date.now()
      const timePassed = now - lastRefill
      
      // Refill tokens based on time passed
      availableTokens = Math.min(
        tokens,
        availableTokens + (timePassed / 1000) * requestsPerSecond
      )
      lastRefill = now
      
      if (availableTokens >= 1) {
        availableTokens -= 1
        return
      }
      
      // Wait until we can get a token
      const waitTime = (1 - availableTokens) / requestsPerSecond * 1000
      await new Promise(resolve => setTimeout(resolve, waitTime))
      availableTokens = 0
    }
  }
}

// Função para medir tempo de execução
export function measureExecutionTime<T>(fn: () => T): { result: T; executionTime: number }
export function measureExecutionTime<T>(fn: () => Promise<T>): Promise<{ result: T; executionTime: number }>
export function measureExecutionTime<T>(fn: () => T | Promise<T>): { result: T; executionTime: number } | Promise<{ result: T; executionTime: number }> {
  const start = performance.now()
  const result = fn()
  
  if (result instanceof Promise) {
    return result.then(res => ({
      result: res,
      executionTime: performance.now() - start
    }))
  }
  
  return {
    result,
    executionTime: performance.now() - start
  }
}

// Função de memoização
export function memoize<T extends (...args: any[]) => any>(fn: T): T {
  const cache = new Map()
  
  return ((...args: Parameters<T>) => {
    const key = JSON.stringify(args)
    if (cache.has(key)) {
      return cache.get(key)
    }
    
    const result = fn(...args)
    cache.set(key, result)
    return result
  }) as T
}

// Função para criar cache simples
export function createSimpleCache<K, V>(maxSize: number = 100) {
  const cache = new Map<K, V>()
  
  return {
    get(key: K): V | undefined {
      return cache.get(key)
    },
    
    set(key: K, value: V): void {
      if (cache.size >= maxSize) {
        const firstKey = cache.keys().next().value
        cache.delete(firstKey)
      }
      cache.set(key, value)
    },
    
    has(key: K): boolean {
      return cache.has(key)
    },
    
    size(): number {
      return cache.size
    },
    
    clear(): void {
      cache.clear()
    }
  }
}

// Função para formatar duração
export function formatDuration(ms: number): string {
  if (ms < 1000) {
    return `${ms}ms`
  }
  
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) {
    const remainingHours = hours % 24
    const remainingMinutes = minutes % 60
    const remainingSeconds = seconds % 60
    return `${days}d ${remainingHours}h ${remainingMinutes}m ${remainingSeconds}s`
  }
  
  if (hours > 0) {
    const remainingMinutes = minutes % 60
    const remainingSeconds = seconds % 60
    return `${hours}h ${remainingMinutes}m ${remainingSeconds}s`
  }
  
  if (minutes > 0) {
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }
  
  if (seconds >= 1) {
    const decimal = (ms % 1000) / 1000
    return `${seconds + decimal}s`
  }
  
  return `${ms}ms`
}