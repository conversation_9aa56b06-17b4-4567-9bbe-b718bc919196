// Utilitários de validação para dados de arbitragem

import { VALIDATION_THRESHOLDS } from '@/config/arbitrage'

export function isValidSpread(spreadPercentage: number): boolean {
  return Math.abs(spreadPercentage) >= VALIDATION_THRESHOLDS.MIN_SPREAD_PERCENTAGE
}

export function isValidVolume(volume: number): boolean {
  return volume >= VALIDATION_THRESHOLDS.MIN_VOLUME_USD
}

export function isValidDataAge(dataAge: number): boolean {
  return dataAge <= VALIDATION_THRESHOLDS.MAX_DATA_AGE_MS
}

export function isValidCrossExchange(spotExchange: string, futuresExchange: string): boolean {
  return spotExchange !== futuresExchange
}

export function isValidPrice(price: number): boolean {
  return price > 0 && isFinite(price)
}

export function isValidSymbol(symbol: string): boolean {
  return Boolean(symbol && symbol.length > 0 && symbol.includes('/'))
}

export function validateArbitrageOpportunity(opportunity: {
  spotExchange: string
  futuresExchange: string
  spreadPercentage: number
  volume: number
  dataAge: number
  spotPrice: number
  futuresPrice: number
  symbol: string
}): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!isValidCrossExchange(opportunity.spotExchange, opportunity.futuresExchange)) {
    errors.push('Must be cross-exchange (different exchanges)')
  }

  if (!isValidSpread(opportunity.spreadPercentage)) {
    errors.push(`Spread must be >= ${VALIDATION_THRESHOLDS.MIN_SPREAD_PERCENTAGE}%`)
  }

  if (!isValidVolume(opportunity.volume)) {
    errors.push(`Volume must be >= $${VALIDATION_THRESHOLDS.MIN_VOLUME_USD}`)
  }

  if (!isValidDataAge(opportunity.dataAge)) {
    errors.push(`Data must be fresher than ${VALIDATION_THRESHOLDS.MAX_DATA_AGE_MS / 1000}s`)
  }

  if (!isValidPrice(opportunity.spotPrice)) {
    errors.push('Invalid spot price')
  }

  if (!isValidPrice(opportunity.futuresPrice)) {
    errors.push('Invalid futures price')
  }

  if (!isValidSymbol(opportunity.symbol)) {
    errors.push('Invalid symbol format')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}