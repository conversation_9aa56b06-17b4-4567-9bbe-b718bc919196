<!DOCTYPE html>
<html>
<head>
    <title>Teste <PERSON> Backend</title>
</head>
<body>
    <h1>Teste de Conexão com Backend</h1>
    <button onclick="testConnection()">Testar Conexão</button>
    <div id="result"></div>

    <script>
        async function testConnection() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testando...';
            
            try {
                console.log('Fazendo requisição para http://localhost:3003/api/arbitrage/opportunities');
                
                const response = await fetch('http://localhost:3003/api/arbitrage/opportunities', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('Response data:', data);
                
                resultDiv.innerHTML = `
                    <h2>✅ Sucesso!</h2>
                    <p>Status: ${response.status}</p>
                    <p>Oportunidades: ${data.opportunities ? data.opportunities.length : 'N/A'}</p>
                    <p>Count: ${data.count || 'N/A'}</p>
                    <pre>${JSON.stringify(data, null, 2).substring(0, 1000)}...</pre>
                `;
                
            } catch (error) {
                console.error('Erro:', error);
                resultDiv.innerHTML = `
                    <h2>❌ Erro!</h2>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>