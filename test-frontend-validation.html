<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Validação Frontend</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .loading { color: #666; font-style: italic; }
    </style>
</head>
<body>
    <h1>🧪 Teste de Validação Frontend vs Backend</h1>
    <div id="results"></div>

    <script>
        async function testFrontendValidation() {
            const resultsDiv = document.getElementById('results');
            
            try {
                resultsDiv.innerHTML = '<div class="loading">🔄 Testando conexão com backend...</div>';
                
                // 1. Testar backend diretamente
                console.log('🔍 Testando backend...');
                const backendResponse = await fetch('http://localhost:3003/api/validation/opportunities/0.3');
                const backendData = await backendResponse.json();
                
                resultsDiv.innerHTML += `
                    <div class="result success">
                        <h3>✅ Backend Funcionando</h3>
                        <p><strong>Total de oportunidades:</strong> ${backendData.analysis.total}</p>
                        <p><strong>Acima de 0.3%:</strong> ${backendData.analysis.aboveThreshold}</p>
                        <p><strong>Percentual:</strong> ${backendData.analysis.percentage}</p>
                    </div>
                `;
                
                // 2. Testar endpoint de oportunidades
                console.log('🔍 Testando endpoint de oportunidades...');
                const opportunitiesResponse = await fetch('http://localhost:3003/api/arbitrage/opportunities');
                const opportunitiesData = await opportunitiesResponse.json();
                
                const filteredOpportunities = opportunitiesData.opportunities.filter(opp => 
                    Math.abs(opp.spreadPercentage) > 0.3
                );
                
                resultsDiv.innerHTML += `
                    <div class="result info">
                        <h3>📊 Endpoint de Oportunidades</h3>
                        <p><strong>Total retornado:</strong> ${opportunitiesData.opportunities.length}</p>
                        <p><strong>Filtrado > 0.3%:</strong> ${filteredOpportunities.length}</p>
                        <p><strong>Status:</strong> ${opportunitiesData.success ? 'Sucesso' : 'Erro'}</p>
                    </div>
                `;
                
                // 3. Análise detalhada
                const byType = filteredOpportunities.reduce((acc, opp) => {
                    acc[opp.type] = (acc[opp.type] || 0) + 1;
                    return acc;
                }, {});
                
                const byExchange = filteredOpportunities.reduce((acc, opp) => {
                    const key = `${opp.spotExchange}-${opp.futuresExchange}`;
                    acc[key] = (acc[key] || 0) + 1;
                    return acc;
                }, {});
                
                resultsDiv.innerHTML += `
                    <div class="result info">
                        <h3>📈 Análise Detalhada</h3>
                        <h4>Por Tipo:</h4>
                        <ul>
                            ${Object.entries(byType).map(([type, count]) => 
                                `<li>${type}: ${count}</li>`
                            ).join('')}
                        </ul>
                        <h4>Top 5 Exchanges:</h4>
                        <ul>
                            ${Object.entries(byExchange)
                                .sort(([,a], [,b]) => b - a)
                                .slice(0, 5)
                                .map(([exchange, count]) => 
                                    `<li>${exchange}: ${count}</li>`
                                ).join('')}
                        </ul>
                    </div>
                `;
                
                // 4. Top oportunidades
                const topOpportunities = filteredOpportunities
                    .sort((a, b) => Math.abs(b.spreadPercentage) - Math.abs(a.spreadPercentage))
                    .slice(0, 10);
                
                resultsDiv.innerHTML += `
                    <div class="result success">
                        <h3>🏆 Top 10 Oportunidades > 0.3%</h3>
                        <table>
                            <thead>
                                <tr>
                                    <th>Símbolo</th>
                                    <th>Tipo</th>
                                    <th>Spot Exchange</th>
                                    <th>Futures Exchange</th>
                                    <th>Spread %</th>
                                    <th>Volume</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${topOpportunities.map(opp => `
                                    <tr>
                                        <td>${opp.symbol}</td>
                                        <td>${opp.type}</td>
                                        <td>${opp.spotExchange}</td>
                                        <td>${opp.futuresExchange}</td>
                                        <td>${Math.abs(opp.spreadPercentage).toFixed(3)}%</td>
                                        <td>${Math.round(opp.volume).toLocaleString()}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                `;
                
                // 5. Validação final
                const isValid = filteredOpportunities.length >= 100; // Esperamos pelo menos 100 oportunidades > 0.3%
                
                resultsDiv.innerHTML += `
                    <div class="result ${isValid ? 'success' : 'error'}">
                        <h3>${isValid ? '✅' : '❌'} Validação Final</h3>
                        <p><strong>Status:</strong> ${isValid ? 'APROVADO' : 'REPROVADO'}</p>
                        <p><strong>Critério:</strong> Pelo menos 100 oportunidades > 0.3%</p>
                        <p><strong>Resultado:</strong> ${filteredOpportunities.length} oportunidades encontradas</p>
                        ${isValid ? 
                            '<p>🎉 O sistema está funcionando corretamente!</p>' : 
                            '<p>⚠️ Possível problema na filtragem ou coleta de dados.</p>'
                        }
                    </div>
                `;
                
            } catch (error) {
                console.error('Erro no teste:', error);
                resultsDiv.innerHTML += `
                    <div class="result error">
                        <h3>❌ Erro no Teste</h3>
                        <p><strong>Erro:</strong> ${error.message}</p>
                        <p><strong>Possível causa:</strong> Backend não está rodando ou há problema de CORS</p>
                    </div>
                `;
            }
        }
        
        // Executar teste automaticamente
        testFrontendValidation();
        
        // Atualizar a cada 30 segundos
        setInterval(testFrontendValidation, 30000);
    </script>
</body>
</html>