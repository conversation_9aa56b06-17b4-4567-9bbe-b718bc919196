// Teste de localização
console.log('Locale info:');
console.log('process.env.LANG:', process.env.LANG);
console.log('process.env.LC_ALL:', process.env.LC_ALL);
console.log('process.env.LC_NUMERIC:', process.env.LC_NUMERIC);

// Teste parseFloat
const testValue = "0.0010952";
console.log(`parseFloat("${testValue}") =`, parseFloat(testValue));

// Teste com Number
console.log(`Number("${testValue}") =`, Number(testValue));

// Teste com +
console.log(`+("${testValue}") =`, +(testValue));

// Teste formatação
const num = 0.0010952;
console.log('num.toString():', num.toString());
console.log('num.toFixed(8):', num.toFixed(8));

// Teste com dados reais
fetch('https://api.gateio.ws/api/v4/futures/usdt/tickers')
  .then(response => response.json())
  .then(data => {
    const neiro = data.find(ticker => ticker.contract === 'NEIRO_USDT');
    if (neiro) {
      console.log('\nGate.io NEIRO ticker:');
      console.log('contract:', neiro.contract);
      console.log('last (raw):', neiro.last);
      console.log('last (type):', typeof neiro.last);
      console.log('parseFloat(last):', parseFloat(neiro.last));
      console.log('Number(last):', Number(neiro.last));
    }
  })
  .catch(error => console.error('Erro:', error));