<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug MDT - Frontend vs Backend</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .result { margin: 10px 0; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .error { background-color: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
        .info { background-color: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
        .warning { background-color: #fff3cd; color: #856404; border-left: 4px solid #ffc107; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; background: white; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .loading { color: #666; font-style: italic; }
        .highlight { background-color: #fff3cd; font-weight: bold; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; background: white; }
        .step-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; color: #333; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug MDT - Rastreamento Completo</h1>
        <div id="results"></div>
    </div>

    <script>
        async function debugMDT() {
            const resultsDiv = document.getElementById('results');
            
            try {
                resultsDiv.innerHTML = '<div class="loading">🔄 Iniciando debug da MDT...</div>';
                
                // STEP 1: Verificar backend
                console.log('🔍 STEP 1: Verificando backend...');
                const backendResponse = await fetch('http://localhost:3003/api/arbitrage/opportunities');
                const backendData = await backendResponse.json();
                
                const mdtBackend = backendData.opportunities.filter(opp => 
                    opp.symbol === 'MDT/USDT'
                );
                
                resultsDiv.innerHTML = `
                    <div class="step">
                        <div class="step-title">📊 STEP 1: Backend Analysis</div>
                        <div class="result ${mdtBackend.length > 0 ? 'success' : 'error'}">
                            <h3>${mdtBackend.length > 0 ? '✅' : '❌'} MDT no Backend</h3>
                            <p><strong>Total de oportunidades:</strong> ${backendData.opportunities.length}</p>
                            <p><strong>MDT encontradas:</strong> ${mdtBackend.length}</p>
                            <p><strong>Status:</strong> ${backendData.success ? 'Sucesso' : 'Erro'}</p>
                        </div>
                        ${mdtBackend.length > 0 ? `
                            <table>
                                <thead>
                                    <tr>
                                        <th>Símbolo</th>
                                        <th>Tipo</th>
                                        <th>Spot Exchange</th>
                                        <th>Futures Exchange</th>
                                        <th>Spread %</th>
                                        <th>Volume</th>
                                        <th>Timestamp</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${mdtBackend.map(opp => `
                                        <tr class="highlight">
                                            <td>${opp.symbol}</td>
                                            <td>${opp.type}</td>
                                            <td>${opp.spotExchange}</td>
                                            <td>${opp.futuresExchange}</td>
                                            <td>${Math.abs(opp.spreadPercentage).toFixed(3)}%</td>
                                            <td>${Math.round(opp.volume).toLocaleString()}</td>
                                            <td>${new Date(opp.timestamp).toLocaleTimeString()}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        ` : ''}
                    </div>
                `;
                
                // STEP 2: Simular adaptação do ExchangeAPI
                console.log('🔍 STEP 2: Simulando adaptação do ExchangeAPI...');
                
                const adaptedMDT = mdtBackend.map(backendOpp => {
                    const now = new Date();
                    const timestamp = new Date(backendOpp.timestamp || Date.now());
                    const dataAge = backendOpp.dataAge || 0;

                    const baseId = `${backendOpp.symbol}-${backendOpp.spotExchange}-${backendOpp.futuresExchange}`;
                    const uniqueHash = Math.abs(
                        (backendOpp.spotPrice || 0) * 1000 + 
                        (backendOpp.futuresPrice || 0) * 1000 + 
                        (backendOpp.spreadPercentage || 0) * 10000
                    ).toString(36).slice(0, 6);
                    const timestampSuffix = timestamp.getTime().toString(36).slice(-4);

                    return {
                        id: `${baseId}-${uniqueHash}-${timestampSuffix}`,
                        symbol: backendOpp.symbol,
                        baseAsset: backendOpp.symbol.split('/')[0] || 'MDT',
                        quoteAsset: backendOpp.symbol.split('/')[1] || 'USDT',
                        
                        spotExchange: backendOpp.spotExchange,
                        spotPrice: backendOpp.spotPrice,
                        spotVolume: backendOpp.volume || 0,
                        
                        futuresExchange: backendOpp.futuresExchange,
                        futuresPrice: backendOpp.futuresPrice,
                        futuresVolume: backendOpp.volume || 0,
                        
                        spreadPercentage: backendOpp.spreadPercentage,
                        spreadAbsolute: backendOpp.spread,
                        
                        type: backendOpp.type === 'spot-futures' ? 'spot-futures-cross' : 'futures-futures-cross',
                        
                        profitability: Math.abs(backendOpp.spreadPercentage) >= 5.0 ? 'high' :
                                      Math.abs(backendOpp.spreadPercentage) >= 1.0 ? 'medium' : 'low',
                        
                        lastUpdate: timestamp,
                        dataAge: dataAge,
                        timestamp: timestamp,
                        isValid: Math.abs(backendOpp.spreadPercentage) > 0.05 && dataAge < 30000
                    };
                });
                
                resultsDiv.innerHTML += `
                    <div class="step">
                        <div class="step-title">🔄 STEP 2: ExchangeAPI Adaptation</div>
                        <div class="result ${adaptedMDT.length > 0 ? 'success' : 'error'}">
                            <h3>${adaptedMDT.length > 0 ? '✅' : '❌'} MDT Adaptada</h3>
                            <p><strong>MDT adaptadas:</strong> ${adaptedMDT.length}</p>
                            <p><strong>Processo:</strong> Backend → Frontend format</p>
                        </div>
                        ${adaptedMDT.length > 0 ? `
                            <div class="code">
                                <strong>Exemplo de MDT adaptada:</strong><br>
                                ${JSON.stringify(adaptedMDT[0], null, 2)}
                            </div>
                        ` : ''}
                    </div>
                `;
                
                // STEP 3: Simular filtros
                console.log('🔍 STEP 3: Simulando filtros...');
                
                // Simular filtros padrão
                const DEFAULT_FILTERS = {
                    searchTerm: '',
                    spotExchange: 'all',
                    futuresExchange: 'all',
                    type: 'all',
                    profitability: 'all',
                    spreadRange: [0, 100],
                    volumeRange: [0, 10000000],
                    priceRange: [0, 1000000]
                };
                
                let filteredMDT = [...adaptedMDT];
                
                // Aplicar filtros
                filteredMDT = filteredMDT.filter(opp => {
                    const absSpread = Math.abs(opp.spreadPercentage);
                    const minVolume = Math.min(opp.spotVolume, opp.futuresVolume);
                    const avgPrice = (opp.spotPrice + opp.futuresPrice) / 2;
                    
                    return absSpread >= DEFAULT_FILTERS.spreadRange[0] && 
                           absSpread <= DEFAULT_FILTERS.spreadRange[1] &&
                           minVolume >= DEFAULT_FILTERS.volumeRange[0] && 
                           minVolume <= DEFAULT_FILTERS.volumeRange[1] &&
                           avgPrice >= DEFAULT_FILTERS.priceRange[0] && 
                           avgPrice <= DEFAULT_FILTERS.priceRange[1];
                });
                
                resultsDiv.innerHTML += `
                    <div class="step">
                        <div class="step-title">🔍 STEP 3: Filter Application</div>
                        <div class="result ${filteredMDT.length > 0 ? 'success' : 'error'}">
                            <h3>${filteredMDT.length > 0 ? '✅' : '❌'} MDT Após Filtros</h3>
                            <p><strong>Antes dos filtros:</strong> ${adaptedMDT.length}</p>
                            <p><strong>Após filtros:</strong> ${filteredMDT.length}</p>
                            <p><strong>Filtros aplicados:</strong></p>
                            <ul>
                                <li>Spread Range: ${DEFAULT_FILTERS.spreadRange[0]}% - ${DEFAULT_FILTERS.spreadRange[1]}%</li>
                                <li>Volume Range: ${DEFAULT_FILTERS.volumeRange[0].toLocaleString()} - ${DEFAULT_FILTERS.volumeRange[1].toLocaleString()}</li>
                                <li>Price Range: $${DEFAULT_FILTERS.priceRange[0]} - $${DEFAULT_FILTERS.priceRange[1].toLocaleString()}</li>
                            </ul>
                        </div>
                        ${filteredMDT.length > 0 ? `
                            <table>
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Símbolo</th>
                                        <th>Tipo</th>
                                        <th>Spread %</th>
                                        <th>Volume</th>
                                        <th>Preço Médio</th>
                                        <th>Válida</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${filteredMDT.map(opp => `
                                        <tr class="highlight">
                                            <td>${opp.id}</td>
                                            <td>${opp.symbol}</td>
                                            <td>${opp.type}</td>
                                            <td>${Math.abs(opp.spreadPercentage).toFixed(3)}%</td>
                                            <td>${Math.round(Math.min(opp.spotVolume, opp.futuresVolume)).toLocaleString()}</td>
                                            <td>$${((opp.spotPrice + opp.futuresPrice) / 2).toFixed(4)}</td>
                                            <td>${opp.isValid ? '✅' : '❌'}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        ` : `
                            <div class="result error">
                                <h4>❌ Possíveis Problemas:</h4>
                                <ul>
                                    ${adaptedMDT.length > 0 ? adaptedMDT.map(opp => {
                                        const issues = [];
                                        const absSpread = Math.abs(opp.spreadPercentage);
                                        const minVolume = Math.min(opp.spotVolume, opp.futuresVolume);
                                        const avgPrice = (opp.spotPrice + opp.futuresPrice) / 2;
                                        
                                        if (absSpread < DEFAULT_FILTERS.spreadRange[0] || absSpread > DEFAULT_FILTERS.spreadRange[1]) {
                                            issues.push(`Spread ${absSpread.toFixed(3)}% fora do range ${DEFAULT_FILTERS.spreadRange[0]}-${DEFAULT_FILTERS.spreadRange[1]}%`);
                                        }
                                        if (minVolume < DEFAULT_FILTERS.volumeRange[0] || minVolume > DEFAULT_FILTERS.volumeRange[1]) {
                                            issues.push(`Volume ${minVolume.toLocaleString()} fora do range`);
                                        }
                                        if (avgPrice < DEFAULT_FILTERS.priceRange[0] || avgPrice > DEFAULT_FILTERS.priceRange[1]) {
                                            issues.push(`Preço $${avgPrice.toFixed(4)} fora do range`);
                                        }
                                        if (!opp.isValid) {
                                            issues.push(`Oportunidade marcada como inválida`);
                                        }
                                        
                                        return `<li><strong>${opp.symbol}:</strong> ${issues.join(', ')}</li>`;
                                    }).join('') : '<li>Nenhuma MDT foi adaptada do backend</li>'}
                                </ul>
                            </div>
                        `}
                    </div>
                `;
                
                // STEP 4: Diagnóstico final
                resultsDiv.innerHTML += `
                    <div class="step">
                        <div class="step-title">🎯 STEP 4: Diagnóstico Final</div>
                        <div class="result ${filteredMDT.length > 0 ? 'success' : 'error'}">
                            <h3>📋 Resumo do Pipeline MDT</h3>
                            <table>
                                <tr>
                                    <td><strong>Backend</strong></td>
                                    <td>${mdtBackend.length} MDT encontradas</td>
                                    <td>${mdtBackend.length > 0 ? '✅' : '❌'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Adaptação</strong></td>
                                    <td>${adaptedMDT.length} MDT adaptadas</td>
                                    <td>${adaptedMDT.length > 0 ? '✅' : '❌'}</td>
                                </tr>
                                <tr>
                                    <td><strong>Filtros</strong></td>
                                    <td>${filteredMDT.length} MDT filtradas</td>
                                    <td>${filteredMDT.length > 0 ? '✅' : '❌'}</td>
                                </tr>
                                <tr class="highlight">
                                    <td><strong>Frontend Final</strong></td>
                                    <td>${filteredMDT.length} MDT para exibição</td>
                                    <td>${filteredMDT.length > 0 ? '✅ DEVE APARECER' : '❌ NÃO APARECE'}</td>
                                </tr>
                            </table>
                            
                            ${filteredMDT.length === 0 && mdtBackend.length > 0 ? `
                                <div class="result error">
                                    <h4>🚨 PROBLEMA IDENTIFICADO:</h4>
                                    <p>A MDT existe no backend mas está sendo filtrada antes de chegar ao frontend!</p>
                                    <p><strong>Ação necessária:</strong> Verificar filtros no useFilters ou ExchangeAPI</p>
                                </div>
                            ` : filteredMDT.length > 0 ? `
                                <div class="result success">
                                    <h4>✅ PIPELINE OK:</h4>
                                    <p>A MDT deveria estar aparecendo no frontend!</p>
                                    <p><strong>Se não está aparecendo:</strong> Verificar componente OpportunityTable ou OpportunityCard</p>
                                </div>
                            ` : `
                                <div class="result error">
                                    <h4>❌ MDT NÃO ENCONTRADA:</h4>
                                    <p>A MDT não está sendo retornada pelo backend</p>
                                    <p><strong>Ação necessária:</strong> Verificar coleta de dados das exchanges</p>
                                </div>
                            `}
                        </div>
                    </div>
                `;
                
            } catch (error) {
                console.error('Erro no debug:', error);
                resultsDiv.innerHTML += `
                    <div class="result error">
                        <h3>❌ Erro no Debug</h3>
                        <p><strong>Erro:</strong> ${error.message}</p>
                        <p><strong>Stack:</strong> ${error.stack}</p>
                    </div>
                `;
            }
        }
        
        // Executar debug automaticamente
        debugMDT();
        
        // Atualizar a cada 30 segundos
        setInterval(debugMDT, 30000);
    </script>
</body>
</html>