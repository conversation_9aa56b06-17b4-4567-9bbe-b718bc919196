<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Final MDT</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; }
        .result { margin: 10px 0; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .error { background-color: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
        .info { background-color: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
        .warning { background-color: #fff3cd; color: #856404; border-left: 4px solid #ffc107; }
        .highlight { background-color: #fff3cd; font-weight: bold; padding: 10px; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; background: white; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Teste Final MDT - Diagnóstico Completo</h1>
        <div id="results"></div>
        <button onclick="testMDT()" style="margin: 20px 0; padding: 10px 20px; font-size: 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">🔄 Executar Teste</button>
    </div>

    <script>
        async function testMDT() {
            const resultsDiv = document.getElementById('results');
            
            try {
                resultsDiv.innerHTML = '<div class="info">🔄 Testando MDT com diagnóstico completo...</div>';
                
                // 1. Buscar dados do backend
                const response = await fetch('http://localhost:3003/api/arbitrage/opportunities');
                const data = await response.json();
                
                const mdtBackend = data.opportunities.filter(opp => opp.symbol === 'MDT/USDT');
                
                resultsDiv.innerHTML = `
                    <div class="result ${mdtBackend.length > 0 ? 'success' : 'error'}">
                        <h3>${mdtBackend.length > 0 ? '✅' : '❌'} Backend Status</h3>
                        <p><strong>Total oportunidades:</strong> ${data.opportunities.length}</p>
                        <p><strong>MDT encontradas:</strong> ${mdtBackend.length}</p>
                    </div>
                `;
                
                if (mdtBackend.length > 0) {
                    const mdt = mdtBackend[0];
                    
                    // 2. Simular adaptação ExchangeAPI
                    const dataAge = mdt.dataAge || 0;
                    const adaptedMDT = {
                        id: `${mdt.symbol}-${mdt.spotExchange}-${mdt.futuresExchange}-test`,
                        symbol: mdt.symbol,
                        baseAsset: 'MDT',
                        quoteAsset: 'USDT',
                        spotExchange: mdt.spotExchange,
                        spotPrice: mdt.spotPrice,
                        spotVolume: mdt.volume,
                        futuresExchange: mdt.futuresExchange,
                        futuresPrice: mdt.futuresPrice,
                        futuresVolume: mdt.volume,
                        spreadPercentage: mdt.spreadPercentage,
                        type: mdt.type === 'spot-futures' ? 'spot-futures-cross' : 'futures-futures-cross',
                        profitability: Math.abs(mdt.spreadPercentage) >= 5.0 ? 'high' : 
                                      Math.abs(mdt.spreadPercentage) >= 1.0 ? 'medium' : 'low',
                        dataAge: dataAge,
                        isValid: Math.abs(mdt.spreadPercentage) > 0.05 && dataAge < 300000 // 5 minutos
                    };
                    
                    resultsDiv.innerHTML += `
                        <div class="highlight">
                            <h4>🎯 MDT Detalhada:</h4>
                            <table>
                                <tr><td><strong>Símbolo</strong></td><td>${adaptedMDT.symbol}</td></tr>
                                <tr><td><strong>Tipo</strong></td><td>${adaptedMDT.type}</td></tr>
                                <tr><td><strong>Spot Exchange</strong></td><td>${adaptedMDT.spotExchange}</td></tr>
                                <tr><td><strong>Spot Price</strong></td><td>$${adaptedMDT.spotPrice}</td></tr>
                                <tr><td><strong>Futures Exchange</strong></td><td>${adaptedMDT.futuresExchange}</td></tr>
                                <tr><td><strong>Futures Price</strong></td><td>$${adaptedMDT.futuresPrice}</td></tr>
                                <tr><td><strong>Spread</strong></td><td>${Math.abs(adaptedMDT.spreadPercentage).toFixed(3)}%</td></tr>
                                <tr><td><strong>Volume</strong></td><td>${Math.round(adaptedMDT.spotVolume).toLocaleString()}</td></tr>
                                <tr><td><strong>Profitabilidade</strong></td><td>${adaptedMDT.profitability}</td></tr>
                                <tr><td><strong>Data Age</strong></td><td>${adaptedMDT.dataAge}ms</td></tr>
                                <tr><td><strong>Is Valid</strong></td><td>${adaptedMDT.isValid ? '✅ Sim' : '❌ Não'}</td></tr>
                            </table>
                        </div>
                    `;
                    
                    // 3. Testar filtros um por um
                    const filters = {
                        searchTerm: '',
                        spotExchange: 'all',
                        futuresExchange: 'all',
                        type: 'all',
                        profitability: 'all',
                        spreadRange: [0, 100],
                        volumeRange: [0, 10000000],
                        priceRange: [0, 1000000],
                        showOnlyHighVolume: false,
                        showOnlyRecentData: false,
                        hideStaleData: false
                    };
                    
                    const filterTests = [];
                    
                    // Teste 1: Busca
                    if (filters.searchTerm.trim()) {
                        const searchLower = filters.searchTerm.toLowerCase();
                        const passes = adaptedMDT.symbol.toLowerCase().includes(searchLower) ||
                                      adaptedMDT.baseAsset.toLowerCase().includes(searchLower);
                        filterTests.push({name: 'Busca', passes, details: `Termo: "${filters.searchTerm}"`});
                    } else {
                        filterTests.push({name: 'Busca', passes: true, details: 'Sem termo de busca'});
                    }
                    
                    // Teste 2: Spot Exchange
                    const spotExchangePasses = filters.spotExchange === 'all' || adaptedMDT.spotExchange === filters.spotExchange;
                    filterTests.push({name: 'Spot Exchange', passes: spotExchangePasses, details: `Filtro: ${filters.spotExchange}, MDT: ${adaptedMDT.spotExchange}`});
                    
                    // Teste 3: Futures Exchange
                    const futuresExchangePasses = filters.futuresExchange === 'all' || adaptedMDT.futuresExchange === filters.futuresExchange;
                    filterTests.push({name: 'Futures Exchange', passes: futuresExchangePasses, details: `Filtro: ${filters.futuresExchange}, MDT: ${adaptedMDT.futuresExchange}`});
                    
                    // Teste 4: Tipo
                    const typePasses = filters.type === 'all' || adaptedMDT.type === filters.type;
                    filterTests.push({name: 'Tipo', passes: typePasses, details: `Filtro: ${filters.type}, MDT: ${adaptedMDT.type}`});
                    
                    // Teste 5: Rentabilidade
                    const profitabilityPasses = filters.profitability === 'all' || adaptedMDT.profitability === filters.profitability;
                    filterTests.push({name: 'Rentabilidade', passes: profitabilityPasses, details: `Filtro: ${filters.profitability}, MDT: ${adaptedMDT.profitability}`});
                    
                    // Teste 6: Spread Range
                    const absSpread = Math.abs(adaptedMDT.spreadPercentage);
                    const spreadPasses = absSpread >= filters.spreadRange[0] && absSpread <= filters.spreadRange[1];
                    filterTests.push({name: 'Spread Range', passes: spreadPasses, details: `MDT: ${absSpread.toFixed(3)}%, Range: ${filters.spreadRange[0]}-${filters.spreadRange[1]}%`});
                    
                    // Teste 7: Volume Range
                    const minVolume = Math.min(adaptedMDT.spotVolume, adaptedMDT.futuresVolume);
                    const volumePasses = minVolume >= filters.volumeRange[0] && minVolume <= filters.volumeRange[1];
                    filterTests.push({name: 'Volume Range', passes: volumePasses, details: `MDT: ${minVolume.toLocaleString()}, Range: ${filters.volumeRange[0].toLocaleString()}-${filters.volumeRange[1].toLocaleString()}`});
                    
                    // Teste 8: Price Range
                    const avgPrice = (adaptedMDT.spotPrice + adaptedMDT.futuresPrice) / 2;
                    const pricePasses = avgPrice >= filters.priceRange[0] && avgPrice <= filters.priceRange[1];
                    filterTests.push({name: 'Price Range', passes: pricePasses, details: `MDT: $${avgPrice.toFixed(4)}, Range: $${filters.priceRange[0]}-$${filters.priceRange[1].toLocaleString()}`});
                    
                    // Teste 9: Alto Volume
                    if (filters.showOnlyHighVolume) {
                        const volumeThreshold = 50000;
                        const highVolumePasses = minVolume >= volumeThreshold;
                        filterTests.push({name: 'Alto Volume', passes: highVolumePasses, details: `MDT: ${minVolume.toLocaleString()}, Threshold: ${volumeThreshold.toLocaleString()}`});
                    } else {
                        filterTests.push({name: 'Alto Volume', passes: true, details: 'Filtro desabilitado'});
                    }
                    
                    // Teste 10: Dados Recentes
                    if (filters.showOnlyRecentData) {
                        const maxAge = 60000;
                        const recentPasses = adaptedMDT.dataAge <= maxAge;
                        filterTests.push({name: 'Dados Recentes', passes: recentPasses, details: `MDT: ${adaptedMDT.dataAge}ms, Max: ${maxAge}ms`});
                    } else {
                        filterTests.push({name: 'Dados Recentes', passes: true, details: 'Filtro desabilitado'});
                    }
                    
                    // Teste 11: Dados Obsoletos
                    if (filters.hideStaleData) {
                        const stalePasses = adaptedMDT.isValid !== false;
                        filterTests.push({name: 'Dados Obsoletos', passes: stalePasses, details: `MDT isValid: ${adaptedMDT.isValid}`});
                    } else {
                        filterTests.push({name: 'Dados Obsoletos', passes: true, details: 'Filtro desabilitado'});
                    }
                    
                    // Resultado dos testes
                    const allPass = filterTests.every(test => test.passes);
                    const failedTests = filterTests.filter(test => !test.passes);
                    
                    resultsDiv.innerHTML += `
                        <div class="result ${allPass ? 'success' : 'error'}">
                            <h3>${allPass ? '✅' : '❌'} Teste de Filtros</h3>
                            <p><strong>Resultado:</strong> ${allPass ? 'MDT PASSA em todos os filtros' : `MDT FALHA em ${failedTests.length} filtro(s)`}</p>
                            
                            <h4>Detalhes dos Filtros:</h4>
                            <table>
                                <thead>
                                    <tr>
                                        <th>Filtro</th>
                                        <th>Status</th>
                                        <th>Detalhes</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${filterTests.map(test => `
                                        <tr style="background-color: ${test.passes ? '#d4edda' : '#f8d7da'}">
                                            <td>${test.name}</td>
                                            <td>${test.passes ? '✅ PASSA' : '❌ FALHA'}</td>
                                            <td>${test.details}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    `;
                    
                    // Diagnóstico final
                    resultsDiv.innerHTML += `
                        <div class="result ${allPass ? 'success' : 'error'}">
                            <h3>🎯 Diagnóstico Final</h3>
                            ${allPass ? `
                                <p><strong>✅ A MDT DEVERIA APARECER no frontend!</strong></p>
                                <p>Todos os filtros estão passando. Se a MDT não está aparecendo:</p>
                                <ul>
                                    <li>Verifique se há cache antigo no React Query</li>
                                    <li>Verifique se o componente OpportunityTable está renderizando corretamente</li>
                                    <li>Verifique se há erro no componente OpportunityCard</li>
                                    <li>Recarregue a página com Ctrl+F5</li>
                                </ul>
                            ` : `
                                <p><strong>❌ A MDT está sendo FILTRADA!</strong></p>
                                <p><strong>Filtros que estão falhando:</strong></p>
                                <ul>
                                    ${failedTests.map(test => `<li><strong>${test.name}:</strong> ${test.details}</li>`).join('')}
                                </ul>
                                <p><strong>Ação necessária:</strong> Ajustar os filtros padrão no useFilters.ts</p>
                            `}
                        </div>
                    `;
                    
                } else {
                    resultsDiv.innerHTML += `
                        <div class="result error">
                            <h3>❌ MDT não encontrada no backend</h3>
                            <p>A MDT não está sendo retornada pelo backend. Verifique:</p>
                            <ul>
                                <li>Se as exchanges estão coletando dados da MDT</li>
                                <li>Se há erro na coleta de dados</li>
                                <li>Se a MDT está sendo filtrada no backend</li>
                            </ul>
                        </div>
                    `;
                }
                
            } catch (error) {
                console.error('Erro no teste:', error);
                resultsDiv.innerHTML += `
                    <div class="result error">
                        <h3>❌ Erro no Teste</h3>
                        <p><strong>Erro:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
        
        // Executar teste automaticamente
        testMDT();
    </script>
</body>
</html>