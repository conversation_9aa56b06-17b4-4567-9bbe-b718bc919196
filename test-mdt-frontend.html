<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste MDT Frontend</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 15px; border-radius: 8px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        .highlight { background-color: #fff3cd; font-weight: bold; padding: 10px; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>🔍 Teste MDT - Frontend vs Backend</h1>
    <div id="results"></div>

    <script>
        async function testMDTFrontend() {
            const resultsDiv = document.getElementById('results');
            
            try {
                resultsDiv.innerHTML = '<div class="info">🔄 Testando MDT no pipeline completo...</div>';
                
                // 1. Testar backend diretamente
                console.log('🔍 Testando backend...');
                const backendResponse = await fetch('http://localhost:3003/api/arbitrage/opportunities');
                const backendData = await backendResponse.json();
                
                const mdtBackend = backendData.opportunities.filter(opp => opp.symbol === 'MDT/USDT');
                
                resultsDiv.innerHTML = `
                    <div class="result ${mdtBackend.length > 0 ? 'success' : 'error'}">
                        <h3>${mdtBackend.length > 0 ? '✅' : '❌'} Backend Test</h3>
                        <p><strong>Total oportunidades:</strong> ${backendData.opportunities.length}</p>
                        <p><strong>MDT encontradas:</strong> ${mdtBackend.length}</p>
                    </div>
                `;
                
                if (mdtBackend.length > 0) {
                    const mdt = mdtBackend[0];
                    resultsDiv.innerHTML += `
                        <div class="highlight">
                            <h4>🎯 MDT Detalhada (Backend):</h4>
                            <p><strong>Símbolo:</strong> ${mdt.symbol}</p>
                            <p><strong>Tipo:</strong> ${mdt.type}</p>
                            <p><strong>Spot Exchange:</strong> ${mdt.spotExchange} (${mdt.spotPrice})</p>
                            <p><strong>Futures Exchange:</strong> ${mdt.futuresExchange} (${mdt.futuresPrice})</p>
                            <p><strong>Spread:</strong> ${Math.abs(mdt.spreadPercentage).toFixed(3)}%</p>
                            <p><strong>Volume:</strong> ${Math.round(mdt.volume).toLocaleString()}</p>
                            <p><strong>Timestamp:</strong> ${new Date(mdt.timestamp).toLocaleString()}</p>
                        </div>
                    `;
                    
                    // 2. Simular adaptação do ExchangeAPI
                    console.log('🔍 Simulando adaptação...');
                    
                    const adaptedMDT = {
                        id: `${mdt.symbol}-${mdt.spotExchange}-${mdt.futuresExchange}-${Date.now()}`,
                        symbol: mdt.symbol,
                        baseAsset: 'MDT',
                        quoteAsset: 'USDT',
                        spotExchange: mdt.spotExchange,
                        spotPrice: mdt.spotPrice,
                        spotVolume: mdt.volume,
                        futuresExchange: mdt.futuresExchange,
                        futuresPrice: mdt.futuresPrice,
                        futuresVolume: mdt.volume,
                        spreadPercentage: mdt.spreadPercentage,
                        spreadAbsolute: mdt.spread,
                        type: mdt.type === 'spot-futures' ? 'spot-futures-cross' : 'futures-futures-cross',
                        profitability: Math.abs(mdt.spreadPercentage) >= 5.0 ? 'high' : 
                                      Math.abs(mdt.spreadPercentage) >= 1.0 ? 'medium' : 'low',
                        lastUpdate: new Date(mdt.timestamp),
                        dataAge: mdt.dataAge,
                        timestamp: new Date(mdt.timestamp),
                        isValid: Math.abs(mdt.spreadPercentage) > 0.05 && mdt.dataAge < 30000
                    };
                    
                    resultsDiv.innerHTML += `
                        <div class="result success">
                            <h3>✅ Adaptação ExchangeAPI</h3>
                            <p><strong>ID:</strong> ${adaptedMDT.id}</p>
                            <p><strong>Tipo adaptado:</strong> ${adaptedMDT.type}</p>
                            <p><strong>Profitabilidade:</strong> ${adaptedMDT.profitability}</p>
                            <p><strong>Válida:</strong> ${adaptedMDT.isValid ? 'Sim' : 'Não'}</p>
                        </div>
                    `;
                    
                    // 3. Simular filtros
                    console.log('🔍 Simulando filtros...');
                    
                    const filters = {
                        searchTerm: '',
                        spotExchange: 'all',
                        futuresExchange: 'all',
                        type: 'all',
                        profitability: 'all',
                        spreadRange: [0, 100],
                        volumeRange: [0, 10000000],
                        priceRange: [0, 1000000]
                    };
                    
                    // Aplicar filtros
                    let passesFilters = true;
                    const filterResults = [];
                    
                    // Filtro de busca
                    if (filters.searchTerm.trim()) {
                        const searchLower = filters.searchTerm.toLowerCase();
                        const passes = adaptedMDT.symbol.toLowerCase().includes(searchLower) ||
                                      adaptedMDT.baseAsset.toLowerCase().includes(searchLower);
                        filterResults.push(`Busca "${filters.searchTerm}": ${passes ? 'PASSA' : 'FALHA'}`);
                        passesFilters = passesFilters && passes;
                    }
                    
                    // Filtro de exchange
                    if (filters.spotExchange !== 'all') {
                        const passes = adaptedMDT.spotExchange === filters.spotExchange;
                        filterResults.push(`Spot Exchange "${filters.spotExchange}": ${passes ? 'PASSA' : 'FALHA'}`);
                        passesFilters = passesFilters && passes;
                    }
                    
                    if (filters.futuresExchange !== 'all') {
                        const passes = adaptedMDT.futuresExchange === filters.futuresExchange;
                        filterResults.push(`Futures Exchange "${filters.futuresExchange}": ${passes ? 'PASSA' : 'FALHA'}`);
                        passesFilters = passesFilters && passes;
                    }
                    
                    // Filtro de tipo
                    if (filters.type !== 'all') {
                        const passes = adaptedMDT.type === filters.type;
                        filterResults.push(`Tipo "${filters.type}": ${passes ? 'PASSA' : 'FALHA'}`);
                        passesFilters = passesFilters && passes;
                    }
                    
                    // Filtro de rentabilidade
                    if (filters.profitability !== 'all') {
                        const passes = adaptedMDT.profitability === filters.profitability;
                        filterResults.push(`Rentabilidade "${filters.profitability}": ${passes ? 'PASSA' : 'FALHA'}`);
                        passesFilters = passesFilters && passes;
                    }
                    
                    // Filtro de spread
                    const absSpread = Math.abs(adaptedMDT.spreadPercentage);
                    const spreadPasses = absSpread >= filters.spreadRange[0] && absSpread <= filters.spreadRange[1];
                    filterResults.push(`Spread ${absSpread.toFixed(3)}% (range ${filters.spreadRange[0]}-${filters.spreadRange[1]}%): ${spreadPasses ? 'PASSA' : 'FALHA'}`);
                    passesFilters = passesFilters && spreadPasses;
                    
                    // Filtro de volume
                    const minVolume = Math.min(adaptedMDT.spotVolume, adaptedMDT.futuresVolume);
                    const volumePasses = minVolume >= filters.volumeRange[0] && minVolume <= filters.volumeRange[1];
                    filterResults.push(`Volume ${minVolume.toLocaleString()} (range ${filters.volumeRange[0].toLocaleString()}-${filters.volumeRange[1].toLocaleString()}): ${volumePasses ? 'PASSA' : 'FALHA'}`);
                    passesFilters = passesFilters && volumePasses;
                    
                    // Filtro de preço
                    const avgPrice = (adaptedMDT.spotPrice + adaptedMDT.futuresPrice) / 2;
                    const pricePasses = avgPrice >= filters.priceRange[0] && avgPrice <= filters.priceRange[1];
                    filterResults.push(`Preço médio $${avgPrice.toFixed(4)} (range $${filters.priceRange[0]}-$${filters.priceRange[1].toLocaleString()}): ${pricePasses ? 'PASSA' : 'FALHA'}`);
                    passesFilters = passesFilters && pricePasses;
                    
                    resultsDiv.innerHTML += `
                        <div class="result ${passesFilters ? 'success' : 'error'}">
                            <h3>${passesFilters ? '✅' : '❌'} Teste de Filtros</h3>
                            <p><strong>Resultado final:</strong> ${passesFilters ? 'MDT PASSA em todos os filtros' : 'MDT FALHA em algum filtro'}</p>
                            <h4>Detalhes dos filtros:</h4>
                            <ul>
                                ${filterResults.map(result => `<li>${result}</li>`).join('')}
                            </ul>
                        </div>
                    `;
                    
                    // 4. Resultado final
                    resultsDiv.innerHTML += `
                        <div class="result ${passesFilters ? 'success' : 'error'}">
                            <h3>🎯 Diagnóstico Final</h3>
                            ${passesFilters ? `
                                <p><strong>✅ A MDT DEVERIA APARECER no frontend!</strong></p>
                                <p>Se não está aparecendo, o problema pode estar em:</p>
                                <ul>
                                    <li>Componente OpportunityTable não está renderizando</li>
                                    <li>Componente OpportunityCard tem algum erro</li>
                                    <li>Hook useArbitrageData não está funcionando</li>
                                    <li>React Query está com cache antigo</li>
                                </ul>
                                <p><strong>Próximo passo:</strong> Verificar console do navegador no frontend real</p>
                            ` : `
                                <p><strong>❌ A MDT está sendo FILTRADA antes de chegar ao frontend!</strong></p>
                                <p>Verifique os filtros que estão falhando acima.</p>
                                <p><strong>Ação:</strong> Ajustar os filtros padrão no useFilters.ts</p>
                            `}
                        </div>
                    `;
                    
                } else {
                    resultsDiv.innerHTML += `
                        <div class="result error">
                            <h3>❌ MDT não encontrada no backend</h3>
                            <p>Verifique se as exchanges estão coletando dados da MDT corretamente.</p>
                        </div>
                    `;
                }
                
            } catch (error) {
                console.error('Erro no teste:', error);
                resultsDiv.innerHTML += `
                    <div class="result error">
                        <h3>❌ Erro no Teste</h3>
                        <p><strong>Erro:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
        
        // Executar teste automaticamente
        testMDTFrontend();
        
        // Botão para reexecutar
        document.body.innerHTML += '<button onclick="testMDTFrontend()" style="margin: 20px; padding: 10px 20px; font-size: 16px;">🔄 Executar Teste Novamente</button>';
    </script>
</body>
</html>