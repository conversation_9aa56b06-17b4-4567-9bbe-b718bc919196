// Teste simples para verificar parseFloat
const testValues = [
  "0.001099",
  "0,001099", 
  "1.099e-3",
  "0.0011004",
  "0",
  "",
  null,
  undefined
];

console.log("Testando parseFloat:");
testValues.forEach(val => {
  const parsed = parseFloat(val);
  console.log(`"${val}" -> ${parsed} (isNaN: ${isNaN(parsed)}, < 0.000001: ${parsed < 0.000001})`);
});

// Teste com dados reais do MEXC
console.log("\nTestando com dados reais do MEXC:");
fetch('https://api.mexc.com/api/v3/ticker/24hr')
  .then(response => response.json())
  .then(data => {
    const neiro = data.find(ticker => ticker.symbol === 'NEIROUSDT');
    if (neiro) {
      console.log('NEIRO ticker:', JSON.stringify(neiro, null, 2));
      console.log('lastPrice:', neiro.lastPrice, 'parsed:', parseFloat(neiro.lastPrice));
    } else {
      console.log('NEIRO não encontrado');
    }
  })
  .catch(error => console.error('Erro:', error));